<!DOCTYPE html>
{% from "security/_macros.html" import render_field %}
<html lang="en">
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <meta name="description" content="">
    <meta name="author" content="">

    <title>Rezibase Login</title>
    <!-- Bootstrap core CSS-->
    <link href="/static/theme/vendor/bootstrap/css/bootstrap.min.css" rel="stylesheet">

    <!-- Custom fonts for this template-->
    <link href="/static/theme/vendor/fontawesome-free/css/all.min.css" rel="stylesheet" type="text/css">

    <!-- Custom styles for this template-->
    <link href="/static/theme/css/sb-admin.css" rel="stylesheet">
    <link
      rel="preconnect"
      href="https://fonts.googleapis.com"
    />
    <link
      rel="preconnect"
      href="https://fonts.gstatic.com"
      crossorigin
    />
    <link
      href="https://fonts.googleapis.com/css2?family=Figtree:wght@400;500;600;700&display=swap"
      rel="stylesheet"
    />
    <style>
        
        .btn.btn-success {
            color: #ffffff;
            background-color: #DD0081;
            border-color: #DD0081;
        }
    </style>
</head>

<body class="bg-dark" style="font-family: 'Figtree', sans-serif;">

{% if form %}
    {% for field, errors in form.errors.items() %}
        {{ form[field].label }}: {{ ', '.join(errors) }}
    {% endfor %}
{% endif %}

{% with messages = get_flashed_messages(category_filter=["otp"]) %}
    {% if messages %}
        {% for message in messages %}
            <div class="alert alert-danger" role="alert">
                {{ message }}
            </div>
        {% endfor %}
    {% endif %}
{% endwith %}


<div class="container">
    <div class="card card-login mx-auto mt-5">
        <div class="card-header">
            <div class="sixcol er-lockup">
                <img src="{{url_for('static', filename='assets/media/logos/logo-23.png')}}" alt="OTP image">
            </div>
        </div>
        <div class="card-body">
            <div class="text-center">
                <a class="btn btn-success btn-block" href="{{ url_for('auth.otp_view',send='send') }}">Send SMS Code to my device</a>
                <br>
            </div>
            <form action="{{ url_for('auth.otp_view') }}" method="POST" name="otp_user_form" id="otp_user_form">
                <input type="hidden" name="csrf_token" value="{{ csrf_token() }}"/>
                <div class="form-group">
                    {#                     <label>Enter SMS Code</label>#}
                    <div class="input-group">
                        <input type="text" class="form-control" placeholder="Enter SMS Code"
                               aria-label="OTP Code" aria-describedby="basic-addon2" name="otp">
                    </div>
                </div>
                <button class="btn btn-primary btn-block" type="submit">Proceed</button>
            </form>
            <br>
            <a href="{{ url_for('auth.logout_view') }}" class="btn btn-info btn-block" >Logout</a>

        </div>
    </div>
</div>

<!-- Bootstrap core JavaScript-->
<script src="/static/theme/vendor/jquery/jquery.min.js"></script>
<script src="/static/theme/vendor/bootstrap/js/bootstrap.bundle.min.js"></script>

<!-- Core plugin JavaScript-->
<script src="/static/theme/vendor/jquery-easing/jquery.easing.min.js"></script>
<script type="text/javascript">
    $(document).ready(function () {

    });

</script>

</body>

</html>

