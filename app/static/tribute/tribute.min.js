!function(e){if("object"==typeof exports&&"undefined"!=typeof module)module.exports=e();else if("function"==typeof define&&define.amd)define([],e);else{var t;t="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:this,t.Tribute=e()}}(function(){return function(){function e(t,n,i){function r(u,l){if(!n[u]){if(!t[u]){var a="function"==typeof require&&require;if(!l&&a)return a(u,!0);if(o)return o(u,!0);var s=new Error("Cannot find module '"+u+"'");throw s.code="MODULE_NOT_FOUND",s}var c=n[u]={exports:{}};t[u][0].call(c.exports,function(e){var n=t[u][1][e];return r(n||e)},c,c.exports,e,t,n,i)}return n[u].exports}for(var o="function"==typeof require&&require,u=0;u<i.length;u++)r(i[u]);return r}return e}()({1:[function(e,t,n){"use strict";function i(e){return e&&e.__esModule?e:{default:e}}function r(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}Object.defineProperty(n,"__esModule",{value:!0});var o=function(){function e(e,t){for(var n=0;n<t.length;n++){var i=t[n];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,i.key,i)}}return function(t,n,i){return n&&e(t.prototype,n),i&&e(t,i),t}}(),u=e("./utils"),l=(i(u),e("./TributeEvents")),a=i(l),s=e("./TributeMenuEvents"),c=i(s),d=e("./TributeRange"),f=i(d),h=e("./TributeSearch"),v=i(h),p=function(){function e(t){var n=this,i=t.values,o=void 0===i?null:i,u=t.iframe,l=void 0===u?null:u,s=t.selectClass,d=void 0===s?"highlight":s,h=t.trigger,p=void 0===h?"@":h,g=t.selectTemplate,m=void 0===g?null:g,b=t.menuItemTemplate,y=void 0===b?null:b,w=t.lookup,T=void 0===w?"key":w,E=t.fillAttr,C=void 0===E?"value":E,k=t.collection,S=void 0===k?null:k,x=t.menuContainer,A=void 0===x?null:x,M=t.noMatchTemplate,L=void 0===M?null:M,N=t.requireLeadingSpace,I=void 0===N||N,D=t.allowSpaces,P=void 0!==D&&D,O=t.replaceTextSuffix,R=void 0===O?null:O,_=t.positionMenu,H=void 0===_||_;if(r(this,e),this.menuSelected=0,this.current={},this.inputEvent=!1,this.isActive=!1,this.menuContainer=A,this.allowSpaces=P,this.replaceTextSuffix=R,this.positionMenu=H,o)this.collection=[{trigger:p,iframe:l,selectClass:d,selectTemplate:(m||e.defaultSelectTemplate).bind(this),menuItemTemplate:(y||e.defaultMenuItemTemplate).bind(this),noMatchTemplate:function(e){return"function"==typeof e?e.bind(n):L}(L),lookup:T,fillAttr:C,values:o,requireLeadingSpace:I}];else{if(!S)throw new Error("[Tribute] No collection specified.");this.collection=S.map(function(t){return{trigger:t.trigger||p,iframe:t.iframe||l,selectClass:t.selectClass||d,selectTemplate:(t.selectTemplate||e.defaultSelectTemplate).bind(n),menuItemTemplate:(t.menuItemTemplate||e.defaultMenuItemTemplate).bind(n),noMatchTemplate:function(e){return"function"==typeof e?e.bind(n):null}(L),lookup:t.lookup||T,fillAttr:t.fillAttr||C,values:t.values,requireLeadingSpace:t.requireLeadingSpace}})}new f.default(this),new a.default(this),new c.default(this),new v.default(this)}return o(e,[{key:"triggers",value:function(){return this.collection.map(function(e){return e.trigger})}},{key:"attach",value:function(e){if(!e)throw new Error("[Tribute] Must pass in a DOM node or NodeList.");if("undefined"!=typeof jQuery&&e instanceof jQuery&&(e=e.get()),e.constructor===NodeList||e.constructor===HTMLCollection||e.constructor===Array)for(var t=e.length,n=0;n<t;++n)this._attach(e[n]);else this._attach(e)}},{key:"_attach",value:function(e){e.hasAttribute("data-tribute")&&console.warn("Tribute was already bound to "+e.nodeName),this.ensureEditable(e),this.events.bind(e),e.setAttribute("data-tribute",!0)}},{key:"ensureEditable",value:function(t){if(e.inputTypes().indexOf(t.nodeName)===-1){if(!t.contentEditable)throw new Error("[Tribute] Cannot bind to "+t.nodeName);t.contentEditable=!0}}},{key:"createMenu",value:function(){var e=this.range.getDocument().createElement("div"),t=this.range.getDocument().createElement("ul");return e.className="tribute-container",e.appendChild(t),this.menuContainer?this.menuContainer.appendChild(e):this.range.getDocument().body.appendChild(e)}},{key:"showMenuFor",value:function(e,t){var n=this;if(!this.isActive||this.current.element!==e||this.current.mentionText!==this.currentMentionTextSnapshot){this.currentMentionTextSnapshot=this.current.mentionText,this.menu||(this.menu=this.createMenu(),e.tributeMenu=this.menu,this.menuEvents.bind(this.menu)),this.isActive=!0,this.menuSelected=0,this.current.mentionText||(this.current.mentionText="");var i=function(e){if(n.isActive){var i=n.search.filter(n.current.mentionText,e,{pre:"<span>",post:"</span>",extract:function(e){if("string"==typeof n.current.collection.lookup)return e[n.current.collection.lookup];if("function"==typeof n.current.collection.lookup)return n.current.collection.lookup(e);throw new Error("Invalid lookup attribute, lookup must be string or function.")}});n.current.filteredItems=i;var r=n.menu.querySelector("ul");if(n.range.positionMenuAtCaret(t),!i.length){var o=new CustomEvent("tribute-no-match",{detail:n.menu});return n.current.element.dispatchEvent(o),void(n.current.collection.noMatchTemplate?r.innerHTML=n.current.collection.noMatchTemplate():n.hideMenu())}r.innerHTML="",i.forEach(function(e,t){var i=n.range.getDocument().createElement("li");i.setAttribute("data-index",t),i.addEventListener("mouseenter",function(e){var t=e.target,i=t.getAttribute("data-index");n.events.setActiveLi(i)}),n.menuSelected===t&&(i.className=n.current.collection.selectClass),i.innerHTML=n.current.collection.menuItemTemplate(e),r.appendChild(i)})}};"function"==typeof this.current.collection.values?this.current.collection.values(this.current.mentionText,i):i(this.current.collection.values)}}},{key:"showMenuForCollection",value:function(e,t){e!==document.activeElement&&this.placeCaretAtEnd(e),this.current.collection=this.collection[t||0],this.current.externalTrigger=!0,this.current.element=e,e.isContentEditable?this.insertTextAtCursor(this.current.collection.trigger):this.insertAtCaret(e,this.current.collection.trigger),this.showMenuFor(e)}},{key:"placeCaretAtEnd",value:function(e){if(e.focus(),"undefined"!=typeof window.getSelection&&"undefined"!=typeof document.createRange){var t=document.createRange();t.selectNodeContents(e),t.collapse(!1);var n=window.getSelection();n.removeAllRanges(),n.addRange(t)}else if("undefined"!=typeof document.body.createTextRange){var i=document.body.createTextRange();i.moveToElementText(e),i.collapse(!1),i.select()}}},{key:"insertTextAtCursor",value:function(e){var t,n;t=window.getSelection(),n=t.getRangeAt(0),n.deleteContents();var i=document.createTextNode(e);n.insertNode(i),n.selectNodeContents(i),n.collapse(!1),t.removeAllRanges(),t.addRange(n)}},{key:"insertAtCaret",value:function(e,t){var n=e.scrollTop,i=e.selectionStart,r=e.value.substring(0,i),o=e.value.substring(e.selectionEnd,e.value.length);e.value=r+t+o,i+=t.length,e.selectionStart=i,e.selectionEnd=i,e.focus(),e.scrollTop=n}},{key:"hideMenu",value:function(){this.menu&&(this.menu.style.cssText="display: none;",this.isActive=!1,this.menuSelected=0,this.current={})}},{key:"selectItemAtIndex",value:function(e,t){if(e=parseInt(e),"number"==typeof e){var n=this.current.filteredItems[e],i=this.current.collection.selectTemplate(n);null!==i&&this.replaceText(i,t,n)}}},{key:"replaceText",value:function(e,t,n){this.range.replaceTriggerText(e,!0,!0,t,n)}},{key:"_append",value:function(e,t,n){if("function"==typeof e.values)throw new Error("Unable to append to values, as it is a function.");n?e.values=t:e.values=e.values.concat(t)}},{key:"append",value:function(e,t,n){var i=parseInt(e);if("number"!=typeof i)throw new Error("please provide an index for the collection to update.");var r=this.collection[i];this._append(r,t,n)}},{key:"appendCurrent",value:function(e,t){if(!this.isActive)throw new Error("No active state. Please use append instead and pass an index.");this._append(this.current.collection,e,t)}},{key:"detach",value:function(e){if(!e)throw new Error("[Tribute] Must pass in a DOM node or NodeList.");if("undefined"!=typeof jQuery&&e instanceof jQuery&&(e=e.get()),e.constructor===NodeList||e.constructor===HTMLCollection||e.constructor===Array)for(var t=e.length,n=0;n<t;++n)this._detach(e[n]);else this._detach(e)}},{key:"_detach",value:function(e){var t=this;this.events.unbind(e),e.tributeMenu&&this.menuEvents.unbind(e.tributeMenu),setTimeout(function(){e.removeAttribute("data-tribute"),t.isActive=!1,e.tributeMenu&&e.tributeMenu.remove()})}}],[{key:"defaultSelectTemplate",value:function(e){return"undefined"==typeof e?null:this.range.isContentEditable(this.current.element)?'<span class="tribute-mention">'+(this.current.collection.trigger+e.original[this.current.collection.fillAttr])+"</span>":this.current.collection.trigger+e.original[this.current.collection.fillAttr]}},{key:"defaultMenuItemTemplate",value:function(e){return e.string}},{key:"inputTypes",value:function(){return["TEXTAREA","INPUT"]}}]),e}();n.default=p,t.exports=n.default},{"./TributeEvents":2,"./TributeMenuEvents":3,"./TributeRange":4,"./TributeSearch":5,"./utils":7}],2:[function(e,t,n){"use strict";function i(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}Object.defineProperty(n,"__esModule",{value:!0});var r=function(){function e(e,t){for(var n=0;n<t.length;n++){var i=t[n];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,i.key,i)}}return function(t,n,i){return n&&e(t.prototype,n),i&&e(t,i),t}}(),o=function(){function e(t){i(this,e),this.tribute=t,this.tribute.events=this}return r(e,[{key:"bind",value:function(e){e.boundKeydown=this.keydown.bind(e,this),e.boundKeyup=this.keyup.bind(e,this),e.boundInput=this.input.bind(e,this),e.addEventListener("keydown",e.boundKeydown,!1),e.addEventListener("keyup",e.boundKeyup,!1),e.addEventListener("input",e.boundInput,!1)}},{key:"unbind",value:function(e){e.removeEventListener("keydown",e.boundKeydown,!1),e.removeEventListener("keyup",e.boundKeyup,!1),e.removeEventListener("input",e.boundInput,!1),delete e.boundKeydown,delete e.boundKeyup,delete e.boundInput}},{key:"keydown",value:function(t,n){t.shouldDeactivate(n)&&(t.tribute.isActive=!1,t.tribute.hideMenu());var i=this;t.commandEvent=!1,e.keys().forEach(function(e){e.key===n.keyCode&&(t.commandEvent=!0,t.callbacks()[e.value.toLowerCase()](n,i))})}},{key:"input",value:function(e,t){e.inputEvent=!0,e.keyup.call(this,e,t)}},{key:"click",value:function(e,t){var n=e.tribute;if(n.menu&&n.menu.contains(t.target)){var i=t.target;for(t.preventDefault(),t.stopPropagation();"li"!==i.nodeName.toLowerCase();)if(i=i.parentNode,!i||i===n.menu)throw new Error("cannot find the <li> container for the click");n.selectItemAtIndex(i.getAttribute("data-index"),t),n.hideMenu()}else n.current.element&&!n.current.externalTrigger&&(n.current.externalTrigger=!1,setTimeout(function(){return n.hideMenu()}))}},{key:"keyup",value:function(e,t){if(e.inputEvent&&(e.inputEvent=!1),e.updateSelection(this),27!==t.keyCode){if(!e.tribute.isActive){var n=e.getKeyCode(e,this,t);if(isNaN(n)||!n)return;var i=e.tribute.triggers().find(function(e){return e.charCodeAt(0)===n});"undefined"!=typeof i&&e.callbacks().triggerChar(t,this,i)}(e.tribute.current.trigger&&e.commandEvent===!1||e.tribute.isActive&&8===t.keyCode)&&e.tribute.showMenuFor(this,!0)}}},{key:"shouldDeactivate",value:function(t){if(!this.tribute.isActive)return!1;if(0===this.tribute.current.mentionText.length){var n=!1;return e.keys().forEach(function(e){t.keyCode===e.key&&(n=!0)}),!n}return!1}},{key:"getKeyCode",value:function(e,t,n){var i=e.tribute,r=i.range.getTriggerInfo(!1,!1,!0,i.allowSpaces);return!!r&&r.mentionTriggerChar.charCodeAt(0)}},{key:"updateSelection",value:function(e){this.tribute.current.element=e;var t=this.tribute.range.getTriggerInfo(!1,!1,!0,this.tribute.allowSpaces);t&&(this.tribute.current.selectedPath=t.mentionSelectedPath,this.tribute.current.mentionText=t.mentionText,this.tribute.current.selectedOffset=t.mentionSelectedOffset)}},{key:"callbacks",value:function(){var e=this;return{triggerChar:function(t,n,i){var r=e.tribute;r.current.trigger=i;var o=r.collection.find(function(e){return e.trigger===i});r.current.collection=o,r.inputEvent&&r.showMenuFor(n,!0)},enter:function(t,n){e.tribute.isActive&&(t.preventDefault(),t.stopPropagation(),setTimeout(function(){e.tribute.selectItemAtIndex(e.tribute.menuSelected,t),e.tribute.hideMenu()},0))},escape:function(t,n){e.tribute.isActive&&(t.preventDefault(),t.stopPropagation(),e.tribute.isActive=!1,e.tribute.hideMenu())},tab:function(t,n){e.callbacks().enter(t,n)},up:function(t,n){if(e.tribute.isActive){t.preventDefault(),t.stopPropagation();var i=e.tribute.current.filteredItems.length,r=e.tribute.menuSelected;i>r&&r>0?(e.tribute.menuSelected--,e.setActiveLi()):0===r&&(e.tribute.menuSelected=i-1,e.setActiveLi(),e.tribute.menu.scrollTop=e.tribute.menu.scrollHeight)}},down:function(t,n){if(e.tribute.isActive){t.preventDefault(),t.stopPropagation();var i=e.tribute.current.filteredItems.length-1,r=e.tribute.menuSelected;i>r?(e.tribute.menuSelected++,e.setActiveLi()):i===r&&(e.tribute.menuSelected=0,e.setActiveLi(),e.tribute.menu.scrollTop=0)}},delete:function(t,n){e.tribute.isActive&&e.tribute.current.mentionText.length<1?e.tribute.hideMenu():e.tribute.isActive&&e.tribute.showMenuFor(n)}}}},{key:"setActiveLi",value:function(e){var t=this.tribute.menu.querySelectorAll("li"),n=t.length>>>0,i=this.getFullHeight(this.tribute.menu),r=this.getFullHeight(t[0]);e&&(this.tribute.menuSelected=e);for(var o=0;o<n;o++){var u=t[o];if(o===this.tribute.menuSelected){var l=r*(o+1),a=this.tribute.menu.scrollTop,s=a+i;l>s?this.tribute.menu.scrollTop+=r:l<s&&(this.tribute.menu.scrollTop-=r),u.className=this.tribute.current.collection.selectClass}else u.className=""}}},{key:"getFullHeight",value:function(e,t){var n=e.getBoundingClientRect().height;if(t){var i=e.currentStyle||window.getComputedStyle(e);return n+parseFloat(i.marginTop)+parseFloat(i.marginBottom)}return n}}],[{key:"keys",value:function(){return[{key:9,value:"TAB"},{key:8,value:"DELETE"},{key:13,value:"ENTER"},{key:27,value:"ESCAPE"},{key:38,value:"UP"},{key:40,value:"DOWN"}]}}]),e}();n.default=o,t.exports=n.default},{}],3:[function(e,t,n){"use strict";function i(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}Object.defineProperty(n,"__esModule",{value:!0});var r=function(){function e(e,t){for(var n=0;n<t.length;n++){var i=t[n];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,i.key,i)}}return function(t,n,i){return n&&e(t.prototype,n),i&&e(t,i),t}}(),o=function(){function e(t){i(this,e),this.tribute=t,this.tribute.menuEvents=this,this.menu=this.tribute.menu}return r(e,[{key:"bind",value:function(e){var t=this;e.menuKeydownEvent=this.tribute.events.keydown.bind(this.menu,this),this.menuClickEvent=this.tribute.events.click.bind(null,this),this.menuContainerScrollEvent=this.debounce(function(){t.tribute.isActive&&t.tribute.showMenuFor(t.tribute.current.element,!1)},300,!1),this.windowResizeEvent=this.debounce(function(){t.tribute.isActive&&t.tribute.range.positionMenuAtCaret(!0)},300,!1),this.tribute.range.getDocument().addEventListener("MSPointerDown",this.menuKeydownEvent,!1),e.addEventListener("keydown",this.menuKeydownEvent,!1),this.tribute.range.getDocument().addEventListener("mousedown",this.menuClickEvent,!1),window.addEventListener("resize",this.windowResizeEvent),this.menuContainer?this.menuContainer.addEventListener("scroll",this.menuContainerScrollEvent,!1):window.addEventListener("scroll",this.menuContainerScrollEvent)}},{key:"unbind",value:function(e){e.removeEventListener("keydown",e.menuKeydownEvent,!1),delete e.menuKeydownEvent,this.tribute.range.getDocument().removeEventListener("mousedown",this.menuClickEvent,!1),this.tribute.range.getDocument().removeEventListener("MSPointerDown",this.menuClickEvent,!1),window.removeEventListener("resize",this.windowResizeEvent),this.menuContainer?this.menuContainer.removeEventListener("scroll",this.menuContainerScrollEvent,!1):window.removeEventListener("scroll",this.menuContainerScrollEvent)}},{key:"debounce",value:function(e,t,n){var i,r=this,o=arguments;return function(){var u=r,l=o,a=function(){i=null,n||e.apply(u,l)},s=n&&!i;clearTimeout(i),i=setTimeout(a,t),s&&e.apply(u,l)}}}]),e}();n.default=o,t.exports=n.default},{}],4:[function(e,t,n){"use strict";function i(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}Object.defineProperty(n,"__esModule",{value:!0});var r=function(){function e(e,t){for(var n=0;n<t.length;n++){var i=t[n];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,i.key,i)}}return function(t,n,i){return n&&e(t.prototype,n),i&&e(t,i),t}}(),o=function(){function e(t){i(this,e),this.tribute=t,this.tribute.range=this}return r(e,[{key:"getDocument",value:function(){var e=void 0;return this.tribute.current.collection&&(e=this.tribute.current.collection.iframe),e?e.contentWindow.document:document}},{key:"positionMenuAtCaret",value:function(e){var t=this,n=this.tribute.current,i=void 0,r=this.getTriggerInfo(!1,!1,!0,this.tribute.allowSpaces);if("undefined"!=typeof r){if(!this.tribute.positionMenu)return void(this.tribute.menu.style.cssText="display: block;");i=this.isContentEditable(n.element)?this.getContentEditableCaretPosition(r.mentionPosition):this.getTextAreaOrInputUnderlinePosition(this.getDocument().activeElement,r.mentionPosition),this.tribute.menu.style.cssText="top: "+i.top+"px;\n                                     left: "+i.left+"px;\n                                     right: "+i.right+"px;\n                                     bottom: "+i.bottom+"px;\n                                     position: absolute;\n                                     zIndex: 10000;\n                                     display: block;","auto"===i.left&&(this.tribute.menu.style.left="auto"),"auto"===i.top&&(this.tribute.menu.style.top="auto"),e&&this.scrollIntoView(),window.setTimeout(function(){var n={width:t.tribute.menu.offsetWidth,height:t.tribute.menu.offsetHeight},r=t.isMenuOffScreen(i,n);(r.horizontally||r.vertically)&&(t.tribute.menu.style.cssText="display: none",t.positionMenuAtCaret(e))},0)}else this.tribute.menu.style.cssText="display: none"}},{key:"selectElement",value:function(e,t,n){var i=void 0,r=e;if(t)for(var o=0;o<t.length;o++){if(r=r.childNodes[t[o]],void 0===r)return;for(;r.length<n;)n-=r.length,r=r.nextSibling;0!==r.childNodes.length||r.length||(r=r.previousSibling)}var u=this.getWindowSelection();i=this.getDocument().createRange(),i.setStart(r,n),i.setEnd(r,n),i.collapse(!0);try{u.removeAllRanges()}catch(e){}u.addRange(i),e.focus()}},{key:"resetSelection",value:function(e,t,n){this.isContentEditable(e)?this.selectElement(e,t,n):e!==this.getDocument().activeElement&&e.focus()}},{key:"replaceTriggerText",value:function(e,t,n,i,r){var o=this.tribute.current,u=this.getTriggerInfo(!0,n,t,this.tribute.allowSpaces),l=new CustomEvent("tribute-replaced",{detail:{item:r,event:i}});if(void 0!==u){if(this.isContentEditable(o.element)){var a="string"==typeof this.tribute.replaceTextSuffix?this.tribute.replaceTextSuffix:" ";e+=a,this.pasteHtml(e,u.mentionPosition,u.mentionPosition+u.mentionText.length+1)}else{var s=this.getDocument().activeElement,c="string"==typeof this.tribute.replaceTextSuffix?this.tribute.replaceTextSuffix:" ";e+=c;var d=u.mentionPosition,f=u.mentionPosition+u.mentionText.length+c.length;s.value=s.value.substring(0,d)+e+s.value.substring(f,s.value.length),s.selectionStart=d+e.length,s.selectionEnd=d+e.length}o.element.dispatchEvent(l)}}},{key:"pasteHtml",value:function(e,t,n){var i=void 0,r=void 0;r=this.getWindowSelection(),i=this.getDocument().createRange(),i.setStart(r.anchorNode,t),i.setEnd(r.anchorNode,n),i.deleteContents();var o=this.getDocument().createElement("div");o.innerHTML=e;for(var u=this.getDocument().createDocumentFragment(),l=void 0,a=void 0;l=o.firstChild;)a=u.appendChild(l);i.insertNode(u),a&&(i=i.cloneRange(),i.setStartAfter(a),i.collapse(!0),r.removeAllRanges(),r.addRange(i))}},{key:"getWindowSelection",value:function(){return this.tribute.collection.iframe?this.tribute.collection.iframe.contentWindow.getSelection():window.getSelection()}},{key:"getNodePositionInParent",value:function(e){if(null===e.parentNode)return 0;for(var t=0;t<e.parentNode.childNodes.length;t++){var n=e.parentNode.childNodes[t];if(n===e)return t}}},{key:"getContentEditableSelectedPath",value:function(e){var t=this.getWindowSelection(),n=t.anchorNode,i=[],r=void 0;if(null!=n){for(var o=void 0,u=n.contentEditable;null!==n&&"true"!==u;)o=this.getNodePositionInParent(n),i.push(o),n=n.parentNode,null!==n&&(u=n.contentEditable);return i.reverse(),r=t.getRangeAt(0).startOffset,{selected:n,path:i,offset:r}}}},{key:"getTextPrecedingCurrentSelection",value:function(){var e=this.tribute.current,t="";if(this.isContentEditable(e.element)){var n=this.getWindowSelection().anchorNode;if(null!=n){var i=n.textContent,r=this.getWindowSelection().getRangeAt(0).startOffset;i&&r>=0&&(t=i.substring(0,r))}}else{var o=this.tribute.current.element;if(o){var u=o.selectionStart;o.value&&u>=0&&(t=o.value.substring(0,u))}}return t}},{key:"getTriggerInfo",value:function(e,t,n,i){var r=this,o=this.tribute.current,u=void 0,l=void 0,a=void 0;if(this.isContentEditable(o.element)){var s=this.getContentEditableSelectedPath(o);s&&(u=s.selected,l=s.path,a=s.offset)}else u=this.getDocument().activeElement;var c=this.getTextPrecedingCurrentSelection();if(void 0!==c&&null!==c){var d=-1,f=void 0;if(this.tribute.collection.forEach(function(e){var t=e.trigger,i=e.requireLeadingSpace?r.lastIndexWithLeadingSpace(c,t):c.lastIndexOf(t);i>d&&(d=i,f=t,n=e.requireLeadingSpace)}),d>=0&&(0===d||!n||/[\xA0\s]/g.test(c.substring(d-1,d)))){var h=c.substring(d+1,c.length);f=c.substring(d,d+1);var v=h.substring(0,1),p=h.length>0&&(" "===v||" "===v);t&&(h=h.trim());var g=i?/[^\S ]/g:/[\xA0\s]/g;if(!p&&(e||!g.test(h)))return{mentionPosition:d,mentionText:h,mentionSelectedElement:u,mentionSelectedPath:l,mentionSelectedOffset:a,mentionTriggerChar:f}}}}},{key:"lastIndexWithLeadingSpace",value:function(e,t){for(var n=e.split("").reverse().join(""),i=-1,r=0,o=e.length;r<o;r++){var u=r===e.length-1,l=/\s/.test(n[r+1]),a=t===n[r];if(a&&(u||l)){i=e.length-1-r;break}}return i}},{key:"isContentEditable",value:function(e){return"INPUT"!==e.nodeName&&"TEXTAREA"!==e.nodeName}},{key:"isMenuOffScreen",value:function(e,t){var n=t.width+e.left,i=t.height+e.top,r=window.innerWidth,o=window.innerHeight,u=document.documentElement,l=(window.pageXOffset||u.scrollLeft)-(u.clientLeft||0),a=(window.pageYOffset||u.scrollTop)-(u.clientTop||0);return{horizontally:Math.ceil(n-l)>=r,vertically:Math.ceil(i-a)>=o}}},{key:"getMenuDimensions",value:function(){var e={width:null,height:null};return this.tribute.menu.style.cssText="top: 0px;\n                                 left: 0px;\n                                 position: fixed;\n                                 zIndex: 10000;\n                                 display: block;\n                                 visibility; hidden;",e.width=this.tribute.menu.offsetWidth,e.height=this.tribute.menu.offsetHeight,this.tribute.menu.style.cssText="display: none;",e}},{key:"getTextAreaOrInputUnderlinePosition",value:function(e,t,n){var i=["direction","boxSizing","width","height","overflowX","overflowY","borderTopWidth","borderRightWidth","borderBottomWidth","borderLeftWidth","paddingTop","paddingRight","paddingBottom","paddingLeft","fontStyle","fontVariant","fontWeight","fontStretch","fontSize","fontSizeAdjust","lineHeight","fontFamily","textAlign","textTransform","textIndent","textDecoration","letterSpacing","wordSpacing"],r=null!==window.mozInnerScreenX,o=this.getDocument().createElement("div");o.id="input-textarea-caret-position-mirror-div",this.getDocument().body.appendChild(o);var u=o.style,l=window.getComputedStyle?getComputedStyle(e):e.currentStyle;u.whiteSpace="pre-wrap","INPUT"!==e.nodeName&&(u.wordWrap="break-word"),u.position="absolute",u.visibility="hidden",i.forEach(function(e){u[e]=l[e]}),r?(u.width=parseInt(l.width)-2+"px",e.scrollHeight>parseInt(l.height)&&(u.overflowY="scroll")):u.overflow="hidden",o.textContent=e.value.substring(0,t),"INPUT"===e.nodeName&&(o.textContent=o.textContent.replace(/\s/g," "));var a=this.getDocument().createElement("span");a.textContent=e.value.substring(t)||".",o.appendChild(a);var s=e.getBoundingClientRect(),c=document.documentElement,d=(window.pageXOffset||c.scrollLeft)-(c.clientLeft||0),f=(window.pageYOffset||c.scrollTop)-(c.clientTop||0),h={top:s.top+f+a.offsetTop+parseInt(l.borderTopWidth)+parseInt(l.fontSize)-e.scrollTop,left:s.left+d+a.offsetLeft+parseInt(l.borderLeftWidth)},v=window.innerWidth,p=window.innerHeight,g=this.getMenuDimensions(),m=this.isMenuOffScreen(h,g);m.horizontally&&(h.right=v-h.left,h.left="auto");var b=this.tribute.menuContainer?this.tribute.menuContainer.offsetHeight:this.getDocument().body.offsetHeight;if(m.vertically){var y=this.tribute.menuContainer?this.tribute.menuContainer.getBoundingClientRect():this.getDocument().body.getBoundingClientRect(),w=b-(p-y.top);h.bottom=w+(p-s.top-a.offsetTop),h.top="auto"}return this.getDocument().body.removeChild(o),h}},{key:"getContentEditableCaretPosition",value:function(e){var t="\ufeff",n=void 0,i="sel_"+(new Date).getTime()+"_"+Math.random().toString().substr(2),r=void 0,o=this.getWindowSelection(),u=o.getRangeAt(0);r=this.getDocument().createRange(),r.setStart(o.anchorNode,e),r.setEnd(o.anchorNode,e),r.collapse(!1),n=this.getDocument().createElement("span"),n.id=i,n.appendChild(this.getDocument().createTextNode(t)),r.insertNode(n),o.removeAllRanges(),o.addRange(u);var l=n.getBoundingClientRect(),a=document.documentElement,s=(window.pageXOffset||a.scrollLeft)-(a.clientLeft||0),c=(window.pageYOffset||a.scrollTop)-(a.clientTop||0),d={left:l.left+s,top:l.top+n.offsetHeight+c},f=window.innerWidth,h=window.innerHeight,v=this.getMenuDimensions(),p=this.isMenuOffScreen(d,v);p.horizontally&&(d.left="auto",d.right=f-l.left-s);var g=this.tribute.menuContainer?this.tribute.menuContainer.offsetHeight:this.getDocument().body.offsetHeight;if(p.vertically){var m=this.tribute.menuContainer?this.tribute.menuContainer.getBoundingClientRect():this.getDocument().body.getBoundingClientRect(),b=g-(h-m.top);s=(window.pageXOffset||a.scrollLeft)-(a.clientLeft||0),c=(window.pageYOffset||a.scrollTop)-(a.clientTop||0),d.top="auto",d.bottom=b+(h-l.top)}return n.parentNode.removeChild(n),d}},{key:"scrollIntoView",value:function(e){var t=20,n=void 0,i=100,r=this.menu;if("undefined"!=typeof r){for(;void 0===n||0===n.height;)if(n=r.getBoundingClientRect(),0===n.height&&(r=r.childNodes[0],void 0===r||!r.getBoundingClientRect))return;var o=n.top,u=o+n.height;if(o<0)window.scrollTo(0,window.pageYOffset+n.top-t);else if(u>window.innerHeight){var l=window.pageYOffset+n.top-t;l-window.pageYOffset>i&&(l=window.pageYOffset+i);var a=window.pageYOffset-(window.innerHeight-u);a>l&&(a=l),window.scrollTo(0,a)}}}}]),e}();n.default=o,t.exports=n.default},{}],5:[function(e,t,n){"use strict";function i(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}Object.defineProperty(n,"__esModule",{value:!0});var r=function(){function e(e,t){for(var n=0;n<t.length;n++){var i=t[n];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,i.key,i)}}return function(t,n,i){return n&&e(t.prototype,n),i&&e(t,i),t}}(),o=function(){function e(t){i(this,e),this.tribute=t,this.tribute.search=this}return r(e,[{key:"simpleFilter",value:function(e,t){var n=this;return t.filter(function(t){return n.test(e,t)})}},{key:"test",value:function(e,t){return null!==this.match(e,t)}},{key:"match",value:function(e,t,n){n=n||{};var i=(t.length,n.pre||""),r=n.post||"",o=n.caseSensitive&&t||t.toLowerCase();e=n.caseSensitive&&e||e.toLowerCase();var u=this.traverse(o,e,0,0,[]);return u?{rendered:this.render(t,u.cache,i,r),score:u.score}:null}},{key:"traverse",value:function(e,t,n,i,r){if(t.length===i)return{score:this.calculateScore(r),cache:r.slice()};if(!(e.length===n||t.length-i>e.length-n)){for(var o=t[i],u=e.indexOf(o,n),l=void 0,a=void 0;u>-1;){if(r.push(u),a=this.traverse(e,t,u+1,i+1,r),r.pop(),!a)return l;(!l||l.score<a.score)&&(l=a),u=e.indexOf(o,u+1)}return l}}},{key:"calculateScore",value:function(e){var t=0,n=1;return e.forEach(function(i,r){r>0&&(e[r-1]+1===i?n+=n+1:n=1),t+=n}),t}},{key:"render",value:function(e,t,n,i){var r=e.substring(0,t[0]);return t.forEach(function(o,u){r+=n+e[o]+i+e.substring(o+1,t[u+1]?t[u+1]:e.length)}),r}},{key:"filter",value:function(e,t,n){var i=this;return n=n||{},t.reduce(function(t,r,o,u){var l=r;n.extract&&(l=n.extract(r),l||(l=""));var a=i.match(e,l,n);return null!=a&&(t[t.length]={string:a.rendered,score:a.score,index:o,original:r}),t},[]).sort(function(e,t){var n=t.score-e.score;return n?n:e.index-t.index})}}]),e}();n.default=o,t.exports=n.default},{}],6:[function(e,t,n){"use strict";function i(e){return e&&e.__esModule?e:{default:e}}Object.defineProperty(n,"__esModule",{value:!0});var r=e("./Tribute"),o=i(r);n.default=o.default,t.exports=n.default},{"./Tribute":1}],7:[function(e,t,n){"use strict";if(Array.prototype.find||(Array.prototype.find=function(e){if(null===this)throw new TypeError("Array.prototype.find called on null or undefined");if("function"!=typeof e)throw new TypeError("predicate must be a function");for(var t,n=Object(this),i=n.length>>>0,r=arguments[1],o=0;o<i;o++)if(t=n[o],e.call(r,t,o,n))return t}),window&&"function"!=typeof window.CustomEvent){var i=function(e,t){t=t||{bubbles:!1,cancelable:!1,detail:void 0};var n=document.createEvent("CustomEvent");return n.initCustomEvent(e,t.bubbles,t.cancelable,t.detail),n};"undefined"!=typeof window.Event&&(i.prototype=window.Event.prototype),window.CustomEvent=i}},{}]},{},[6])(6)});
//# sourceMappingURL=tribute.min.js.map