/*! TreeView's high contrast theme wise override definitions and variables */
.e-treeview .e-list-item div.e-icons.interaction {
  -webkit-transition: -webkit-transform .3s ease-in-out;
  border-radius: 15px;
  transition: transform .3s ease-in-out;
}

.e-treeview .e-list-item .e-icons.e-icon-collapsible {
  transform: rotate(90deg);
}

.e-treeview.e-drag-item.e-rtl .e-icons.e-drop-next {
  transform: rotate(180deg);
}

.e-treeview.e-rtl div.e-icons {
  transform: rotate(180deg);
}

/*! TreeView icons */
.e-treeview .e-list-item div.e-icons::before {
  content: '\e22e';
}

.e-treeview .e-sibling::before {
  content: '';
}

.e-treeview .e-popup .e-icons::before {
  content: '\e930';
}

.e-treeview.e-drag-item .e-icons.e-drop-in::before {
  content: '\e22c';
}

.e-treeview.e-drag-item .e-icons.e-drop-out::before {
  content: '\e22b';
}

.e-treeview.e-drag-item .e-icons.e-drop-next::before {
  content: '\e22d';
}

.e-treeview.e-drag-item .e-icons.e-no-drop::before {
  content: '\e22a';
}

@keyframes rotation {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(359deg);
  }
}

.e-bigger .e-treeview .e-fullrow,
.e-treeview.e-bigger .e-fullrow {
  height: 40px;
}

.e-bigger .e-treeview .e-list-text,
.e-treeview.e-bigger .e-list-text {
  line-height: 38px;
  min-height: 38px;
  padding: 0 10px;
}

.e-bigger .e-treeview .e-list-text .e-input-group,
.e-treeview.e-bigger .e-list-text .e-input-group {
  height: 38px;
}

.e-bigger .e-treeview .e-list-text .e-input-group .e-input,
.e-treeview.e-bigger .e-list-text .e-input-group .e-input {
  height: 36px;
}

.e-bigger .e-treeview .e-checkbox-wrapper,
.e-treeview.e-bigger .e-checkbox-wrapper {
  margin: 0 0 0 10px;
}

.e-bigger .e-treeview .e-checkbox-wrapper + .e-list-icon,
.e-bigger .e-treeview .e-checkbox-wrapper + .e-list-img,
.e-treeview.e-bigger .e-checkbox-wrapper + .e-list-icon,
.e-treeview.e-bigger .e-checkbox-wrapper + .e-list-img {
  margin: 0 0 0 16px;
}

.e-bigger .e-treeview .e-list-icon,
.e-bigger .e-treeview .e-list-img,
.e-treeview.e-bigger .e-list-icon,
.e-treeview.e-bigger .e-list-img {
  margin: 0 0 0 10px;
}

.e-bigger .e-treeview .e-list-icon + .e-list-icon,
.e-bigger .e-treeview .e-list-icon + .e-list-img,
.e-bigger .e-treeview .e-list-img + .e-list-icon,
.e-bigger .e-treeview .e-list-img + .e-list-img,
.e-treeview.e-bigger .e-list-icon + .e-list-icon,
.e-treeview.e-bigger .e-list-icon + .e-list-img,
.e-treeview.e-bigger .e-list-img + .e-list-icon,
.e-treeview.e-bigger .e-list-img + .e-list-img {
  margin: 0 0 0 10px;
}

.e-bigger .e-treeview .e-icon-collapsible::before,
.e-bigger .e-treeview .e-icon-expandable::before,
.e-treeview.e-bigger .e-icon-collapsible::before,
.e-treeview.e-bigger .e-icon-expandable::before {
  padding: 6px;
}

.e-bigger .e-treeview.e-rtl .e-checkbox-wrapper,
.e-treeview.e-bigger.e-rtl .e-checkbox-wrapper {
  margin: 0 10px 0 0;
}

.e-bigger .e-treeview.e-rtl .e-checkbox-wrapper + .e-list-icon,
.e-bigger .e-treeview.e-rtl .e-checkbox-wrapper + .e-list-img,
.e-treeview.e-bigger.e-rtl .e-checkbox-wrapper + .e-list-icon,
.e-treeview.e-bigger.e-rtl .e-checkbox-wrapper + .e-list-img {
  margin: 0 16px 0 0;
}

.e-bigger .e-treeview.e-rtl .e-list-icon,
.e-bigger .e-treeview.e-rtl .e-list-img,
.e-treeview.e-bigger.e-rtl .e-list-icon,
.e-treeview.e-bigger.e-rtl .e-list-img {
  margin: 0 10px 0 0;
}

.e-bigger .e-treeview.e-rtl .e-list-icon + .e-list-icon,
.e-bigger .e-treeview.e-rtl .e-list-icon + .e-list-img,
.e-bigger .e-treeview.e-rtl .e-list-img + .e-list-icon,
.e-bigger .e-treeview.e-rtl .e-list-img + .e-list-img,
.e-treeview.e-bigger.e-rtl .e-list-icon + .e-list-icon,
.e-treeview.e-bigger.e-rtl .e-list-icon + .e-list-img,
.e-treeview.e-bigger.e-rtl .e-list-img + .e-list-icon,
.e-treeview.e-bigger.e-rtl .e-list-img + .e-list-img {
  margin: 0 10px 0 0;
}

.e-treeview {
  display: block;
  overflow: hidden;
  position: relative;
  white-space: nowrap;
}

.e-treeview > .e-ul {
  -webkit-overflow-scrolling: touch;
  overflow: auto;
}

.e-treeview .e-ul {
  margin: 0;
  padding: 0 0 0 24px;
}

.e-treeview li.e-node-collapsed .e-list-item .e-fullrow {
  display: none;
}

.e-treeview .e-list-item {
  list-style: none;
  padding: 2px 0;
}

.e-treeview .e-list-item .e-ul {
  margin: 2px 0 -2px;
  padding: 0 0 0 24px;
}

.e-treeview .e-list-item.e-disable > .e-text-content,
.e-treeview .e-list-item.e-disable > .e-fullrow {
  -ms-touch-action: none;
  filter: alpha(opacity=50);
  opacity: .5;
  pointer-events: none;
  touch-action: none;
}

.e-treeview .e-list-item .e-icons.e-icons-spinner::before {
  content: none;
}

.e-treeview .e-icons .e-spinner-pane {
  position: relative;
}

.e-treeview .e-text-content {
  -moz-user-select: none;
  -ms-user-select: none;
  -webkit-user-select: none;
  user-select: none;
  border: 1px solid;
  cursor: pointer;
  margin: 0;
  padding: 0 0 0 24px;
}

.e-treeview .e-fullrow {
  -moz-user-select: none;
  -ms-user-select: none;
  -webkit-user-select: none;
  user-select: none;
  border: 1px solid;
  box-sizing: border-box;
  cursor: pointer;
  height: 32px;
  left: 0;
  overflow: hidden;
  position: absolute;
  width: 100%;
}

.e-treeview .e-checkbox-wrapper {
  margin: 0 0 0 5px;
  pointer-events: all;
  position: relative;
}

.e-treeview .e-checkbox-wrapper + .e-list-icon,
.e-treeview .e-checkbox-wrapper + .e-list-img {
  margin: 0 0 0 12px;
}

.e-treeview .e-checkbox-wrapper + .e-list-text {
  padding: 0 10px;
}

.e-treeview .e-checkbox-wrapper .e-ripple-container {
  bottom: -7px;
  height: 32px;
  left: -7px;
  right: -7px;
  top: -7px;
  width: 32px;
}

.e-treeview .e-list-text {
  box-sizing: border-box;
  display: inline-block;
  line-height: 30px;
  margin: 0;
  min-height: 30px;
  padding: 0 5px;
  text-decoration: none;
  vertical-align: middle;
}

.e-treeview .e-list-text .e-input-group {
  height: 30px;
  margin-bottom: 0;
  min-width: 150px;
  vertical-align: bottom;
}

.e-treeview .e-list-text .e-input-group .e-input {
  height: 28px;
}

.e-treeview .e-list-icon,
.e-treeview .e-list-img {
  display: inline-block;
  height: 18px;
  margin: 0 0 0 5px;
  vertical-align: middle;
  width: 18px;
}

.e-treeview .e-list-icon + .e-list-icon,
.e-treeview .e-list-icon + .e-list-img,
.e-treeview .e-list-img + .e-list-icon,
.e-treeview .e-list-img + .e-list-img {
  margin: 0 0 0 10px;
}

.e-treeview .e-list-icon + .e-list-text,
.e-treeview .e-list-img + .e-list-text {
  padding: 0 10px;
}

.e-treeview .e-icon-collapsible,
.e-treeview .e-icon-expandable {
  display: inline-block;
  height: 24px;
  margin: 0 0 0 -24px;
  vertical-align: middle;
  width: 24px;
}

.e-treeview .e-icon-collapsible::before,
.e-treeview .e-icon-expandable::before {
  display: inline-block;
  padding: 7px;
}

.e-treeview .e-load {
  animation: rotation .5s infinite linear;
}

.e-treeview .e-sibling {
  border: 4px solid transparent;
  height: 6px;
  margin-top: -5px;
  position: absolute;
  width: 6px;
  z-index: 2;
}

.e-treeview .e-text-content + .e-sibling {
  margin-top: -1px;
}

.e-treeview .e-sibling::before {
  left: 0;
  height: 1px;
  position: absolute;
  width: 144px;
  z-index: 2;
}

.e-treeview .e-popup {
  -moz-user-select: none;
  -ms-user-select: none;
  -webkit-user-select: none;
  user-select: none;
  font-weight: normal;
  position: absolute;
  z-index: 99999;
}

.e-treeview .e-popup .e-content {
  border-radius: 4px;
  border-style: solid;
  border-width: 1px;
  font-size: 14px;
  padding: 4px;
}

.e-treeview .e-popup .e-icons {
  border: 1px solid transparent;
  cursor: pointer;
  display: inline-block;
  height: 26px;
  line-height: 18px;
  padding: 4px;
  width: 26px;
}

.e-treeview .e-popup .e-downtail::before,
.e-treeview .e-popup .e-downtail::after {
  border: 10px solid transparent;
  content: '';
  height: 0;
  left: 8px;
  position: absolute;
  width: 0;
}

.e-treeview .e-popup .e-downtail::after {
  bottom: -18px;
}

.e-treeview.e-fullrow-wrap .e-text-content {
  pointer-events: none;
  position: relative;
}

.e-treeview.e-fullrow-wrap .e-icon-collapsible,
.e-treeview.e-fullrow-wrap .e-icon-expandable,
.e-treeview.e-fullrow-wrap .e-input,
.e-treeview.e-fullrow-wrap .e-list-url {
  pointer-events: auto;
}

.e-treeview.e-drag-item {
  overflow: visible;
  z-index: 10000;
}

.e-treeview.e-drag-item .e-text-content {
  float: left;
}

.e-treeview.e-drag-item .e-icon-collapsible::before,
.e-treeview.e-drag-item .e-icon-expandable::before {
  font-size: 12px;
  padding: 6px;
}

.e-treeview.e-drag-item .e-drop-count {
  border: 2px solid;
  border-radius: 15px;
  box-sizing: content-box;
  font-size: 12px;
  line-height: normal;
  min-width: 12px;
  padding: 3px 5px 4px;
  margin-left: -12px;
  position: absolute;
  text-align: center;
  top: -10px;
}

.e-treeview.e-dragging .e-text-content,
.e-treeview.e-dragging .e-fullrow {
  cursor: default;
}

.e-treeview.e-rtl .e-ul {
  padding: 0 24px 0 0;
}

.e-treeview.e-rtl .e-list-item .e-ul {
  padding: 0 24px 0 0;
}

.e-treeview.e-rtl .e-text-content {
  padding: 0 24px 0 0;
}

.e-treeview.e-rtl .e-checkbox-wrapper {
  margin: 0 5px 0 0;
}

.e-treeview.e-rtl .e-checkbox-wrapper + .e-list-icon,
.e-treeview.e-rtl .e-checkbox-wrapper + .e-list-img {
  margin: 0 12px 0 0;
}

.e-treeview.e-rtl .e-list-icon,
.e-treeview.e-rtl .e-list-img {
  margin: 0 5px 0 0;
}

.e-treeview.e-rtl .e-list-icon + .e-list-icon,
.e-treeview.e-rtl .e-list-icon + .e-list-img,
.e-treeview.e-rtl .e-list-img + .e-list-icon,
.e-treeview.e-rtl .e-list-img + .e-list-img {
  margin: 0 10px 0 0;
}

.e-treeview.e-rtl .e-icon-collapsible,
.e-treeview.e-rtl .e-icon-expandable {
  margin: 0 -24px 0 0;
}

.e-treeview.e-rtl .e-sibling::before {
  right: 0;
}

.e-treeview.e-rtl.e-drag-item .e-text-content {
  float: right;
}

.e-treeview.e-rtl.e-drag-item .e-drop-count {
  margin-right: -12px;
}

.e-bigger .e-treeview .e-list-text,
.e-treeview.e-bigger .e-list-text {
  font-size: 15px;
}

.e-bigger .e-treeview .e-icon-collapsible::before,
.e-bigger .e-treeview .e-icon-expandable::before,
.e-treeview.e-bigger .e-icon-collapsible::before,
.e-treeview.e-bigger .e-icon-expandable::before {
  font-size: 12px;
}

.e-treeview {
  -webkit-tap-highlight-color: transparent;
}

.e-treeview .e-text-content,
.e-treeview .e-fullrow {
  border-color: transparent;
}

.e-treeview .e-list-text {
  color: #fff;
  font-size: 14px;
}

.e-treeview .e-list-text .e-input {
  font-weight: 600;
}

.e-treeview .e-list-icon,
.e-treeview .e-list-img {
  font-size: 16px;
}

.e-treeview .e-icon-collapsible,
.e-treeview .e-icon-expandable {
  color: #fff;
}

.e-treeview .e-icon-collapsible::before,
.e-treeview .e-icon-expandable::before {
  font-size: 10px;
}

.e-treeview .e-list-item.e-active,
.e-treeview .e-list-item.e-hover {
  background: transparent;
}

.e-treeview .e-list-item.e-hover > .e-text-content {
  color: #fff;
}

.e-treeview .e-list-item.e-hover > .e-text-content .e-list-text {
  color: #fff;
}

.e-treeview .e-list-item.e-hover > .e-text-content .e-icon-collapsible,
.e-treeview .e-list-item.e-hover > .e-text-content .e-icon-expandable {
  color: #fff;
}

.e-treeview .e-list-item.e-active > .e-text-content {
  color: #000;
}

.e-treeview .e-list-item.e-active > .e-text-content .e-list-text {
  font-weight: 600;
  color: #000;
}

.e-treeview .e-list-item.e-active > .e-text-content .e-icon-collapsible,
.e-treeview .e-list-item.e-active > .e-text-content .e-icon-expandable {
  color: #000;
}

.e-treeview .e-list-item.e-active > .e-text-content .e-check {
  background-color: #000;
  border-color: #000;
  color: #ffd939;
}

.e-treeview .e-list-item.e-active.e-hover > .e-text-content {
  color: #000;
}

.e-treeview .e-list-item.e-active.e-hover > .e-text-content .e-list-text {
  color: #000;
}

.e-treeview .e-list-item.e-active.e-hover > .e-text-content .e-icon-collapsible,
.e-treeview .e-list-item.e-active.e-hover > .e-text-content .e-icon-expandable {
  color: #000;
}

.e-treeview .e-list-item.e-editing.e-active > .e-text-content,
.e-treeview .e-list-item.e-editing.e-hover > .e-text-content {
  color: #fff;
}

.e-treeview .e-list-item.e-editing.e-active > .e-text-content .e-list-text,
.e-treeview .e-list-item.e-editing.e-hover > .e-text-content .e-list-text {
  color: #fff;
}

.e-treeview .e-list-item.e-editing.e-active > .e-text-content .e-list-text .e-input,
.e-treeview .e-list-item.e-editing.e-hover > .e-text-content .e-list-text .e-input {
  font-weight: 600;
}

.e-treeview .e-list-item.e-editing.e-active > .e-text-content .e-icon-collapsible,
.e-treeview .e-list-item.e-editing.e-active > .e-text-content .e-icon-expandable,
.e-treeview .e-list-item.e-editing.e-hover > .e-text-content .e-icon-collapsible,
.e-treeview .e-list-item.e-editing.e-hover > .e-text-content .e-icon-expandable {
  color: #fff;
}

.e-treeview .e-list-item.e-hover > .e-fullrow {
  background-color: #685708;
  border-color: #fff;
}

.e-treeview .e-list-item.e-active > .e-fullrow {
  background-color: #ffd939;
  border-color: #ffd939;
}

.e-treeview .e-list-item.e-active.e-animation-active > .e-fullrow {
  background-color: transparent;
  border-color: transparent;
}

.e-treeview .e-list-item.e-active.e-animation-active > .e-text-content {
  color: #fff;
}

.e-treeview .e-list-item.e-active.e-animation-active > .e-text-content .e-list-text {
  color: #fff;
}

.e-treeview .e-list-item.e-active.e-hover > .e-fullrow {
  background-color: #ffd939;
  border-color: #fff;
  box-shadow: 0 -1px 2px #fff, 0 1px 2px #fff;
}

.e-treeview .e-list-item.e-editing.e-active > .e-fullrow,
.e-treeview .e-list-item.e-editing.e-hover > .e-fullrow {
  background-color: transparent;
  border-color: transparent;
}

.e-treeview .e-list-item.e-disable > .e-text-content,
.e-treeview .e-list-item.e-disable > .e-fullrow {
  color: #757575;
}

.e-treeview .e-list-item.e-disable .e-icon-collapsible,
.e-treeview .e-list-item.e-disable .e-icon-expandable {
  color: #757575;
}

.e-treeview .e-sibling {
  border-right-color: transparent;
  border-left-color: #fff;
}

.e-treeview .e-sibling::before {
  background: #fff;
}

.e-treeview .e-popup .e-content {
  background-color: #685708;
  border-color: #fff;
}

.e-treeview .e-popup.e-select .e-icons {
  border-color: #fff;
}

.e-treeview .e-popup .e-downtail::before {
  border-top-color: #fff;
}

.e-treeview .e-popup .e-downtail::after {
  border-top-color: #685708;
}

.e-treeview:not(.e-fullrow-wrap) .e-list-item.e-hover > .e-text-content {
  background-color: #685708;
  border-color: #fff;
}

.e-treeview:not(.e-fullrow-wrap) .e-list-item.e-active > .e-text-content {
  background-color: #ffd939;
  border-color: #ffd939;
}

.e-treeview:not(.e-fullrow-wrap) .e-list-item.e-active.e-hover > .e-text-content {
  background-color: #ffd939;
  border-color: #fff;
  box-shadow: 0 -1px 2px #fff, 0 1px 2px #fff;
}

.e-treeview:not(.e-fullrow-wrap) .e-list-item.e-editing.e-active > .e-text-content,
.e-treeview:not(.e-fullrow-wrap) .e-list-item.e-editing.e-hover > .e-text-content {
  background-color: transparent;
  border-color: transparent;
}

.e-treeview.e-fullrow-wrap .e-text-content {
  border-color: transparent;
}

.e-treeview.e-drag-item {
  background-color: #ffd939;
  font-family: "Segoe UI", "GeezaPro", "DejaVu Serif", sans-serif, "-apple-system", "BlinkMacSystemFont";
}

.e-treeview.e-drag-item .e-icon-collapsible::before,
.e-treeview.e-drag-item .e-icon-expandable::before {
  font-size: 12px;
}

.e-treeview.e-drag-item .e-list-text {
  font-weight: 600;
  color: #000;
}

.e-treeview.e-drag-item .e-icons {
  color: #000;
}

.e-treeview.e-drag-item .e-drop-count {
  background-color: #000;
  border-color: #fff;
  color: #fff;
}

.e-treeview.e-drag-item.e-rtl .e-sibling {
  border-left-color: transparent;
  border-right-color: #fff;
}
