/*! switch layout */
.e-switch-wrapper, .e-css.e-switch-wrapper {
  cursor: pointer;
  display: inline-block;
  height: 20px;
  position: relative;
  -webkit-user-select: none;
     -moz-user-select: none;
      -ms-user-select: none;
          user-select: none;
  width: 44px;
}

.e-switch-wrapper .e-switch, .e-css.e-switch-wrapper .e-switch {
  -moz-appearance: none;
  height: 1px;
  opacity: 0;
  position: absolute;
  width: 1px;
}

.e-switch-wrapper .e-switch-inner, .e-css.e-switch-wrapper .e-switch-inner {
  -ms-transition: all 0.25s cubic-bezier(0.4, 0, 0.23, 1), outline-offset 0s;
  -webkit-transition: all 0.25s cubic-bezier(0.4, 0, 0.23, 1), outline-offset 0s;
  border: 1px solid #a6a6a6;
  border-radius: 20px;
  box-sizing: border-box;
  height: 100%;
  left: 0;
  overflow: hidden;
  position: absolute;
  top: 0;
  transition: all 0.25s cubic-bezier(0.4, 0, 0.23, 1), outline-offset 0s;
  width: 100%;
}

.e-switch-wrapper .e-switch-on, .e-switch-wrapper .e-switch-off, .e-css.e-switch-wrapper .e-switch-on, .e-css.e-switch-wrapper .e-switch-off {
  -ms-transition: all 0.25s cubic-bezier(0.4, 0, 0.23, 1) 0.05s;
  -webkit-transition: all 0.25s cubic-bezier(0.4, 0, 0.23, 1) 0.05s;
  -ms-flex-align: center;
      align-items: center;
  border-radius: inherit;
  display: -ms-flexbox;
  display: flex;
  font-family: "Segoe UI", "GeezaPro", "DejaVu Serif", sans-serif, "-apple-system", "BlinkMacSystemFont";
  font-size: 12px;
  height: 100%;
  -ms-flex-pack: center;
      justify-content: center;
  left: 0;
  position: absolute;
  transition: all 0.25s cubic-bezier(0.4, 0, 0.23, 1) 0.05s;
  width: 100%;
}

.e-switch-wrapper .e-switch-on, .e-css.e-switch-wrapper .e-switch-on {
  left: -100%;
  text-indent: -14px;
}

.e-switch-wrapper .e-switch-off, .e-css.e-switch-wrapper .e-switch-off {
  left: 0;
  opacity: 1;
  text-indent: 14px;
}

.e-switch-wrapper .e-switch-handle, .e-css.e-switch-wrapper .e-switch-handle {
  -ms-transition: all 0.25s cubic-bezier(0.4, 0, 0.23, 1) 0.05s;
  -webkit-transition: all 0.25s cubic-bezier(0.4, 0, 0.23, 1) 0.05s;
  border-radius: 50%;
  bottom: 0;
  height: 12px;
  left: 4px;
  margin: auto 0;
  position: absolute;
  top: 0;
  transition: all 0.25s cubic-bezier(0.4, 0, 0.23, 1) 0.05s;
  width: 12px;
}

.e-switch-wrapper .e-switch-inner.e-switch-active .e-switch-on, .e-css.e-switch-wrapper .e-switch-inner.e-switch-active .e-switch-on {
  left: 0;
  opacity: 1;
}

.e-switch-wrapper .e-switch-inner.e-switch-active .e-switch-off, .e-css.e-switch-wrapper .e-switch-inner.e-switch-active .e-switch-off {
  left: 100%;
}

.e-switch-wrapper .e-switch-handle.e-switch-active, .e-css.e-switch-wrapper .e-switch-handle.e-switch-active {
  left: 100%;
  margin-left: -16px;
}

.e-switch-wrapper.e-switch-disabled, .e-css.e-switch-wrapper.e-switch-disabled {
  cursor: default;
}

.e-switch-wrapper .e-ripple-container, .e-css.e-switch-wrapper .e-ripple-container {
  border-radius: 50%;
  bottom: -9px;
  height: 52px;
  left: -17px;
  pointer-events: none;
  position: absolute;
  top: -17px;
  width: 52px;
  z-index: 1;
}

.e-switch-wrapper.e-rtl .e-switch-handle, .e-css.e-switch-wrapper.e-rtl .e-switch-handle {
  left: 100%;
  margin-left: -16px;
}

.e-switch-wrapper.e-rtl .e-switch-inner.e-switch-active .e-switch-on, .e-css.e-switch-wrapper.e-rtl .e-switch-inner.e-switch-active .e-switch-on {
  left: 0;
}

.e-switch-wrapper.e-rtl .e-switch-inner.e-switch-active .e-switch-off, .e-css.e-switch-wrapper.e-rtl .e-switch-inner.e-switch-active .e-switch-off {
  left: -100%;
}

.e-switch-wrapper.e-rtl .e-switch-on, .e-css.e-switch-wrapper.e-rtl .e-switch-on {
  left: 100%;
}

.e-switch-wrapper.e-rtl .e-switch-off, .e-css.e-switch-wrapper.e-rtl .e-switch-off {
  left: 0;
}

.e-switch-wrapper.e-rtl .e-switch-handle.e-switch-active, .e-css.e-switch-wrapper.e-rtl .e-switch-handle.e-switch-active {
  border-radius: 50%;
  height: 12px;
  left: 4px;
  margin: auto 0;
  position: absolute;
  top: 0;
  transition: all 0.25s cubic-bezier(0.4, 0, 0.23, 1) 0.05s;
  width: 12px;
}

.e-switch-wrapper.e-small, .e-css.e-switch-wrapper.e-small {
  height: 16px;
  width: 34px;
}

.e-switch-wrapper.e-small .e-switch-handle, .e-css.e-switch-wrapper.e-small .e-switch-handle {
  height: 8px;
  width: 8px;
}

.e-switch-wrapper.e-small .e-ripple-container, .e-css.e-switch-wrapper.e-small .e-ripple-container {
  border-radius: 50%;
  height: 36px;
  left: -10px;
  pointer-events: none;
  position: absolute;
  top: -10px;
  width: 36px;
  z-index: 1;
}

.e-switch-wrapper.e-small .e-switch-handle.e-switch-active, .e-css.e-switch-wrapper.e-small .e-switch-handle.e-switch-active {
  left: 100%;
  margin-left: -12px;
}

.e-switch-wrapper.e-small .e-switch-on, .e-switch-wrapper.e-small .e-switch-off, .e-css.e-switch-wrapper.e-small .e-switch-on, .e-css.e-switch-wrapper.e-small .e-switch-off {
  font-size: 9px;
}

.e-switch-wrapper.e-small .e-switch-on, .e-css.e-switch-wrapper.e-small .e-switch-on {
  text-indent: -10px;
}

.e-switch-wrapper.e-small .e-switch-off, .e-css.e-switch-wrapper.e-small .e-switch-off {
  text-indent: 10px;
}

.e-switch-wrapper.e-small.e-rtl .e-switch-handle, .e-css.e-switch-wrapper.e-small.e-rtl .e-switch-handle {
  left: 100%;
  margin-left: -12px;
}

.e-switch-wrapper.e-small.e-rtl .e-switch-handle, .e-css.e-switch-wrapper.e-small.e-rtl .e-switch-handle {
  height: 8px;
  width: 8px;
}

.e-switch-wrapper.e-small.e-rtl .e-switch-on, .e-css.e-switch-wrapper.e-small.e-rtl .e-switch-on {
  left: 100%;
  opacity: 1;
}

.e-switch-wrapper.e-small.e-rtl .e-switch-off, .e-css.e-switch-wrapper.e-small.e-rtl .e-switch-off {
  left: 0;
}

.e-switch-wrapper.e-small.e-rtl .e-switch-inner.e-switch-active .e-switch-on, .e-css.e-switch-wrapper.e-small.e-rtl .e-switch-inner.e-switch-active .e-switch-on {
  left: 0;
}

.e-switch-wrapper.e-small.e-rtl .e-switch-inner.e-switch-active .e-switch-off, .e-css.e-switch-wrapper.e-small.e-rtl .e-switch-inner.e-switch-active .e-switch-off {
  left: -100%;
}

.e-switch-wrapper.e-small.e-rtl .e-switch-handle.e-switch-active, .e-css.e-switch-wrapper.e-small.e-rtl .e-switch-handle.e-switch-active {
  left: 16px;
}

.e-control.e-small .e-switch-wrapper, .e-control.e-small.e-switch-wrapper,
.e-control.e-small .e-css.e-switch-wrapper, .e-control.e-small.e-css.e-switch-wrapper {
  height: 16px;
  width: 34px;
}

.e-control.e-small .e-switch-wrapper .e-switch-handle, .e-control.e-small.e-switch-wrapper .e-switch-handle,
.e-control.e-small .e-css.e-switch-wrapper .e-switch-handle, .e-control.e-small.e-css.e-switch-wrapper .e-switch-handle {
  height: 8px;
  width: 8px;
}

.e-control.e-small .e-switch-wrapper .e-ripple-container, .e-control.e-small.e-switch-wrapper .e-ripple-container,
.e-control.e-small .e-css.e-switch-wrapper .e-ripple-container, .e-control.e-small.e-css.e-switch-wrapper .e-ripple-container {
  border-radius: 50%;
  height: 36px;
  left: -10px;
  pointer-events: none;
  position: absolute;
  top: -10px;
  width: 36px;
  z-index: 1;
}

.e-control.e-small .e-switch-wrapper .e-switch-handle.e-switch-active, .e-control.e-small.e-switch-wrapper .e-switch-handle.e-switch-active,
.e-control.e-small .e-css.e-switch-wrapper .e-switch-handle.e-switch-active, .e-control.e-small.e-css.e-switch-wrapper .e-switch-handle.e-switch-active {
  left: 100%;
  margin-left: -12px;
}

.e-control.e-small .e-switch-wrapper .e-switch-on,
.e-control.e-small .e-switch-wrapper .e-switch-off, .e-control.e-small.e-switch-wrapper .e-switch-on,
.e-control.e-small.e-switch-wrapper .e-switch-off,
.e-control.e-small .e-css.e-switch-wrapper .e-switch-on,
.e-control.e-small .e-css.e-switch-wrapper .e-switch-off, .e-control.e-small.e-css.e-switch-wrapper .e-switch-on,
.e-control.e-small.e-css.e-switch-wrapper .e-switch-off {
  font-size: 9px;
}

.e-control.e-small .e-switch-wrapper .e-switch-on, .e-control.e-small.e-switch-wrapper .e-switch-on,
.e-control.e-small .e-css.e-switch-wrapper .e-switch-on, .e-control.e-small.e-css.e-switch-wrapper .e-switch-on {
  text-indent: -10px;
}

.e-control.e-small .e-switch-wrapper .e-switch-off, .e-control.e-small.e-switch-wrapper .e-switch-off,
.e-control.e-small .e-css.e-switch-wrapper .e-switch-off, .e-control.e-small.e-css.e-switch-wrapper .e-switch-off {
  text-indent: 10px;
}

.e-control.e-small .e-switch-wrapper.e-rtl .e-switch-handle, .e-control.e-small.e-switch-wrapper.e-rtl .e-switch-handle,
.e-control.e-small .e-css.e-switch-wrapper.e-rtl .e-switch-handle, .e-control.e-small.e-css.e-switch-wrapper.e-rtl .e-switch-handle {
  left: 100%;
  margin-left: -12px;
}

.e-control.e-small .e-switch-wrapper.e-rtl .e-switch-handle, .e-control.e-small.e-switch-wrapper.e-rtl .e-switch-handle,
.e-control.e-small .e-css.e-switch-wrapper.e-rtl .e-switch-handle, .e-control.e-small.e-css.e-switch-wrapper.e-rtl .e-switch-handle {
  height: 8px;
  width: 8px;
}

.e-control.e-small .e-switch-wrapper.e-rtl .e-switch-on, .e-control.e-small.e-switch-wrapper.e-rtl .e-switch-on,
.e-control.e-small .e-css.e-switch-wrapper.e-rtl .e-switch-on, .e-control.e-small.e-css.e-switch-wrapper.e-rtl .e-switch-on {
  left: 100%;
  opacity: 1;
}

.e-control.e-small .e-switch-wrapper.e-rtl .e-switch-off, .e-control.e-small.e-switch-wrapper.e-rtl .e-switch-off,
.e-control.e-small .e-css.e-switch-wrapper.e-rtl .e-switch-off, .e-control.e-small.e-css.e-switch-wrapper.e-rtl .e-switch-off {
  left: 0;
}

.e-control.e-small .e-switch-wrapper.e-rtl .e-switch-inner.e-switch-active .e-switch-on, .e-control.e-small.e-switch-wrapper.e-rtl .e-switch-inner.e-switch-active .e-switch-on,
.e-control.e-small .e-css.e-switch-wrapper.e-rtl .e-switch-inner.e-switch-active .e-switch-on, .e-control.e-small.e-css.e-switch-wrapper.e-rtl .e-switch-inner.e-switch-active .e-switch-on {
  left: 0;
}

.e-control.e-small .e-switch-wrapper.e-rtl .e-switch-inner.e-switch-active .e-switch-off, .e-control.e-small.e-switch-wrapper.e-rtl .e-switch-inner.e-switch-active .e-switch-off,
.e-control.e-small .e-css.e-switch-wrapper.e-rtl .e-switch-inner.e-switch-active .e-switch-off, .e-control.e-small.e-css.e-switch-wrapper.e-rtl .e-switch-inner.e-switch-active .e-switch-off {
  left: -100%;
}

.e-control.e-small .e-switch-wrapper.e-rtl .e-switch-handle.e-switch-active, .e-control.e-small.e-switch-wrapper.e-rtl .e-switch-handle.e-switch-active,
.e-control.e-small .e-css.e-switch-wrapper.e-rtl .e-switch-handle.e-switch-active, .e-control.e-small.e-css.e-switch-wrapper.e-rtl .e-switch-handle.e-switch-active {
  left: 16px;
}

.e-control.e-bigger.e-small .e-switch-wrapper, .e-control.e-bigger.e-small.e-switch-wrapper,
.e-control.e-bigger.e-small .e-css.e-switch-wrapper, .e-control.e-bigger.e-small.e-css.e-switch-wrapper {
  height: 18px;
  width: 38px;
}

.e-control.e-bigger.e-small .e-switch-wrapper .e-switch-handle, .e-control.e-bigger.e-small.e-switch-wrapper .e-switch-handle,
.e-control.e-bigger.e-small .e-css.e-switch-wrapper .e-switch-handle, .e-control.e-bigger.e-small.e-css.e-switch-wrapper .e-switch-handle {
  height: 10px;
  left: 4px;
  top: 0;
  width: 10px;
}

.e-control.e-bigger.e-small .e-switch-wrapper .e-ripple-container, .e-control.e-bigger.e-small.e-switch-wrapper .e-ripple-container,
.e-control.e-bigger.e-small .e-css.e-switch-wrapper .e-ripple-container, .e-control.e-bigger.e-small.e-css.e-switch-wrapper .e-ripple-container {
  border-radius: 50%;
  height: 36px;
  left: -10px;
  pointer-events: none;
  position: absolute;
  top: -10px;
  width: 36px;
  z-index: 1;
}

.e-control.e-bigger.e-small .e-switch-wrapper .e-switch-handle.e-switch-active, .e-control.e-bigger.e-small.e-switch-wrapper .e-switch-handle.e-switch-active,
.e-control.e-bigger.e-small .e-css.e-switch-wrapper .e-switch-handle.e-switch-active, .e-control.e-bigger.e-small.e-css.e-switch-wrapper .e-switch-handle.e-switch-active {
  left: 100%;
  margin-left: -14px;
}

.e-control.e-bigger.e-small .e-switch-wrapper .e-switch-on,
.e-control.e-bigger.e-small .e-switch-wrapper .e-switch-off, .e-control.e-bigger.e-small.e-switch-wrapper .e-switch-on,
.e-control.e-bigger.e-small.e-switch-wrapper .e-switch-off,
.e-control.e-bigger.e-small .e-css.e-switch-wrapper .e-switch-on,
.e-control.e-bigger.e-small .e-css.e-switch-wrapper .e-switch-off, .e-control.e-bigger.e-small.e-css.e-switch-wrapper .e-switch-on,
.e-control.e-bigger.e-small.e-css.e-switch-wrapper .e-switch-off {
  font-size: 9px;
}

.e-control.e-bigger.e-small .e-switch-wrapper .e-switch-on, .e-control.e-bigger.e-small.e-switch-wrapper .e-switch-on,
.e-control.e-bigger.e-small .e-css.e-switch-wrapper .e-switch-on, .e-control.e-bigger.e-small.e-css.e-switch-wrapper .e-switch-on {
  text-indent: -11px;
}

.e-control.e-bigger.e-small .e-switch-wrapper .e-switch-off, .e-control.e-bigger.e-small.e-switch-wrapper .e-switch-off,
.e-control.e-bigger.e-small .e-css.e-switch-wrapper .e-switch-off, .e-control.e-bigger.e-small.e-css.e-switch-wrapper .e-switch-off {
  text-indent: 12px;
}

.e-control.e-bigger.e-small .e-switch-wrapper.e-rtl .e-switch-handle, .e-control.e-bigger.e-small.e-switch-wrapper.e-rtl .e-switch-handle,
.e-control.e-bigger.e-small .e-css.e-switch-wrapper.e-rtl .e-switch-handle, .e-control.e-bigger.e-small.e-css.e-switch-wrapper.e-rtl .e-switch-handle {
  left: 100%;
  margin-left: -14px;
}

.e-control.e-bigger.e-small .e-switch-wrapper.e-rtl .e-switch-handle, .e-control.e-bigger.e-small.e-switch-wrapper.e-rtl .e-switch-handle,
.e-control.e-bigger.e-small .e-css.e-switch-wrapper.e-rtl .e-switch-handle, .e-control.e-bigger.e-small.e-css.e-switch-wrapper.e-rtl .e-switch-handle {
  height: 10px;
  width: 10px;
}

.e-control.e-bigger.e-small .e-switch-wrapper.e-rtl .e-switch-on, .e-control.e-bigger.e-small.e-switch-wrapper.e-rtl .e-switch-on,
.e-control.e-bigger.e-small .e-css.e-switch-wrapper.e-rtl .e-switch-on, .e-control.e-bigger.e-small.e-css.e-switch-wrapper.e-rtl .e-switch-on {
  left: 100%;
  opacity: 1;
}

.e-control.e-bigger.e-small .e-switch-wrapper.e-rtl .e-switch-off, .e-control.e-bigger.e-small.e-switch-wrapper.e-rtl .e-switch-off,
.e-control.e-bigger.e-small .e-css.e-switch-wrapper.e-rtl .e-switch-off, .e-control.e-bigger.e-small.e-css.e-switch-wrapper.e-rtl .e-switch-off {
  left: 0;
}

.e-control.e-bigger.e-small .e-switch-wrapper.e-rtl .e-switch-inner.e-switch-active .e-switch-on, .e-control.e-bigger.e-small.e-switch-wrapper.e-rtl .e-switch-inner.e-switch-active .e-switch-on,
.e-control.e-bigger.e-small .e-css.e-switch-wrapper.e-rtl .e-switch-inner.e-switch-active .e-switch-on, .e-control.e-bigger.e-small.e-css.e-switch-wrapper.e-rtl .e-switch-inner.e-switch-active .e-switch-on {
  left: 0;
}

.e-control.e-bigger.e-small .e-switch-wrapper.e-rtl .e-switch-inner.e-switch-active .e-switch-off, .e-control.e-bigger.e-small.e-switch-wrapper.e-rtl .e-switch-inner.e-switch-active .e-switch-off,
.e-control.e-bigger.e-small .e-css.e-switch-wrapper.e-rtl .e-switch-inner.e-switch-active .e-switch-off, .e-control.e-bigger.e-small.e-css.e-switch-wrapper.e-rtl .e-switch-inner.e-switch-active .e-switch-off {
  left: -100%;
}

.e-control.e-bigger.e-small .e-switch-wrapper.e-rtl .e-switch-handle.e-switch-active, .e-control.e-bigger.e-small.e-switch-wrapper.e-rtl .e-switch-handle.e-switch-active,
.e-control.e-bigger.e-small .e-css.e-switch-wrapper.e-rtl .e-switch-handle.e-switch-active, .e-control.e-bigger.e-small.e-css.e-switch-wrapper.e-rtl .e-switch-handle.e-switch-active {
  left: 18px;
}

.e-control.e-bigger .e-switch-wrapper, .e-control.e-bigger.e-switch-wrapper,
.e-control.e-bigger .e-css.e-switch-wrapper, .e-control.e-bigger.e-css.e-switch-wrapper {
  height: 22px;
  width: 46px;
}

.e-control.e-bigger .e-switch-wrapper .e-switch-handle, .e-control.e-bigger.e-switch-wrapper .e-switch-handle,
.e-control.e-bigger .e-css.e-switch-wrapper .e-switch-handle, .e-control.e-bigger.e-css.e-switch-wrapper .e-switch-handle {
  height: 12px;
  left: 5px;
  top: 0;
  width: 12px;
}

.e-control.e-bigger .e-switch-wrapper .e-switch-handle.e-switch-active, .e-control.e-bigger.e-switch-wrapper .e-switch-handle.e-switch-active,
.e-control.e-bigger .e-css.e-switch-wrapper .e-switch-handle.e-switch-active, .e-control.e-bigger.e-css.e-switch-wrapper .e-switch-handle.e-switch-active {
  left: 100%;
  margin-left: -17px;
}

.e-control.e-bigger .e-switch-wrapper .e-switch-on,
.e-control.e-bigger .e-switch-wrapper .e-switch-off, .e-control.e-bigger.e-switch-wrapper .e-switch-on,
.e-control.e-bigger.e-switch-wrapper .e-switch-off,
.e-control.e-bigger .e-css.e-switch-wrapper .e-switch-on,
.e-control.e-bigger .e-css.e-switch-wrapper .e-switch-off, .e-control.e-bigger.e-css.e-switch-wrapper .e-switch-on,
.e-control.e-bigger.e-css.e-switch-wrapper .e-switch-off {
  font-size: 12px;
}

.e-control.e-bigger .e-switch-wrapper .e-switch-on, .e-control.e-bigger.e-switch-wrapper .e-switch-on,
.e-control.e-bigger .e-css.e-switch-wrapper .e-switch-on, .e-control.e-bigger.e-css.e-switch-wrapper .e-switch-on {
  text-indent: -14px;
}

.e-control.e-bigger .e-switch-wrapper .e-switch-off, .e-control.e-bigger.e-switch-wrapper .e-switch-off,
.e-control.e-bigger .e-css.e-switch-wrapper .e-switch-off, .e-control.e-bigger.e-css.e-switch-wrapper .e-switch-off {
  text-indent: 16px;
}

.e-control.e-bigger .e-switch-wrapper .e-ripple-container, .e-control.e-bigger.e-switch-wrapper .e-ripple-container,
.e-control.e-bigger .e-css.e-switch-wrapper .e-ripple-container, .e-control.e-bigger.e-css.e-switch-wrapper .e-ripple-container {
  height: 52px;
  left: -16px;
  top: -16px;
  width: 52px;
}

.e-control.e-bigger .e-switch-wrapper.e-rtl .e-switch-handle, .e-control.e-bigger.e-switch-wrapper.e-rtl .e-switch-handle,
.e-control.e-bigger .e-css.e-switch-wrapper.e-rtl .e-switch-handle, .e-control.e-bigger.e-css.e-switch-wrapper.e-rtl .e-switch-handle {
  height: 12px;
  left: 100%;
  margin-left: -17px;
  top: 0;
  width: 12px;
}

.e-control.e-bigger .e-switch-wrapper.e-rtl .e-switch-on, .e-control.e-bigger.e-switch-wrapper.e-rtl .e-switch-on,
.e-control.e-bigger .e-css.e-switch-wrapper.e-rtl .e-switch-on, .e-control.e-bigger.e-css.e-switch-wrapper.e-rtl .e-switch-on {
  left: 100%;
  opacity: 1;
}

.e-control.e-bigger .e-switch-wrapper.e-rtl .e-switch-off, .e-control.e-bigger.e-switch-wrapper.e-rtl .e-switch-off,
.e-control.e-bigger .e-css.e-switch-wrapper.e-rtl .e-switch-off, .e-control.e-bigger.e-css.e-switch-wrapper.e-rtl .e-switch-off {
  left: 0;
}

.e-control.e-bigger .e-switch-wrapper.e-rtl .e-switch-inner.e-switch-active .e-switch-on, .e-control.e-bigger.e-switch-wrapper.e-rtl .e-switch-inner.e-switch-active .e-switch-on,
.e-control.e-bigger .e-css.e-switch-wrapper.e-rtl .e-switch-inner.e-switch-active .e-switch-on, .e-control.e-bigger.e-css.e-switch-wrapper.e-rtl .e-switch-inner.e-switch-active .e-switch-on {
  left: 0;
}

.e-control.e-bigger .e-switch-wrapper.e-rtl .e-switch-inner.e-switch-active .e-switch-off, .e-control.e-bigger.e-switch-wrapper.e-rtl .e-switch-inner.e-switch-active .e-switch-off,
.e-control.e-bigger .e-css.e-switch-wrapper.e-rtl .e-switch-inner.e-switch-active .e-switch-off, .e-control.e-bigger.e-css.e-switch-wrapper.e-rtl .e-switch-inner.e-switch-active .e-switch-off {
  left: -100%;
}

.e-control.e-bigger .e-switch-wrapper.e-rtl .e-switch-handle.e-switch-active, .e-control.e-bigger.e-switch-wrapper.e-rtl .e-switch-handle.e-switch-active,
.e-control.e-bigger .e-css.e-switch-wrapper.e-rtl .e-switch-handle.e-switch-active, .e-control.e-bigger.e-css.e-switch-wrapper.e-rtl .e-switch-handle.e-switch-active {
  left: 21px;
}

.e-control.e-bigger .e-switch-wrapper.e-small, .e-control.e-bigger.e-switch-wrapper.e-small,
.e-control.e-bigger .e-css.e-switch-wrapper.e-small, .e-control.e-bigger.e-css.e-switch-wrapper.e-small {
  height: 18px;
  width: 38px;
}

.e-control.e-bigger .e-switch-wrapper.e-small .e-switch-handle, .e-control.e-bigger.e-switch-wrapper.e-small .e-switch-handle,
.e-control.e-bigger .e-css.e-switch-wrapper.e-small .e-switch-handle, .e-control.e-bigger.e-css.e-switch-wrapper.e-small .e-switch-handle {
  height: 10px;
  left: 4px;
  top: 0;
  width: 10px;
}

.e-control.e-bigger .e-switch-wrapper.e-small .e-ripple-container, .e-control.e-bigger.e-switch-wrapper.e-small .e-ripple-container,
.e-control.e-bigger .e-css.e-switch-wrapper.e-small .e-ripple-container, .e-control.e-bigger.e-css.e-switch-wrapper.e-small .e-ripple-container {
  border-radius: 50%;
  height: 36px;
  left: -10px;
  pointer-events: none;
  position: absolute;
  top: -10px;
  width: 36px;
  z-index: 1;
}

.e-control.e-bigger .e-switch-wrapper.e-small .e-switch-handle.e-switch-active, .e-control.e-bigger.e-switch-wrapper.e-small .e-switch-handle.e-switch-active,
.e-control.e-bigger .e-css.e-switch-wrapper.e-small .e-switch-handle.e-switch-active, .e-control.e-bigger.e-css.e-switch-wrapper.e-small .e-switch-handle.e-switch-active {
  left: 100%;
  margin-left: -14px;
}

.e-control.e-bigger .e-switch-wrapper.e-small .e-switch-on,
.e-control.e-bigger .e-switch-wrapper.e-small .e-switch-off, .e-control.e-bigger.e-switch-wrapper.e-small .e-switch-on,
.e-control.e-bigger.e-switch-wrapper.e-small .e-switch-off,
.e-control.e-bigger .e-css.e-switch-wrapper.e-small .e-switch-on,
.e-control.e-bigger .e-css.e-switch-wrapper.e-small .e-switch-off, .e-control.e-bigger.e-css.e-switch-wrapper.e-small .e-switch-on,
.e-control.e-bigger.e-css.e-switch-wrapper.e-small .e-switch-off {
  font-size: 9px;
}

.e-control.e-bigger .e-switch-wrapper.e-small .e-switch-on, .e-control.e-bigger.e-switch-wrapper.e-small .e-switch-on,
.e-control.e-bigger .e-css.e-switch-wrapper.e-small .e-switch-on, .e-control.e-bigger.e-css.e-switch-wrapper.e-small .e-switch-on {
  text-indent: -11px;
}

.e-control.e-bigger .e-switch-wrapper.e-small .e-switch-off, .e-control.e-bigger.e-switch-wrapper.e-small .e-switch-off,
.e-control.e-bigger .e-css.e-switch-wrapper.e-small .e-switch-off, .e-control.e-bigger.e-css.e-switch-wrapper.e-small .e-switch-off {
  text-indent: 12px;
}

.e-control.e-bigger .e-switch-wrapper.e-small.e-rtl .e-switch-handle, .e-control.e-bigger.e-switch-wrapper.e-small.e-rtl .e-switch-handle,
.e-control.e-bigger .e-css.e-switch-wrapper.e-small.e-rtl .e-switch-handle, .e-control.e-bigger.e-css.e-switch-wrapper.e-small.e-rtl .e-switch-handle {
  left: 100%;
  margin-left: -14px;
}

.e-control.e-bigger .e-switch-wrapper.e-small.e-rtl .e-switch-handle, .e-control.e-bigger.e-switch-wrapper.e-small.e-rtl .e-switch-handle,
.e-control.e-bigger .e-css.e-switch-wrapper.e-small.e-rtl .e-switch-handle, .e-control.e-bigger.e-css.e-switch-wrapper.e-small.e-rtl .e-switch-handle {
  height: 10px;
  width: 10px;
}

.e-control.e-bigger .e-switch-wrapper.e-small.e-rtl .e-switch-on, .e-control.e-bigger.e-switch-wrapper.e-small.e-rtl .e-switch-on,
.e-control.e-bigger .e-css.e-switch-wrapper.e-small.e-rtl .e-switch-on, .e-control.e-bigger.e-css.e-switch-wrapper.e-small.e-rtl .e-switch-on {
  left: 100%;
  opacity: 1;
}

.e-control.e-bigger .e-switch-wrapper.e-small.e-rtl .e-switch-off, .e-control.e-bigger.e-switch-wrapper.e-small.e-rtl .e-switch-off,
.e-control.e-bigger .e-css.e-switch-wrapper.e-small.e-rtl .e-switch-off, .e-control.e-bigger.e-css.e-switch-wrapper.e-small.e-rtl .e-switch-off {
  left: 0;
}

.e-control.e-bigger .e-switch-wrapper.e-small.e-rtl .e-switch-inner.e-switch-active .e-switch-on, .e-control.e-bigger.e-switch-wrapper.e-small.e-rtl .e-switch-inner.e-switch-active .e-switch-on,
.e-control.e-bigger .e-css.e-switch-wrapper.e-small.e-rtl .e-switch-inner.e-switch-active .e-switch-on, .e-control.e-bigger.e-css.e-switch-wrapper.e-small.e-rtl .e-switch-inner.e-switch-active .e-switch-on {
  left: 0;
}

.e-control.e-bigger .e-switch-wrapper.e-small.e-rtl .e-switch-inner.e-switch-active .e-switch-off, .e-control.e-bigger.e-switch-wrapper.e-small.e-rtl .e-switch-inner.e-switch-active .e-switch-off,
.e-control.e-bigger .e-css.e-switch-wrapper.e-small.e-rtl .e-switch-inner.e-switch-active .e-switch-off, .e-control.e-bigger.e-css.e-switch-wrapper.e-small.e-rtl .e-switch-inner.e-switch-active .e-switch-off {
  left: -100%;
}

.e-control.e-bigger .e-switch-wrapper.e-small.e-rtl .e-switch-handle.e-switch-active, .e-control.e-bigger.e-switch-wrapper.e-small.e-rtl .e-switch-handle.e-switch-active,
.e-control.e-bigger .e-css.e-switch-wrapper.e-small.e-rtl .e-switch-handle.e-switch-active, .e-control.e-bigger.e-css.e-switch-wrapper.e-small.e-rtl .e-switch-handle.e-switch-active {
  left: 18px;
}

/*! switch theme */
.e-switch-wrapper, .e-css.e-switch-wrapper {
  -webkit-tap-highlight-color: transparent;
}

.e-switch-wrapper .e-switch-off, .e-css.e-switch-wrapper .e-switch-off {
  background-color: #fff;
  color: #000;
}

.e-switch-wrapper .e-switch-handle, .e-css.e-switch-wrapper .e-switch-handle {
  background-color: #3d3d3d;
  box-shadow: none;
}

.e-switch-wrapper .e-switch-on, .e-css.e-switch-wrapper .e-switch-on {
  background-color: initial;
  color: #000;
}

.e-switch-wrapper .e-switch-handle.e-switch-active, .e-css.e-switch-wrapper .e-switch-handle.e-switch-active {
  background-color: #fff;
}

.e-switch-wrapper .e-switch-inner.e-switch-active, .e-css.e-switch-wrapper .e-switch-inner.e-switch-active {
  background-color: #400074;
  border-color: #400074;
}

.e-switch-wrapper .e-switch-inner, .e-css.e-switch-wrapper .e-switch-inner {
  background-color: transparent;
}

.e-switch-wrapper .e-ripple-element, .e-css.e-switch-wrapper .e-ripple-element {
  background-color: rgba(0, 0, 0, 0.12);
}

.e-switch-wrapper .e-ripple-check .e-ripple-element, .e-css.e-switch-wrapper .e-ripple-check .e-ripple-element {
  background-color: rgba(255, 64, 129, 0.12);
}

.e-switch-wrapper.e-switch-disabled .e-switch-handle.e-switch-active, .e-css.e-switch-wrapper.e-switch-disabled .e-switch-handle.e-switch-active {
  box-shadow: none;
}

.e-switch-wrapper.e-switch-disabled .e-switch-handle, .e-css.e-switch-wrapper.e-switch-disabled .e-switch-handle {
  background-color: #757575;
  box-shadow: none;
}

.e-switch-wrapper.e-switch-disabled .e-switch-inner .e-switch-off, .e-css.e-switch-wrapper.e-switch-disabled .e-switch-inner .e-switch-off {
  background-color: #fff;
  border-color: #757575;
  color: #757575;
  opacity: 1;
}

.e-switch-wrapper.e-switch-disabled .e-switch-inner .e-switch-on, .e-css.e-switch-wrapper.e-switch-disabled .e-switch-inner .e-switch-on {
  background-color: #fff;
  color: #757575;
  opacity: 1;
}

.e-switch-wrapper.e-switch-disabled .e-switch-inner, .e-css.e-switch-wrapper.e-switch-disabled .e-switch-inner {
  background-color: #fff;
  border-color: #757575;
  opacity: 1;
}

.e-switch-wrapper.e-switch-disabled:hover .e-switch-inner.e-switch-active, .e-css.e-switch-wrapper.e-switch-disabled:hover .e-switch-inner.e-switch-active {
  background-color: #fff;
  border-color: #757575;
}

.e-switch-wrapper.e-switch-disabled:hover .e-switch-inner, .e-css.e-switch-wrapper.e-switch-disabled:hover .e-switch-inner {
  border-color: #757575;
  color: #757575;
}

.e-switch-wrapper.e-switch-disabled:hover .e-switch-inner.e-switch-active .e-switch-on, .e-css.e-switch-wrapper.e-switch-disabled:hover .e-switch-inner.e-switch-active .e-switch-on {
  background-color: #fff;
  color: #757575;
}

.e-switch-wrapper.e-switch-disabled:hover .e-switch-handle, .e-css.e-switch-wrapper.e-switch-disabled:hover .e-switch-handle {
  background-color: #757575;
}

.e-switch-wrapper.e-switch-disabled:hover .e-switch-handle.e-switch-active, .e-css.e-switch-wrapper.e-switch-disabled:hover .e-switch-handle.e-switch-active {
  background-color: #757575;
}

.e-switch-wrapper:hover .e-switch-inner.e-switch-active, .e-css.e-switch-wrapper:hover .e-switch-inner.e-switch-active {
  background-color: #ecf;
  border-color: #000;
}

.e-switch-wrapper:hover .e-switch-inner, .e-css.e-switch-wrapper:hover .e-switch-inner {
  background-color: transparent;
  border-color: initial;
}

.e-switch-wrapper:hover .e-switch-inner.e-switch-active .e-switch-on, .e-css.e-switch-wrapper:hover .e-switch-inner.e-switch-active .e-switch-on {
  background-color: #ecf;
  color: #fff;
}

.e-switch-wrapper:hover .e-switch-handle.e-switch-active, .e-css.e-switch-wrapper:hover .e-switch-handle.e-switch-active {
  background-color: #000;
}

.e-switch-wrapper:not(.e-switch-disabled):hover .e-switch-handle:not(.e-switch-active), .e-css.e-switch-wrapper:not(.e-switch-disabled):hover .e-switch-handle:not(.e-switch-active) {
  background-color: #3d3d3d;
}

.e-switch-wrapper.e-focus .e-switch-inner, .e-css.e-switch-wrapper.e-focus .e-switch-inner {
  background-color: transparent;
  border-color: #000;
  box-shadow: none;
  outline: #000 1px solid;
  outline-offset: 1px;
}

.e-switch-wrapper.e-focus .e-switch-inner.e-switch-active, .e-css.e-switch-wrapper.e-focus .e-switch-inner.e-switch-active {
  background-color: #400074;
  border-color: #400074;
  outline: #000 1px solid;
}

.e-switch-wrapper.e-focus .e-ripple-container, .e-css.e-switch-wrapper.e-focus .e-ripple-container {
  background-color: rgba(0, 0, 0, 0.12);
}

.e-switch-wrapper.e-focus .e-ripple-check.e-ripple-container, .e-css.e-switch-wrapper.e-focus .e-ripple-check.e-ripple-container {
  background-color: rgba(255, 64, 129, 0.12);
}

.e-switch-wrapper.e-rtl.e-focus .e-switch-on, .e-css.e-switch-wrapper.e-rtl.e-focus .e-switch-on {
  background-color: #400074;
}

.e-switch-wrapper.e-rtl.e-focus .e-switch-off, .e-css.e-switch-wrapper.e-rtl.e-focus .e-switch-off {
  background-color: #400074;
}

.e-switch-wrapper.e-rtl.e-focus .e-switch-inner.e-switch-active, .e-css.e-switch-wrapper.e-rtl.e-focus .e-switch-inner.e-switch-active {
  background-color: #400074;
  border-color: #400074;
  outline: #000 1px solid;
}

.e-switch-wrapper.e-rtl .e-switch-on, .e-css.e-switch-wrapper.e-rtl .e-switch-on {
  background-color: #400074;
}

.e-switch-wrapper.e-rtl .e-switch-handle, .e-css.e-switch-wrapper.e-rtl .e-switch-handle {
  background-color: #3d3d3d;
  box-shadow: none;
}

.e-switch-wrapper.e-rtl .e-switch-inner.e-switch-active .e-switch-off, .e-css.e-switch-wrapper.e-rtl .e-switch-inner.e-switch-active .e-switch-off {
  background-color: #fff;
}

.e-switch-wrapper.e-rtl .e-switch-handle.e-switch-active, .e-css.e-switch-wrapper.e-rtl .e-switch-handle.e-switch-active {
  background-color: #fff;
}

.e-switch-wrapper.e-rtl .e-switch-inner.e-switch-active, .e-css.e-switch-wrapper.e-rtl .e-switch-inner.e-switch-active {
  background-color: #400074;
  border-color: #400074;
}

.e-switch-wrapper.e-rtl:hover .e-switch-inner.e-switch-active, .e-css.e-switch-wrapper.e-rtl:hover .e-switch-inner.e-switch-active {
  background-color: #ecf;
  border-color: #000;
}

.e-switch-wrapper.e-rtl:hover .e-switch-inner, .e-css.e-switch-wrapper.e-rtl:hover .e-switch-inner {
  border-color: initial;
}

.e-switch-wrapper.e-rtl:hover .e-switch-inner.e-switch-active .e-switch-on, .e-css.e-switch-wrapper.e-rtl:hover .e-switch-inner.e-switch-active .e-switch-on {
  background-color: #ecf;
}

.e-switch-wrapper.e-rtl:hover .e-switch-handle.e-switch-active, .e-css.e-switch-wrapper.e-rtl:hover .e-switch-handle.e-switch-active {
  background-color: #000;
}

.e-switch-wrapper.e-rtl.e-switch-disabled .e-switch-inner .e-switch-on, .e-css.e-switch-wrapper.e-rtl.e-switch-disabled .e-switch-inner .e-switch-on {
  background-color: #fff;
  color: #757575;
  opacity: 1;
}

.e-switch-wrapper.e-rtl.e-switch-disabled .e-switch-inner .e-switch-off, .e-css.e-switch-wrapper.e-rtl.e-switch-disabled .e-switch-inner .e-switch-off {
  background-color: #fff;
  color: #757575;
  opacity: 1;
}

.e-switch-wrapper.e-rtl.e-switch-disabled .e-switch-handle, .e-css.e-switch-wrapper.e-rtl.e-switch-disabled .e-switch-handle {
  background-color: #757575;
  box-shadow: none;
}

.e-switch-wrapper.e-rtl.e-switch-disabled .e-switch-handle.e-switch-active, .e-css.e-switch-wrapper.e-rtl.e-switch-disabled .e-switch-handle.e-switch-active {
  background-color: #757575;
  box-shadow: none;
}

.e-switch-wrapper.e-rtl.e-switch-disabled .e-switch-inner, .e-css.e-switch-wrapper.e-rtl.e-switch-disabled .e-switch-inner {
  background-color: #fff;
  border-color: #757575;
  opacity: 1;
}

.e-switch-wrapper.e-rtl.e-switch-disabled:hover .e-switch-inner.e-switch-active .e-switch-on, .e-css.e-switch-wrapper.e-rtl.e-switch-disabled:hover .e-switch-inner.e-switch-active .e-switch-on {
  background-color: #fff;
  color: #757575;
}

.e-switch-wrapper.e-rtl.e-switch-disabled:hover .e-switch-inner.e-switch-active, .e-css.e-switch-wrapper.e-rtl.e-switch-disabled:hover .e-switch-inner.e-switch-active {
  background-color: #fff;
  border-color: #757575;
}

.e-switch-wrapper.e-rtl.e-switch-disabled:hover .e-switch-inner, .e-css.e-switch-wrapper.e-rtl.e-switch-disabled:hover .e-switch-inner {
  border-color: #757575;
  color: #757575;
}

.e-switch-wrapper.e-rtl.e-switch-disabled:hover .e-switch-handle.e-switch-active, .e-css.e-switch-wrapper.e-rtl.e-switch-disabled:hover .e-switch-handle.e-switch-active {
  background-color: #757575;
}

.e-switch-wrapper.e-rtl.e-switch-disabled:hover .e-switch-handle, .e-css.e-switch-wrapper.e-rtl.e-switch-disabled:hover .e-switch-handle {
  background-color: #757575;
}

.e-switch-wrapper .e-switch:focus, .e-css.e-switch-wrapper .e-switch:focus {
  box-shadow: none;
}

.e-switch-wrapper.e-small.e-rtl.e-switch-disabled:hover .e-switch-inner.e-switch-active, .e-css.e-switch-wrapper.e-small.e-rtl.e-switch-disabled:hover .e-switch-inner.e-switch-active {
  background-color: transparent;
}

.e-control .e-js [class^='e-'], .e-control .e-js [class*=' e-'] {
  box-sizing: content-box;
}
