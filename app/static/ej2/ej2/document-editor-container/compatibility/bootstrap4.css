.e-de-ctnr-close::before {
  content: '\e745';
}

.e-de-ctnr-linespacing::before {
  content: '\e784';
}

.e-de-ctnr-undo::before {
  content: '\e752';
}

.e-de-ctnr-find::before {
  content: '\e724';
}

.e-de-ctnr-lock::before {
  content: '\e735';
}

.e-de-selected-spellcheck-item::before {
  content: '\e718';
  font-family: 'e-icons';
  font-size: 10px;
}

.e-de-selected-underline-item::before {
  content: '\e718';
  font-family: 'e-icons';
  font-size: 10px;
}

.e-de-ctnr-italic::before {
  content: '\e78e';
}

.e-de-ctnr-link::before {
  content: '\e72e';
}

.e-de-ctnr-download::before {
  content: '\e75d';
}

.e-de-selected-item::before {
  content: '\e718';
}

.e-de-ctnr-break::before {
  content: '\e749';
}

.e-de-ctnr-page-break::before {
  content: '\e708';
}

.e-de-ctnr-section-break::before {
  content: '\e75f';
}

.e-de-ctnr-upload::before {
  content: '\e769';
}

.e-de-ctnr-tableofcontent::before {
  content: '\e753';
}

.e-de-ctnr-pagenumber::before {
  content: '\e733';
}

.e-de-ctnr-highlight::before {
  content: '\e770';
}

.e-de-ctnr-new::before {
  content: '\e759';
}

.e-de-ctnr-paste::before {
  content: '\e739';
}

.e-de-ctnr-bold::before {
  content: '\e78b';
}

.e-de-ctnr-subscript::before {
  content: '\e707';
}

.e-de-ctnr-pagesetup::before {
  content: '\e73a';
}

.e-de-ctnr-strikethrough::before {
  content: '\e786';
}

.e-de-ctnr-image::before {
  content: '\e776';
}

.e-de-ctnr-redo::before {
  content: '\e778';
}

.e-de-ctnr-bookmark::before {
  content: '\e731';
}

.e-de-ctnr-increaseindent::before {
  content: '\e702';
}

.e-de-ctnr-header::before {
  content: '\e772';
}

.e-de-ctnr-backgroundcolor::before {
  content: '\e754';
}

.e-de-ctnr-open::before {
  content: '\e70f';
}

.e-de-ctnr-underline::before {
  content: '\e792';
}

.e-de-ctnr-superscript::before {
  content: '\e779';
}

.e-de-ctnr-alignleft::before {
  content: '\e76f';
}

.e-de-ctnr-numbering::before {
  content: '\e72c';
}

.e-de-ctnr-aligncenter::before {
  content: '\e732';
}

.e-de-ctnr-bullets::before {
  content: '\e72a';
}

.e-de-ctnr-decreaseindent::before {
  content: '\e722';
}

.e-de-ctnr-showhide::before {
  content: '\e715';
  font-size: 16px;
}

.e-de-ctnr-print::before {
  content: '\e743';
}

.e-de-ctnr-alignright::before {
  content: '\e746';
}

.e-de-ctnr-footer::before {
  content: '\e75a';
}

.e-de-ctnr-clearall::before {
  content: '\e703';
}

.e-de-ctnr-highlightcolor::before {
  content: '\e770';
}

.e-de-ctnr-insertleft::before {
  content: '\e737';
}

.e-de-ctnr-insertright::before {
  content: '\e70e';
}

.e-de-ctnr-insertabove::before {
  content: '\e783';
}

.e-de-ctnr-insertbelow::before {
  content: '\e736';
}

.e-de-ctnr-deleterows::before {
  content: '\e738';
}

.e-de-ctnr-deletecolumns::before {
  content: '\e719';
}

.e-de-ctnr-aligntop::before {
  content: '\e709';
}

.e-de-ctnr-alignbottom::before {
  content: '\e726';
}

.e-de-ctnr-fontcolor::before {
  content: '\e74b';
}

.e-de-ctnr-strokesize::before {
  content: '\e75b';
}

.e-de-ctnr-cellbg-clr-picker::before {
  content: '\e754';
}

.e-de-ctnr-mergecell::before {
  content: '\e7b1';
}

.e-de-ctnr-table::before {
  content: '\e7ac';
}

.e-de-ctnr-justify::before {
  content: '\e79b';
}

.e-de-ctnr-outsideborder::before {
  content: '\e7a1';
}

.e-de-ctnr-allborders::before {
  content: '\e79e';
}

.e-de-ctnr-insideborders::before {
  content: '\e79d';
}

.e-de-ctnr-leftborders::before {
  content: '\e7a7';
}

.e-de-ctnr-insideverticalborder::before {
  content: '\e79c';
}

.e-de-ctnr-rightborder::before {
  content: '\e79f';
}

.e-de-ctnr-topborder::before {
  content: '\e7a3';
}

.e-de-ctnr-insidehorizondalborder::before {
  content: '\e7a9';
}

.e-de-ctnr-bottomborder::before {
  content: '\e7aa';
}

.e-de-ctnr-aligncenter-table::before {
  content: '\e706';
}

.e-de-ctnr-bullet-none::before {
  content: '\e7b3';
}

.e-de-ctnr-bullet-dot::before {
  content: '\e7a0';
}

.e-de-ctnr-bullet-circle::before {
  content: '\e7b0';
}

.e-de-ctnr-bullet-square::before {
  content: '\e7a5';
}

.e-de-ctnr-bullet-flower::before {
  content: '\e7a4';
}

.e-de-ctnr-bullet-arrow::before {
  content: '\e7b6';
}

.e-de-ctnr-bullet-tick::before {
  content: '\e7ab';
}

.e-de-flip {
  transform: scaleX(-1);
}

.e-de-toolbar .e-toolbar-items .e-toolbar-item .e-tbar-btn-text .e-de-text-wrap {
  height: 0;
  line-height: 0;
}

.e-de-ctnr-file-picker {
  left: -110em;
  position: fixed;
}

.e-de-ctnr-rtl {
  direction: rtl;
}

.e-de-ctnr-hglt-btn {
  display: inline-block;
  height: 25px;
  margin: 3px;
  width: 25px;
}

.e-color-selected, .e-de-ctnr-hglt-btn:hover {
  border: 1px solid #ff8c00;
}

.e-hglt-no-color {
  height: 30px;
  padding-top: 1px;
  width: 157px;
}

.e-hglt-no-color:hover {
  background-color: #d3d3d3;
  cursor: pointer;
}

.e-de-ctnr-hglt-no-color {
  font-size: 12px;
  font-weight: 400;
  left: 40px;
  padding-top: 11px;
  position: absolute;
  top: 100px;
}

.e-de-ctn-title {
  background-color: #5a8e8a;
  color: #fff;
}

.e-de-tool-ctnr-properties-pane {
  display: -ms-inline-flexbox;
  display: inline-flex;
  height: calc(100% - 113px);
  width: 100%;
}

.e-de-ctnr-properties-pane {
  display: -ms-inline-flexbox;
  display: inline-flex;
  height: calc(100% - 113px);
  width: 100%;
}

.e-de-statusbar-seperator {
  border: 1px solid #495057;
  height: 16px;
  margin-left: 15.5px;
  margin-top: 10px;
}

.e-de-statusbar-spellcheck {
  border-radius: 2px;
  height: 34px;
  margin-left: 7.5px;
  width: 91px;
}

.e-de-ctn {
  background-color: #f8f9fa;
  border: 1px solid #dee2e6;
  height: calc(100%);
  position: relative;
  width: 100%;
}

.e-bigger .e-de-statusbar-spellcheck {
  border-radius: 2px;
  height: 34px;
  margin-left: 7.5px;
  width: 111px;
}

.e-de-ctnr-toolbar {
  display: -ms-inline-flexbox;
  display: inline-flex;
  height: 77px;
  width: 100%;
}

.e-de-tlbr-wrapper .e-de-toolbar.e-toolbar, .e-de-tlbr-wrapper .e-de-ctnr-properties-pane-btn {
  border: 0;
}

.e-de-pane {
  border-left: 1px solid #dee2e6;
}

.e-de-pane-rtl {
  border-right: 1px solid #dee2e6;
}

.e-de-tool-ctnr-properties-pane {
  background-color: #f8f9fa;
  border-bottom: 1px solid #dee2e6;
  border-top: 1px solid #dee2e6;
}

.e-de-ctnr-segment {
  margin-bottom: 14px;
}

.e-de-font-clr-picker .e-colorpicker-wrapper:first-child, .e-de-font-clr-picker > .e-split-btn-wrapper {
  margin-right: 14px;
}

.e-de-font-clr-picker.e-rtl .e-colorpicker-wrapper:first-child, .e-de-font-clr-picker.e-rtl > .e-split-btn-wrapper {
  margin-left: 14px;
  margin-right: 0;
}

.e-de-ctnr-segment > div:first-child, .e-de-ctnr-segment > button:first-child {
  margin-right: 14px;
}

.e-de-ctnr-segment.e-de-ctnr-segment-rtl > div:first-child, .e-de-ctnr-segment.e-de-ctnr-segment-rtl > button:first-child {
  margin-left: 14px;
  margin-right: 0;
}

.e-de-tlbr-wrapper {
  background-color: #f8f9fa;
  height: 77px;
  width: calc(100% - 75px);
}

.e-de-ctnr-prop-label {
  color: #212529;
  display: inline-block;
  font-size: 14px;
  font-weight: 500;
  letter-spacing: .05px;
  margin-bottom: 14px;
  opacity: .87;
}

.e-de-table-prop-label {
  margin-left: 12px;
}

.e-de-table-prop-label.e-de-rtl {
  margin-left: 0;
  margin-right: 12px;
}

.e-de-toolbar.e-toolbar {
  border-radius: 0;
}

.e-de-toolbar.e-toolbar .e-toolbar-items {
  height: 77px;
}

.e-de-toolbar.e-toolbar .e-toolbar-items .e-toolbar-item.e-de-separator {
  height: 59px;
  margin: 0 5.5px;
}

.e-de-toolbar.e-toolbar .e-toolbar-items .e-toolbar-item.e-de-toolbar-btn-start {
  margin-left: 14px;
  margin-right: 7px;
}

.e-de-toolbar.e-toolbar .e-toolbar-items .e-toolbar-item.e-de-toolbar-btn-first {
  margin-right: 7px;
}

.e-de-toolbar.e-toolbar .e-toolbar-items .e-toolbar-item.e-de-toolbar-btn-middle {
  margin-left: 7px;
  margin-right: 7px;
}

.e-de-toolbar.e-toolbar .e-toolbar-items .e-toolbar-item.e-de-toolbar-btn-last {
  margin-left: 7px;
}

.e-de-toolbar.e-toolbar .e-toolbar-items .e-toolbar-item.e-de-toolbar-btn-end {
  margin-left: 7px;
  margin-right: 14px;
}

.e-de-toolbar.e-toolbar .e-toolbar-items .e-toolbar-item.e-de-image-focus :focus {
  background-color: #6c757d;
}

.e-de-toolbar.e-toolbar .e-toolbar-items .e-toolbar-item .e-tbar-btn.e-btn {
  padding: 0;
  padding-bottom: 6px;
}

.e-de-toolbar.e-toolbar .e-toolbar-items .e-toolbar-item .e-tbar-btn.e-btn:focus {
  padding: 0;
  padding-bottom: 6px;
}

.e-de-toolbar.e-toolbar .e-toolbar-items .e-toolbar-item .e-tbar-btn.e-btn:active {
  padding: 0;
  padding-bottom: 6px;
}

.e-de-toolbar.e-toolbar .e-toolbar-items .e-toolbar-item .e-tbar-btn.e-btn:hover {
  padding: 0;
  padding-bottom: 6px;
}

.e-de-toolbar.e-toolbar .e-toolbar-items .e-toolbar-item .e-tbar-btn.e-btn {
  padding: 0 1px;
  padding-bottom: 6px;
}

.e-de-toolbar.e-toolbar .e-toolbar-items .e-toolbar-item .e-tbar-btn.e-btn:focus {
  padding: 0;
  padding-bottom: 6px;
}

.e-de-toolbar.e-toolbar .e-toolbar-items .e-toolbar-item .e-tbar-btn.e-btn:active {
  padding: 0;
  padding-bottom: 6px;
}

.e-de-toolbar.e-toolbar .e-toolbar-items .e-toolbar-item .e-tbar-btn.e-btn:hover {
  padding: 0;
  padding-bottom: 6px;
}

.e-de-toolbar.e-toolbar .e-toolbar-items .e-toolbar-item .e-tbar-btn.e-btn .e-btn-icon {
  color: #495057;
  font-size: 16px;
}

.e-de-toolbar.e-toolbar .e-toolbar-items .e-toolbar-item .e-tbar-btn.e-btn:hover .e-btn-icon, .e-de-toolbar.e-toolbar .e-toolbar-items .e-toolbar-item .e-tbar-btn.e-btn:focus .e-btn-icon, .e-de-toolbar.e-toolbar .e-toolbar-items .e-toolbar-item .e-tbar-btn.e-btn:active .e-btn-icon, .e-de-toolbar.e-toolbar .e-toolbar-items .e-toolbar-item .e-tbar-btn.e-btn:disabled .e-btn-icon {
  color: #fff;
}

.e-bigger .e-de-toolbar.e-toolbar .e-toolbar-items .e-toolbar-item .e-tbar-btn.e-btn .e-btn-icon {
  color: #495057;
  font-size: 18px;
}

.e-bigger .e-de-toolbar.e-toolbar .e-toolbar-items .e-toolbar-item .e-tbar-btn.e-btn:hover .e-btn-icon, .e-bigger .e-de-toolbar.e-toolbar .e-toolbar-items .e-toolbar-item .e-tbar-btn.e-btn:focus .e-btn-icon, .e-bigger .e-de-toolbar.e-toolbar .e-toolbar-items .e-toolbar-item .e-tbar-btn.e-btn:active .e-btn-icon, .e-bigger .e-de-toolbar.e-toolbar .e-toolbar-items .e-toolbar-item .e-tbar-btn.e-btn:disabled .e-btn-icon {
  color: #fff;
}

.e-bigger .e-de-toolbar.e-toolbar .e-toolbar-items .e-toolbar-item .e-tbar-btn-text {
  font-size: 13px;
}

.e-de-toolbar.e-toolbar .e-toolbar-items .e-toolbar-item .e-tbar-btn.e-btn .e-tbar-btn-text {
  padding: 0;
}

.e-de-overlay {
  filter: alpha(opacity=50);
  height: 100%;
  opacity: .5;
  pointer-events: none;
  -ms-touch-action: none;
      touch-action: none;
  width: 100%;
}

.e-de-font-clr-picker .e-colorpicker-wrapper .e-btn.e-icon-btn, .e-de-font-clr-picker .e-colorpicker-wrapper .e-split-btn-wrapper .e-split-colorpicker.e-split-btn {
  padding: 0;
}

.e-de-prop-font-colorpicker .e-btn.e-icon-btn, .e-de-prop-font-colorpicker .e-split-btn-wrapper .e-split-colorpicker.e-split-btn {
  padding: 4px 8px !important;
}

.e-de-ctnr-style-div {
  width: 235px;
}

.e-btn-toggle .e-tbar-btn-text {
  color: #fff !important;
}

.e-btn-toggle .e-btn-icon {
  color: #fff !important;
}

.e-de-ctnr-list, .e-de-list-header-presetmenu {
  color: #495057;
}

.e-de-ctnr-group-btn .e-btn {
  background-color: #fff;
  border-color: #dee2e6;
}

.e-de-ctnr-group-btn .e-btn:focus {
  background-color: #545b62;
  border: 1px;
  border-color: #4e555b;
  box-shadow: 0 0 0 0 #6c757d;
  outline-color: #6c757d;
}

.e-de-ctnr-group-btn .e-btn:active, .e-de-ctnr-group-btn .e-btn .e-btn-toggle {
  background-color: #545b62;
  border: 1px;
  border-color: #4e555b;
}

.e-de-ctnr-group-btn .e-btn:hover {
  background-color: #5a6268;
  border: 1px;
  border-color: #545b62;
}

.e-de-ctnr-group-btn .e-btn:disabled {
  background-color: #6c757d;
  border-color: #6c757d;
  opacity: 65%;
}

.e-de-ctnr-group-btn .e-btn:hover .e-btn-icon, .e-de-ctnr-group-btn .e-btn:focus .e-btn-icon, .e-de-ctnr-group-btn .e-btn:active .e-btn-icon, .e-de-ctnr-group-btn .e-btn:disabled .e-btn-icon {
  color: #fff;
}

.e-de-ctnr-group-btn .e-btn-icon {
  color: #495057;
  font-size: 14px;
}

.e-bigger .e-de-ctnr-group-btn .e-btn-icon {
  color: #495057;
}

.e-btn-toggle .e-btn-icon {
  color: #fff;
}

.e-de-prop-font-colorpicker .e-split-btn-wrapper .e-btn, .e-de-prop-font-colorpicker .e-split-btn-wrapper .e-btn-icon, .e-de-border-size-button, .e-de-border-size-button .e-btn-icon {
  background-color: #fff;
  border-color: #dee2e6;
  color: #495057;
}

.e-de-border-size-button:focus, .e-de-border-size-button:hover, .e-de-prop-font-colorpicker .e-split-btn-wrapper .e-btn:hover, .e-de-prop-font-colorpicker .e-split-btn-wrapper .e-btn:focus {
  background-color: #6c757d;
  border-color: inset 0 3px 5px #4e555b;
}

.e-de-border-size-button:focus .e-btn-icon, .e-de-border-size-button:hover .e-btn-icon, .e-de-prop-font-colorpicker .e-split-btn-wrapper .e-btn:hover .e-btn-icon, .e-de-prop-font-colorpicker .e-split-btn-wrapper .e-btn:focus .e-btn-icon {
  background-color: #6c757d;
  border-color: inset 0 3px 5px #4e555b;
  color: #fff;
}

.e-de-border-size-button .e-btn-icon:focus, .e-de-border-size-button .e-btn-icon:hover {
  background-color: #6c757d;
  border-color: inset 0 3px 5px #4e555b;
  color: #fff;
}

.e-de-pagenumber-text {
  background: #fff;
  color: #495057;
  border-radius: 4px;
  border: 1px solid #ced4da !important;
  height: 24px !important;
  margin-top: -3px !important;
  padding: 3px !important;
}

.de-split-button > div:first-child {
  margin-right: 14px;
}

.e-de-ctnr-properties-pane-btn {
  width: 75px;
}

.e-de-ctnr-properties-pane-btn .e-btn {
  background-color: #f8f9fa;
  border-radius: 0;
  box-shadow: none;
  color: #212529;
  min-height: 100%;
  min-width: 100%;
}

.e-de-ctnr-properties-pane-btn .e-btn:focus {
  box-shadow: none;
}

.e-de-ctnr-properties-pane-btn .e-btn:active {
  box-shadow: none;
}

.e-de-ctnr-properties-pane-btn .e-btn:hover {
  box-shadow: none;
}

.e-de-showhide-btn {
  border: 0;
  height: 77px;
}

.e-de-showhide-btn-rtl {
  border: 0;
  height: 77px;
}

.e-de-toolbar.e-toolbar .e-toolbar-items .e-toolbar-item:not(.e-separator) {
  padding: 0;
}

.e-de-toolbar.e-toolbar .e-toolbar-items .e-toolbar-item .e-tbar-btn-text {
  display: table;
  font-size: 12px;
  margin: 0 6.5px;
  padding: 0;
  white-space: normal;
}

.e-de-toolbar.e-toolbar .e-toolbar-items .e-toolbar-item button.e-btn.e-tbtn-txt .e-icons.e-btn-icon, .e-de-toolbar.e-toolbar .e-toolbar-items .e-toolbar-item .e-tbar-btn.e-btn.e-tbtn-txt .e-icons.e-btn-icon {
  padding: 0;
}

.e-de-cntr-pane-padding {
  padding: 14px;
}

.e-de-prop-pane {
  height: 100%;
  min-height: 200px;
  overflow: auto;
  width: 264px;
}

.e-bigger .e-de-cntr-pane-padding {
  padding: 16px;
}

.e-bigger .e-de-prop-pane {
  height: 100%;
  min-height: 200px;
  overflow: auto;
  width: 272px;
}

.e-bigger .e-btn-toggle .e-tbar-btn-text {
  color: #fff !important;
}

.e-bigger .e-de-prop-font-colorpicker .e-split-btn-wrapper .e-btn {
  border-color: #ced4da;
}

.e-de-ctnr-group-btn.e-btn-group:not(.e-outline) {
  box-shadow: none;
  height: 31px;
}

.e-de-ctnr-group-btn.e-btn-group:not(.e-outline):focus {
  box-shadow: none;
}

.e-de-ctnr-group-btn.e-btn-group:not(.e-outline):active {
  box-shadow: none;
}

.e-de-ctnr-group-btn.e-btn-group:not(.e-outline):hover {
  box-shadow: none;
}

.e-de-ctnr-group-btn button {
  box-shadow: none;
  height: 31px;
}

.e-de-ctnr-group-btn button:focus {
  box-shadow: none;
}

.e-de-ctnr-group-btn button:active {
  box-shadow: none;
}

.e-de-ctnr-group-btn button:hover {
  box-shadow: none;
}

.e-de-property-div-padding {
  border-bottom: 0.5px solid #dee2e6;
  padding-bottom: 11.5px;
  padding-top: 12.5px;
}

.e-de-ctnr-dropdown-ftr {
  border-top: 1px solid #dee2e6;
  color: #212529;
  display: block;
  font-size: 12px;
  line-height: 40px;
  text-indent: 1.2em;
}

.e-de-char-fmt-btn-left > button {
  width: 37.75px;
}

.e-de-char-fmt-btn-right > button {
  width: 37.5px;
}

.e-de-panel-left-width {
  width: 147px;
}

.e-bigger .e-de-panel-left-width {
  width: 149px;
}

.e-bigger .e-de-char-fmt-btn-left > button {
  width: 37.25px;
}

.e-de-panel-right-width {
  width: 73px;
}

.e-de-cntr-highlight-pane {
  border: 1px solid #dee2e6;
}

.e-de-btn-hghlclr > button:first-child {
  padding: 1px !important;
}

.e-de-ctnr-hglt-color {
  font-size: 12px;
  font-weight: 400;
  height: 18px !important;
  width: 18px !important;
}

.e-de-font-clr-picker > div button, .e-de-font-clr-picker > button {
  width: 30.8px;
}

.e-de-floating-menu.e-de-bullets-menu .e-de-floating-menuitem-md {
  height: 55px;
  padding: 4px !important;
  width: 60px;
}

.e-de-floating-menu.e-de-bullets-menu .e-de-floating-menuitem-md:hover {
  border: 3px solid #5a8e8a;
  padding: 2px !important;
}

.e-de-floating-menu.e-de-bullets-menu .e-de-floating-bullet-menuitem-md {
  height: 38px;
  padding: 4px !important;
  width: 38px;
}

.e-de-floating-menu.e-de-bullets-menu .e-de-floating-bullet-menuitem-md:hover {
  border: 3px solid #5a8e8a;
  padding: 2px !important;
}

.e-de-list-header-presetmenu {
  cursor: pointer;
  font-size: 11px;
  line-height: 14px;
  overflow: hidden;
  text-align: left;
  min-width: 50px;
  white-space: nowrap;
  width: 100%;
}

.e-de-bullet-list-header-presetmenu {
  cursor: pointer;
  font-size: 14px;
  left: -11px;
  line-height: 32px;
  min-width: 50px;
  overflow: hidden;
  position: relative;
  white-space: nowrap;
  width: 100%;
}

.e-rtl .e-de-bullet-list-header-presetmenu {
  cursor: pointer;
  font-size: 14px;
  left: 10px;
  line-height: 32px;
  min-width: 50px;
  overflow: hidden;
  position: relative;
  white-space: nowrap;
  width: 100%;
}

.e-de-bullet {
  font-size: 42px;
}

.e-de-list-header-presetmenu .e-de-list-line {
  border-bottom: 1px solid #ccc;
  margin-left: 5px;
  width: 100%;
}

.e-de-toc-optionsdiv {
  margin-bottom: 11.5px;
  margin-left: 5.5px;
  margin-top: 15.5px;
}

.e-de-toc-optionsdiv.e-de-rtl {
  margin-right: 5.5px;
  margin-left: 0;
}

.e-de-list-header-presetmenu div span {
  color: #aaa;
  display: inline-block;
  vertical-align: middle;
}

.e-de-floating-menu .e-de-floating-menuitem, .e-de-floating-menu .e-de-menuitem-none {
  cursor: pointer;
  height: 70px;
  padding: 0 !important;
  margin: 0 5px 5px 0 !important;
  width: 70px;
}

.e-de-list-thumbnail .e-de-list-items {
  float: left;
}

.e-de-list-thumbnail .e-de-list-items {
  border: 1px solid #e4e4e4;
  clear: initial;
  display: inline-block;
  height: auto;
  margin: 5px;
  padding: 2px;
  text-align: center;
  width: auto;
}

.e-de-list-items {
  cursor: pointer;
  background: #f8f9fa;
  box-sizing: border-box;
  list-style: none;
  padding: 7px 10px 7px 10px;
  position: relative;
}

.e-de-list-item-size {
  font-size: 14px;
}

.e-de-floating-menuitem.e-de-floating-menuitem-md.e-de-list-items.e-de-list-item-size.de-list-item-selected, .e-de-floating-menuitem.e-de-floating-bullet-menuitem-md.e-de-list-items.e-de-list-item-size.de-list-item-selected {
  border: 3px solid #5a8e8a;
  padding: 2px !important;
}

.e-de-floating-menu {
  padding: 10px 4px 5px 10px !important;
}

.e-de-list-container {
  background: #f8f9fa;
  border: 1px solid #ccc;
  border-radius: 2px;
  box-shadow: 0 0 14px rgba(0, 0, 0, 0.2);
  box-sizing: border-box;
  display: inline-block;
  line-height: normal;
  margin: 0;
  outline: 0;
  padding: 5px 0;
  position: absolute;
  width: auto;
  z-index: 10020;
}

.e-de-ctnr-list {
  font-size: 12px;
  vertical-align: top;
}

.e-de-image-property {
  padding-left: 32px;
}

.e-de-img-prty-span {
  color: #212529;
  left: 12px;
  position: absolute;
  top: 8px;
}

.e-btn-toggle {
  background-color: #6c757d !important;
  outline: #fafafa 0 solid;
  outline-offset: 0;
  box-shadow: inset 0 3px 5px #4e555b !important;
}

.e-btn-toggle:hover {
  background-color: #6c757d !important;
  outline: #fafafa 0 solid;
  outline-offset: 0;
  box-shadow: inset 0 3px 5px #4e555b !important;
}

.e-de-ctnr-group-btn-top > button:first-child {
  border-radius: 0;
  border-top-left-radius: 4px;
}

.e-de-ctnr-group-btn-top.e-de-rtl > button:first-child {
  border-top-left-radius: 0;
  border-top-right-radius: 4px;
}

.e-de-ctnr-group-btn-top > button:last-child {
  border-radius: 0;
  border-top-right-radius: 4px;
}

.e-de-ctnr-group-btn-top.e-de-rtl > button:last-child {
  border-top-left-radius: 4px;
  border-top-right-radius: 0;
}

.e-de-ctnr-group-btn-middle > button {
  border-radius: 0;
  border-top: 0;
  border-bottom: 0;
}

.e-de-ctnr-group-btn-bottom > button:first-child {
  border-radius: 0;
  border-bottom-left-radius: 4px;
}

.e-de-ctnr-group-btn-bottom.e-de-rtl > button:first-child {
  border-bottom-left-radius: 0;
  border-bottom-right-radius: 4px;
}

.e-de-ctnr-group-btn-bottom > button:last-child {
  border-radius: 0;
  border-bottom-right-radius: 4px;
}

.e-de-ctnr-group-btn-bottom.e-de-rtl > button:last-child {
  border-bottom-right-radius: 0;
  border-bottom-left-radius: 4px;
}

.e-de-toc-template1 {
  background: #f8f9fa;
  border: 1px solid #dee2e6;
  color: #212529;
  height: 129px;
  margin-left: 78px;
  width: 94px;
}

.e-de-toc-template1.e-de-rtl {
  margin-left: 0;
  margin-right: 78px;
}

.e-de-toc-template1-content1 {
  font-size: 10px;
  height: 11px;
  margin-left: 5.4px;
  margin-top: 6.7px;
  width: 81px;
}

.e-de-toc-template1-content2 {
  font-size: 8px;
  height: 9px;
  margin-left: 20.4px;
  margin-top: 5.7px;
  width: 64px;
}

.e-de-toc-template1-content3 {
  font-size: 7px;
  height: 8px;
  margin-left: 28.4px;
  margin-top: 6.7px;
  width: 60px;
}

.e-de-prop-sub-label {
  color: #212529;
  font-size: 13px;
  margin-bottom: 8px;
}

.e-de-toc-checkbox1 {
  height: 14px;
  margin-top: 14px;
}

.e-de-toc-checkbox2 {
  height: 14px;
  margin-top: 14px;
}

.e-de-toc-checkbox3 {
  height: 14px;
  margin-top: 14px;
}

.e-de-status-bar {
  background-color: #f8f9fa;
  border-bottom: 1px solid #dee2e6;
  display: -ms-inline-flexbox;
  display: inline-flex;
  height: 36px;
  width: 100%;
}

.e-de-ctnr-pg-no {
  color: #212529;
  display: -ms-inline-flexbox;
  display: inline-flex;
  font-size: 13px;
  height: 100%;
  width: 100px;
}

.e-de-ctnr-pg-no-spellout {
  color: #212529;
  display: -ms-inline-flexbox;
  display: inline-flex;
  font-size: 13px;
  height: 100%;
  width: calc(100% - 75px);
}

.e-bigger .e-de-ctnr-pg-no-spellout {
  color: #212529;
  display: -ms-inline-flexbox;
  display: inline-flex;
  font-size: 13px;
  height: 100%;
  width: calc(100% - 90px);
}

.e-de-statusbar-zoom-spell {
  background-color: #f8f9fa;
  border: 0;
  color: #212529;
  float: right;
  height: 34px;
  margin-left: calc(100% - 292px);
}

.e-bigger .e-de-statusbar-zoom-spell {
  background-color: #f8f9fa;
  border: 0;
  color: #212529;
  float: right;
  height: 34px;
  margin-left: calc(100% - 325px);
}

.e-de-btn-cancel {
  margin-left: 10px;
}

.e-de-btn-cancel-rtl {
  margin-left: 0;
  margin-right: 10px;
}

.e-de-prop-header-label {
  color: #212529;
  display: inline-block;
  font-size: 13px;
  font-weight: bold;
  letter-spacing: .05px;
  opacity: .87;
}

.e-de-prop-separator-line {
  border-bottom: 1px solid #dee2e6;
}

.e-de-status-bar > div label {
  font-weight: normal;
}

.e-de-stylediv {
  padding-left: 12px;
}

.e-de-stylediv-rtl {
  padding-left: 0;
  padding-right: 12px;
}

.e-de-border-style-div {
  margin-left: 12px;
}

.e-de-border-style-div.e-de-rtl {
  margin-left: 0;
  margin-right: 12px;
}

.e-de-insert-del-cell {
  margin-left: 12px;
}

.e-de-insert-del-cell.e-de-rtl {
  margin-left: 0;
  margin-right: 12px;
}

.e-de-cell-margin {
  margin-left: 12px;
}

.e-de-align-text {
  margin-left: 12px;
}

.e-de-align-text.e-de-rtl {
  margin-left: 0;
  margin-right: 12px;
}

.e-de-border-size-button {
  height: 31px;
  margin-top: 14px;
  width: 99px;
}

.e-de-color-picker {
  height: 31px;
  width: 99px;
}

.e-de-cell-div {
  margin-left: 12px;
}

.e-de-cell-div.e-de-rtl {
  margin-left: 0;
  margin-right: 12px;
}

.e-de-cell-text-box {
  margin-right: 12px;
}

.e-de-prop-fill-label {
  margin-left: 10.3px;
  margin-right: 8px;
}

.e-de-prop-fill-label.e-de-rtl {
  margin-left: 8px;
  margin-right: 10.3px;
}

.e-de-grp-btn-ctnr .e-de-ctnr-group-btn, .e-bigger .e-de-grp-btn-ctnr .e-de-ctnr-group-btn {
  height: 42px !important;
}

.e-de-grp-btn-ctnr .e-de-ctnr-group-btn > button, .e-bigger .e-de-grp-btn-ctnr .e-de-ctnr-group-btn > button {
  height: 42px;
  width: 42px;
}

.e-de-border-clr-picker .e-split-btn-wrapper > button:first-child {
  width: 70px;
}

.e-bigger .de-split-button > div:first-child {
  margin-right: 16px;
}

.e-bigger .e-de-border-clr-picker .e-split-btn-wrapper > button:first-child {
  width: 66px;
}

.e-bigger .e-de-prop-fill-label {
  margin-left: 14.5px;
  margin-right: 9.8px;
}

.e-bigger .e-de-prop-fill-label.e-de-rtl {
  margin-left: 9.8px;
  margin-right: 14.5px;
}

.e-bigger .e-de-cell-text-box {
  margin-right: 16px;
}

.e-bigger .e-de-cell-div {
  margin-left: 16px;
}

.e-bigger .e-de-cell-div.e-de-rtl {
  margin-left: 0;
  margin-right: 16px;
}

.e-bigger .e-de-border-style-div {
  margin-left: 16px;
}

.e-bigger .e-de-border-style-div.e-de-rtl {
  margin-left: 0;
  margin-right: 16px;
}

.e-bigger .e-de-color-picker {
  height: 38px;
  width: 117px;
}

.e-bigger .e-de-border-size-button {
  height: 38px;
  margin-top: 14px;
  width: 117px;
}

.e-bigger .e-de-align-text {
  margin-left: 16px;
}

.e-bigger .e-de-align-text.e-de-rtl {
  margin-left: 0;
  margin-right: 16px;
}

.e-bigger .e-de-insert-del-cell {
  margin-left: 16px;
}

.e-bigger .e-de-insert-del-cell.e-de-rtl {
  margin-left: 0;
  margin-right: 16px;
}

.e-bigger .e-de-cell-margin {
  margin-left: 14px;
}

.e-bigger .e-de-cell-margin.e-de-rtl {
  margin-left: 0;
  margin-right: 14px;
}

.e-bigger .e-de-stylediv {
  padding-left: 16px;
}

.e-bigger .e-de-stylediv-rtl {
  padding-right: 16px;
}

.e-bigger .e-de-tool-ctnr-properties-pane {
  display: -ms-inline-flexbox;
  display: inline-flex;
  height: calc(100% - 118px);
  min-height: 200px;
  width: 100%;
}

.e-bigger .e-de-ctnr-properties-pane {
  display: -ms-inline-flexbox;
  display: inline-flex;
  height: calc(100% - 118px);
  width: 100%;
}

.e-bigger .e-de-ctn {
  background-color: #f8f9fa;
  border: 1px solid #dee2e6;
  height: calc(100%);
  position: relative;
  width: 100%;
}

.e-bigger .e-de-ctnr-toolbar {
  display: -ms-inline-flexbox;
  display: inline-flex;
  height: 86px;
  width: 100%;
}

.e-bigger .e-de-tlbr-wrapper .e-de-toolbar.e-toolbar, .e-bigger .e-de-tlbr-wrapper .e-de-ctnr-properties-pane-btn {
  border: 0;
}

.e-bigger .e-de-pane {
  border-left: 1px solid #dee2e6;
}

.e-bigger .e-de-pane-rtl {
  border-right: 1px solid #dee2e6;
}

.e-bigger .e-de-ctnr-segment {
  margin-bottom: 16px;
}

.e-bigger .e-de-font-clr-picker .e-colorpicker-wrapper:first-child, .e-bigger .e-de-font-clr-picker > .e-split-btn-wrapper {
  margin-right: 16px;
}

.e-bigger .e-de-font-clr-picker.e-rtl .e-colorpicker-wrapper:first-child, .e-bigger .e-de-font-clr-picker.e-rtl > .e-split-btn-wrapper {
  margin-left: 16px;
  margin-right: 0;
}

.e-bigger .e-de-ctnr-segment > div:first-child, .e-bigger .e-de-ctnr-segment > button:first-child {
  margin-right: 16px;
}

.e-bigger .e-de-ctnr-segment.e-de-ctnr-segment-rtl > div:first-child, .e-bigger .e-de-ctnr-segment.e-de-ctnr-segment-rtl > button:first-child {
  margin-left: 16px;
  margin-right: 0;
}

.e-bigger .e-de-tlbr-wrapper {
  background-color: #f8f9fa;
  height: 86px;
  width: calc(100% - 75px);
}

.e-bigger .e-de-ctnr-prop-label {
  color: #212529;
  display: inline-block;
  font-size: 14px;
  font-weight: 500;
  letter-spacing: .05px;
  margin-bottom: 16px;
  opacity: .87;
}

.e-bigger .e-de-table-prop-label {
  margin-left: 14.5px;
}

.e-bigger .e-de-table-prop-label.e-de-rtl {
  margin-left: 0;
  margin-right: 14.5px;
}

.e-bigger .e-de-toolbar.e-toolbar .e-toolbar-items {
  height: 86px;
}

.e-bigger .e-de-toolbar.e-toolbar .e-toolbar-items .e-toolbar-item.e-de-separator {
  height: 25px;
  margin: 0 7.5px;
}

.e-bigger .e-de-toolbar.e-toolbar .e-toolbar-items .e-toolbar-item.e-de-toolbar-btn-start {
  margin-left: 16px;
  margin-right: 8px;
}

.e-bigger .e-de-toolbar.e-toolbar .e-toolbar-items .e-toolbar-item.e-de-toolbar-btn-first {
  margin-right: 8px;
}

.e-bigger .e-de-toolbar.e-toolbar .e-toolbar-items .e-toolbar-item.e-de-toolbar-btn-middle {
  margin-left: 8px;
  margin-right: 8px;
}

.e-bigger .e-de-toolbar.e-toolbar .e-toolbar-items .e-toolbar-item.e-de-toolbar-btn-last {
  margin-left: 8px;
}

.e-bigger .e-de-toolbar.e-toolbar .e-toolbar-items .e-toolbar-item.e-de-toolbar-btn-end {
  margin-left: 8px;
  margin-right: 16px;
}

.e-bigger .e-de-toolbar.e-toolbar .e-toolbar-items .e-toolbar-item.e-de-image-focus :focus {
  background-color: #6c757d;
}

.e-bigger .e-de-toolbar.e-toolbar .e-toolbar-items .e-toolbar-item .e-tbar-btn.e-btn {
  padding: 0;
  padding-bottom: 6px;
}

.e-bigger .e-de-toolbar.e-toolbar .e-toolbar-items .e-toolbar-item .e-tbar-btn.e-btn:focus {
  padding: 0;
  padding-bottom: 6px;
}

.e-bigger .e-de-toolbar.e-toolbar .e-toolbar-items .e-toolbar-item .e-tbar-btn.e-btn:active {
  padding: 0;
  padding-bottom: 6px;
}

.e-bigger .e-de-toolbar.e-toolbar .e-toolbar-items .e-toolbar-item .e-tbar-btn.e-btn:hover {
  padding: 0;
  padding-bottom: 6px;
}

.e-bigger .e-de-toolbar.e-toolbar .e-toolbar-items .e-toolbar-item .e-tbar-btn.e-btn {
  padding: 0 1px;
  padding-bottom: 6px;
}

.e-bigger .e-de-toolbar.e-toolbar .e-toolbar-items .e-toolbar-item .e-tbar-btn.e-btn:focus {
  padding: 0;
  padding-bottom: 6px;
}

.e-bigger .e-de-toolbar.e-toolbar .e-toolbar-items .e-toolbar-item .e-tbar-btn.e-btn:active {
  padding: 0;
  padding-bottom: 6px;
}

.e-bigger .e-de-toolbar.e-toolbar .e-toolbar-items .e-toolbar-item .e-tbar-btn.e-btn:hover {
  padding: 0;
  padding-bottom: 6px;
}

.e-bigger .e-de-toolbar.e-toolbar .e-toolbar-items .e-toolbar-item .e-tbar-btn.e-btn .e-tbar-btn-text {
  padding: 0;
}

.e-bigger .e-de-overlay {
  filter: alpha(opacity=50);
  height: 100%;
  opacity: .5;
  pointer-events: none;
  -ms-touch-action: none;
      touch-action: none;
  width: 100%;
}

.e-bigger .e-de-ctnr-properties-pane-btn {
  width: 75px;
}

.e-bigger .e-de-ctnr-properties-pane-btn .e-btn {
  background-color: #f8f9fa;
  border-radius: 0;
  box-shadow: none;
  min-height: 100%;
  min-width: 100%;
}

.e-bigger .e-de-ctnr-properties-pane-btn .e-btn:focus {
  box-shadow: none;
}

.e-bigger .e-de-ctnr-properties-pane-btn .e-btn:active {
  box-shadow: none;
}

.e-bigger .e-de-ctnr-properties-pane-btn .e-btn:hover {
  box-shadow: none;
}

.e-bigger .e-de-showhide-btn {
  border: 0;
  height: 86px;
}

.e-bigger .e-de-showhide-btn-rtl {
  border: 0;
  height: 86px;
}

.e-bigger .e-de-toolbar.e-toolbar .e-toolbar-items .e-toolbar-item:not(.e-separator) {
  padding: 0;
}

.e-bigger .e-de-toolbar.e-toolbar .e-toolbar-items .e-toolbar-item .e-tbar-btn-text {
  display: table;
  font-size: 12px;
  margin: 0 6px;
  padding: 0;
  white-space: normal;
}

.e-bigger .e-de-toolbar.e-toolbar .e-toolbar-items .e-toolbar-item button.e-btn.e-tbtn-txt .e-icons.e-btn-icon, .e-bigger .e-de-toolbar.e-toolbar .e-toolbar-items .e-toolbar-item .e-tbar-btn.e-btn.e-tbtn-txt .e-icons.e-btn-icon {
  padding: 0;
}

.e-bigger .e-de-ctnr-group-btn.e-btn-group:not(.e-outline) {
  box-shadow: none;
  height: 38px;
}

.e-bigger .e-de-ctnr-group-btn.e-btn-group:not(.e-outline):focus {
  box-shadow: none;
}

.e-bigger .e-de-ctnr-group-btn.e-btn-group:not(.e-outline):active {
  box-shadow: none;
}

.e-bigger .e-de-ctnr-group-btn.e-btn-group:not(.e-outline):hover {
  box-shadow: none;
}

.e-bigger .e-de-ctnr-group-btn button {
  box-shadow: none;
  height: 38px;
}

.e-bigger .e-de-ctnr-group-btn button:focus {
  box-shadow: none;
}

.e-bigger .e-de-ctnr-group-btn button:active {
  box-shadow: none;
}

.e-bigger .e-de-ctnr-group-btn button:hover {
  box-shadow: none;
}

.e-bigger .e-de-property-div-padding {
  border-bottom: 0.5px solid #dee2e6;
  padding-bottom: 14.5px;
  padding-top: 15.5px;
}

.e-bigger .e-de-ctnr-dropdown-ftr {
  border-top: 1px solid #dee2e6;
  color: #212529;
  display: block;
  font-size: 12px;
  line-height: 40px;
  text-indent: 1.2em;
}

.e-bigger .e-de-char-fmt-btn > button {
  width: 38.5px;
}

.e-bigger .e-de-btn-hghlclr > button:first-child {
  padding: 0 6px !important;
}

.e-bigger .e-de-ctnr-hglt-color {
  font-size: 12px;
  font-weight: 400;
  height: 25px !important;
  width: 25px !important;
  border-radius: 4px;
}

.e-bigger .e-de-font-clr-picker > div button, .e-bigger .e-de-font-clr-picker > button {
  width: auto;
}

.e-bigger .e-de-ctnr-list {
  font-size: 12px;
  vertical-align: top;
}

.e-bigger .e-de-image-property {
  padding-left: 32px;
}

.e-bigger .e-de-img-prty-span {
  color: #212529;
  left: 10px;
  position: absolute;
  top: 14px;
}

.e-bigger .e-btn-toggle {
  background-color: #6c757d !important;
  box-shadow: inset 0 3px 5px #4e555b !important;
  outline: #fafafa 0 solid;
  outline-offset: 0;
}

.e-bigger .e-btn-toggle:hover {
  background-color: #6c757d !important;
  outline: #fafafa 0 solid;
  outline-offset: 0;
  box-shadow: inset 0 3px 5px #4e555b !important;
}

.e-bigger .e-de-toc-template1 {
  background: #f8f9fa;
  border: 1px solid #dee2e6;
  color: #212529;
  height: 129px;
  margin-left: 78px;
  width: 94px;
}

.e-bigger .e-de-toc-template1-content1 {
  font-size: 10px;
  height: 11px;
  margin-left: 5.4px;
  margin-top: 6.7px;
  width: 81px;
}

.e-bigger .e-de-toc-template1-content2 {
  font-size: 8px;
  height: 9px;
  margin-left: 20.4px;
  margin-top: 5.7px;
  width: 64px;
}

.e-bigger .e-de-toc-template1-content3 {
  font-size: 7px;
  height: 8px;
  margin-left: 28.4px;
  margin-top: 6.7px;
  width: 60px;
}

.e-bigger .e-de-toc-optionsdiv {
  margin-bottom: 11.5px;
  margin-left: 5.5px;
  margin-top: 15.5px;
}

.e-bigger .e-de-toc-optionsdiv.e-de-rtl {
  margin-right: 5.5px;
  margin-left: 0;
}

.e-bigger .e-de-prop-sub-label {
  font-size: 13px;
  margin-bottom: 8.5px;
}

.e-bigger .e-de-btn-cancel {
  margin-left: 10px;
}

.e-bigger .e-de-status-bar {
  background-color: #f8f9fa;
  border-bottom: 1px solid #dee2e6;
  display: -ms-inline-flexbox;
  display: inline-flex;
  height: 36px;
  width: 100%;
}

.e-bigger .e-de-statusbar-zoom {
  background-color: #f8f9fa;
  border: 0;
  float: right;
  height: 34px;
}

.e-bigger .e-de-toolbar.e-toolbar .e-toolbar-items .e-toolbar-item .e-tbar-btn-text {
  line-height: 25px;
  padding: 0 5px !important;
}

.e-bigger .e-de-toolbar.e-toolbar .e-toolbar-items .e-toolbar-item .e-tbar-btn-text .e-de-text-wrap {
  height: 0;
  line-height: 0;
}

.e-lib .e-js [class^='e-'], .e-lib .e-js [class*=' e-'] {
  box-sizing: content-box;
}
