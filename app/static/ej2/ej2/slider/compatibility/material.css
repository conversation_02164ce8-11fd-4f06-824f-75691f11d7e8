@import url("https://fonts.googleapis.com/css?family=Roboto:400,500");
.e-control-wrapper.e-slider-container.e-material-slider .e-slider .e-handle.e-material-handle {
  cursor: default;
  transition: transform 0.4s cubic-bezier(0.25, 0.8, 0.25, 1);
  z-index: 3;
}

.e-control-wrapper.e-slider-container.e-material-slider .e-slider .e-handle.e-tab-handle::after {
  background-color: transparent;
}

.e-control-wrapper.e-slider-container.e-material-slider .e-slider .e-handle.e-handle-start.e-tab-handle::after {
  background-color: transparent;
}

.e-slider-tooltip.e-tooltip-wrap.e-popup {
  background-color: #e3165b;
  border: 0;
  border-radius: 8px;
  cursor: grab;
  opacity: 1;
}

.e-slider-tooltip.e-tooltip-wrap.e-popup.e-popup.e-popup-close {
  display: block;
  opacity: 0;
}

.e-slider-tooltip.e-tooltip-wrap.e-popup.e-material-tooltip-start {
  background-color: rgba(0, 0, 0, 0.26);
}

.e-slider-tooltip.e-tooltip-wrap.e-popup.e-material-tooltip-start .e-arrow-tip-inner {
  color: #000;
  opacity: 0;
}

.e-slider-tooltip.e-tooltip-wrap.e-popup.e-material-tooltip-start .e-arrow-tip-outer.e-tip-bottom {
  border-top-color: rgba(0, 0, 0, 0.26);
}

.e-slider-tooltip.e-tooltip-wrap.e-popup.e-material-tooltip-start .e-arrow-tip-outer.e-tip-top {
  border-bottom-color: rgba(0, 0, 0, 0.26);
}

.e-slider-tooltip.e-tooltip-wrap.e-popup.e-material-tooltip-start .e-arrow-tip-outer.e-tip-right {
  border-left-color: rgba(0, 0, 0, 0.26);
}

.e-slider-tooltip.e-tooltip-wrap.e-popup.e-material-tooltip-start .e-arrow-tip-outer.e-tip-left {
  border-right-color: rgba(0, 0, 0, 0.26);
}

.e-slider-tooltip.e-tooltip-wrap.e-popup.e-tooltip-active {
  cursor: grabbing;
}

.e-slider-tooltip.e-tooltip-wrap.e-popup .e-arrow-tip {
  visibility: visible;
}

.e-slider-tooltip.e-tooltip-wrap.e-popup .e-tip-content {
  background-color: transparent;
  height: 16px;
  overflow: hidden;
  padding: 0 8px;
  text-align: center;
  top: calc(50% - 8px);
  width: 100%;
}

.e-slider-tooltip.e-tooltip-wrap.e-popup .e-tip-content.e-material-tooltip-show {
  color: #fff;
}

.e-slider-tooltip.e-tooltip-wrap.e-popup .e-tip-content.e-material-tooltip-hide {
  color: transparent;
}

.e-slider-tooltip.e-tooltip-wrap.e-popup .e-arrow-tip-inner {
  color: #e3165b;
}

.e-slider-tooltip.e-tooltip-wrap.e-popup .e-arrow-tip-outer.e-tip-bottom {
  border-top-color: #e3165b;
}

.e-slider-tooltip.e-tooltip-wrap.e-popup .e-arrow-tip-outer.e-tip-top {
  border-bottom-color: #e3165b;
}

.e-slider-tooltip.e-tooltip-wrap.e-popup .e-arrow-tip-outer.e-tip-right {
  border-left-color: #e3165b;
}

.e-slider-tooltip.e-tooltip-wrap.e-popup .e-arrow-tip-outer.e-tip-left {
  border-right-color: #e3165b;
}

.e-slider-tooltip.e-tooltip-wrap.e-popup.e-tooltip-wrap.e-popup.e-material-default {
  background-color: #e3165b;
  border: 0;
  border-bottom-left-radius: 50%;
  border-bottom-right-radius: 0%;
  border-top-left-radius: 50%;
  border-top-right-radius: 50%;
  cursor: grab;
  opacity: 1;
}

.e-slider-tooltip.e-tooltip-wrap.e-popup.e-tooltip-wrap.e-popup.e-material-default.e-material-tooltip-start {
  background-color: rgba(0, 0, 0, 0.26);
}

.e-slider-tooltip.e-tooltip-wrap.e-popup.e-tooltip-wrap.e-popup.e-material-default.e-tooltip-active {
  cursor: grabbing;
}

.e-slider-tooltip.e-tooltip-wrap.e-popup.e-tooltip-wrap.e-popup.e-material-default .e-arrow-tip {
  visibility: hidden;
}

.e-slider-tooltip.e-tooltip-wrap.e-popup.e-tooltip-wrap.e-popup.e-material-default.e-slider-horizontal-before .e-tip-content {
  transform: rotate(-45deg);
}

.e-slider-tooltip.e-tooltip-wrap.e-popup.e-tooltip-wrap.e-popup.e-material-default.e-slider-horizontal-after .e-tip-content {
  transform: rotate(-225deg);
}

.e-slider-tooltip.e-tooltip-wrap.e-popup.e-tooltip-wrap.e-popup.e-material-default.e-slider-vertical-before .e-tip-content {
  transform: rotate(45deg);
}

.e-slider-tooltip.e-tooltip-wrap.e-popup.e-tooltip-wrap.e-popup.e-material-default.e-slider-vertical-after .e-tip-content {
  transform: rotate(225deg);
}

.e-slider-tooltip.e-tooltip-wrap.e-popup.e-tooltip-wrap.e-popup.e-material-default .e-tip-content {
  background-color: transparent;
  height: 16px;
  overflow: hidden;
  padding: 0;
  text-align: center;
  top: calc(50% - 8px);
  width: 100%;
}

.e-slider-tooltip.e-tooltip-wrap.e-popup.e-tooltip-wrap.e-popup.e-material-default .e-tip-content.e-material-tooltip-show {
  color: #fff;
}

.e-slider-tooltip.e-tooltip-wrap.e-popup.e-tooltip-wrap.e-popup.e-material-default .e-tip-content.e-material-tooltip-hide {
  color: transparent;
}

.e-bigger.e-slider-tooltip.e-tooltip-wrap.e-popup .e-tip-content {
  font-size: 11px;
  line-height: 16px;
}

.e-bigger .e-slider-tooltip.e-tooltip-wrap.e-popup .e-tip-content {
  font-size: 11px;
  line-height: 16px;
}

.e-bigger .e-control-wrapper.e-slider-container .e-scale .e-tick .e-tick-value, .e-control-wrapper.e-slider-container.e-bigger .e-scale .e-tick .e-tick-value {
  font-size: 12px;
}

.e-control-wrapper.e-slider-container {
  -moz-user-select: none;
  -ms-user-select: none;
  -webkit-tap-highlight-color: transparent;
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  box-sizing: border-box;
  display: inline-block;
  height: 18px;
  line-height: normal;
  outline: none;
  position: relative;
  user-select: none;
}

.e-control-wrapper.e-slider-container::after {
  content: "material";
  display: none;
}

.e-control-wrapper.e-slider-container .e-slider .e-handle.e-large-thumb-size {
  transform: scale(1.5);
}

.e-control-wrapper.e-slider-container.e-rtl.e-horizontal .e-slider .e-handle {
  margin: 0 -6px 0 0;
  top: calc(50% - 6px);
}

.e-control-wrapper.e-slider-container.e-rtl.e-horizontal .e-scale.e-h-scale .e-tick.e-first-tick {
  background-position-x: right;
}

.e-control-wrapper.e-slider-container.e-rtl.e-horizontal .e-scale.e-h-scale .e-tick.e-last-tick {
  background-position-x: left;
}

.e-control-wrapper.e-slider-container.e-rtl.e-vertical {
  direction: ltr;
}

.e-control-wrapper.e-slider-container.e-disabled.e-material-slider .e-slider .e-handle.e-handle-first {
  background-color: #757575;
}

.e-control-wrapper.e-slider-container.e-disabled:hover .e-scale .e-tick {
  visibility: hidden;
}

.e-control-wrapper.e-slider-container.e-disabled .e-slider .e-range {
  background: rgba(0, 0, 0, 0.26);
}

.e-control-wrapper.e-slider-container.e-disabled .e-slider .e-handle {
  background: #757575;
  transform: scale(0.5) !important;
}

.e-control-wrapper.e-slider-container.e-disabled .e-slider .e-handle.e-handle-first {
  background-color: #757575;
  border: 0;
}

.e-control-wrapper.e-slider-container.e-disabled .e-slider .e-handle::after {
  -ms-transform: scale(0);
  -webkit-transform: scale(0);
  background: transparent;
  border: 7px solid #fff;
  border-radius: 50%;
  box-sizing: border-box;
  content: '';
  height: 26px;
  left: calc(50% - 13px);
  opacity: 1;
  position: absolute;
  top: calc(50% - 13px);
  transform: scale(1);
  transition: none;
  width: 26px;
  z-index: -1;
}

.e-control-wrapper.e-slider-container.e-disabled .e-btn {
  cursor: default;
}

.e-control-wrapper.e-slider-container.e-disabled .e-slider .e-handle {
  cursor: default;
}

.e-control-wrapper.e-slider-container.e-disabled .e-slider .e-handle.e-handle-disable {
  display: block;
}

.e-control-wrapper.e-slider-container.e-horizontal {
  height: 48px;
  width: 100%;
}

.e-control-wrapper.e-slider-container.e-horizontal .e-first-button {
  left: 0;
  margin-top: -9px;
  top: 50%;
}

.e-control-wrapper.e-slider-container.e-horizontal .e-first-button .e-button-icon {
  background-image: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADAAAAAwCAMAAABg3Am1AAAAkFBMVEUAAAD////////////////////////+/v7////+/v7////////+/v7+/v7////////+/v7+/v7+/v7////+/v7+/v7+/v7+/v7////+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7///8G+5iqAAAAL3RSTlMABAgMEBQYHCAkKDA0ODxETFRYYGRsdHyAh4uTm6Onr7O/w8fL09ff4+fr7/P3+/6nVicAAADJSURBVHgBldJHdoNgAANhQUjvPTHuxbgAuv/t/J5O8I82Wn27UfGaVmjvBwvsdmEDUH33JuBxYwNQ/48m4LWzAbie2QR8nkzA/coGoPodTMDzzgagae1ykHAASDgAJBwAEk4xSDgAJBwAEg4ACacYJBwAEg4ACYeAjyMEaqYQSC97CFT/DQxID2sIpK8zBLqZQyC9dRDoajIyID1tIVD10zMg3S0ZSF4IJC8GkhcDyQuB5MVA8kIgeTGQvBhIXggkLwaSFwLJK3cBRvanB71ijlkAAAAASUVORK5CYII=");
  background-repeat: no-repeat;
  background-size: cover;
  display: inline-block;
  height: 7px;
  left: calc(50% - 4.6px);
  position: absolute;
  top: calc(50% - 3.6px);
  width: 7px;
}

.e-control-wrapper.e-slider-container.e-horizontal .e-second-button {
  margin-top: -9px;
  right: 0;
  top: 50%;
}

.e-control-wrapper.e-slider-container.e-horizontal .e-second-button .e-button-icon {
  background-image: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADAAAAAwCAMAAABg3Am1AAAAkFBMVEUAAAD////////////////////////+/v7////////+/v7+/v7+/v7////////+/v7////+/v7+/v7+/v7+/v7+/v7////////+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7///9VYKa9AAAAL3RSTlMABAgMEBQYHCAoLDQ4PEBMUFhcZGx0eICDi5Obn6ers7vDx8vP19vf4+fr7/P3++LF0fAAAADNSURBVHgBldNHbsNAGEPhkZSi9F4S915k6d3/dga8Jgjw7bkYzPeXS5ObkkX3EQ5gcRcO6H/qbACbp3AAo6twwOEtHMCsDQecvqpsAKuHcMDw12QD2L2EA/C8EFleiCwvRJYXIssLmeGFzPBCZnghM7yQGV7IDC9Eglc26D6zwfS2JIP9a/To4b8pyWD9GH1c/11FNOZthO/4nvEeX5dksH2OTnT4rUsyWN4Xk4cj8nBEHo7IwxF5OCIPR+ThiDwckYcj8nBEHo5Iw5GdAVm8p9sPUXNKAAAAAElFTkSuQmCC");
  background-repeat: no-repeat;
  background-size: cover;
  display: inline-block;
  height: 7px;
  left: calc(50% - 2.6px);
  position: absolute;
  top: calc(50% - 3.6px);
  width: 7px;
}

.e-control-wrapper.e-slider-container.e-horizontal.e-slider-btn {
  padding: 0 30px;
}

.e-control-wrapper.e-slider-container.e-horizontal .e-slider {
  height: 32px;
  position: relative;
  top: calc(50% - 16px);
  width: 100%;
}

.e-control-wrapper.e-slider-container.e-horizontal .e-slider-track {
  height: 2px;
  left: 0;
  position: absolute;
  width: 100%;
  background: rgba(0, 0, 0, 0.26);
  top: calc(50% - 1px);
}

.e-control-wrapper.e-slider-container.e-horizontal .e-handle {
  margin-left: -6px;
  top: calc(50% - 6px);
}

.e-control-wrapper.e-slider-container.e-horizontal.e-scale-both .e-range {
  height: 2px;
}

.e-control-wrapper.e-slider-container.e-horizontal .e-range {
  height: 2px;
  top: calc(50% - 1px);
}

.e-control-wrapper.e-slider-container.e-horizontal .e-limits {
  background-color: rgba(0, 0, 0, 0.26);
  height: 2px;
  position: absolute;
  top: calc(50% - 1px);
}

.e-control-wrapper.e-slider-container.e-vertical {
  height: inherit;
  padding: 38px 0;
  width: 48px;
}

.e-control-wrapper.e-slider-container.e-vertical .e-slider {
  height: 100%;
  left: calc(50% - 16px);
  position: relative;
  width: 32px;
}

.e-control-wrapper.e-slider-container.e-vertical .e-slider-track {
  background: rgba(0, 0, 0, 0.26);
  bottom: 0;
  height: 100%;
  position: absolute;
  left: calc(50% - 1px);
  width: 2px;
}

.e-control-wrapper.e-slider-container.e-vertical.e-small-size.e-slider-btn {
  height: 100%;
  padding: 30px 0;
}

.e-control-wrapper.e-slider-container.e-vertical.e-small-size.e-slider-btn .e-slider {
  height: 100%;
  width: 2px;
}

.e-control-wrapper.e-slider-container.e-vertical .e-first-button {
  bottom: 0;
  margin-right: -9px;
  right: 50%;
}

.e-control-wrapper.e-slider-container.e-vertical .e-first-button .e-button-icon {
  background-image: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAMAAABEpIrGAAAAjVBMVEUAAAD////////////////////////+/v7////+/v7////////+/v7////////+/v7+/v7+/v7+/v7////+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7////1kjJ6AAAALnRSTlMABAgMEBQYHCAkKDA4QERITFRcYGRodHyDj5OXn6Ons7e7v8vP09ff5+vv8/f7zZvJMAAAAItJREFUeAHdyVcagjAQBsAfUVTAXsTeSTRm7388i/kUQjYHYF4H9bAUXjuEV/KQEdC98f9I8dLXxJngY879CsbG/ccARuPsetHCT1tWXyUoSDXZRiiZ2p/Bsi7/HrbgVPw8REUk/n+P4ZAoMvQATmMyFmBk39+CExzefwnBauZEsgOPWOkevIYz1NEToo45F58KJy0AAAAASUVORK5CYII=");
  background-repeat: no-repeat;
  background-size: cover;
  display: inline-block;
  height: 8px;
  left: calc(50% - 3.8px);
  position: absolute;
  top: calc(50% - 2.7px);
  width: 8px;
}

.e-control-wrapper.e-slider-container.e-vertical .e-second-button {
  margin-right: -9px;
  right: 50%;
  top: 0;
}

.e-control-wrapper.e-slider-container.e-vertical .e-second-button .e-button-icon {
  background-image: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAMAAABEpIrGAAAAjVBMVEUAAAD////////////////////////////////+/v7////+/v7+/v7////////+/v7+/v7////+/v7////+/v7+/v7////+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7////wrNfvAAAALnRSTlMABAgMEBQYICgsMDQ4QERITFBcYGhseHyDi5Obn6Ort7u/x8/X29/j5+vv8/f77y3LJgAAAI1JREFUeAHdyVeagjAUBtCbYQTsXewFENGYf//LsyB8kORmAZzXQ600mLrfz2XX9X8xcO0Qb4+3s2B/ia8N9yOFwtz+wR0/z77tvQSVm0+mI2piYXyEhp3+E2gWzQ8lNGpY//8UhjygijjBIvGotIbVofwZGFHxPQmGGn/ez8B6hETiAofUo1XmtKVWeAEhzjgqqZr/lwAAAABJRU5ErkJggg==");
  background-repeat: no-repeat;
  background-size: cover;
  display: inline-block;
  height: 8px;
  left: calc(50% - 3.8px);
  position: absolute;
  top: calc(50% - 4.7px);
  width: 8px;
}

.e-control-wrapper.e-slider-container.e-vertical.e-scale-both .e-slider .e-handle {
  margin-bottom: -6px;
}

.e-control-wrapper.e-slider-container.e-vertical .e-slider .e-handle {
  margin-bottom: -6px;
  left: calc(50% - 6px);
}

.e-control-wrapper.e-slider-container.e-vertical .e-slider .e-range {
  left: calc(50% - 1px);
  width: 2px;
}

.e-control-wrapper.e-slider-container.e-vertical .e-slider .e-limits {
  background-color: rgba(0, 0, 0, 0.26);
  left: calc(50% - 1px);
  position: absolute;
  width: 2px;
}

.e-control-wrapper.e-slider-container .e-range {
  border-radius: 2px;
  position: absolute;
  transition: left 300ms ease-out, right 300ms ease-out, bottom 300ms ease-out, width 300ms ease-out, height 300ms ease-out;
}

.e-control-wrapper.e-slider-container .e-range.e-drag-horizontal {
  cursor: ew-resize;
}

.e-control-wrapper.e-slider-container .e-range.e-drag-vertical {
  cursor: ns-resize;
}

.e-control-wrapper.e-slider-container .e-slider {
  box-sizing: border-box;
  cursor: default;
  display: block;
  outline: 0 none;
  padding: 0;
  position: relative;
}

.e-control-wrapper.e-slider-container .e-slider .e-handle {
  border-radius: 50%;
  box-sizing: border-box;
  cursor: grab;
  height: 12px;
  outline: none;
  position: absolute;
  -ms-touch-action: none;
      touch-action: none;
  transition: left 300ms ease-out, right 300ms ease-out, bottom 300ms ease-out, transform 300ms ease-out;
  width: 12px;
  z-index: 10;
  cursor: grab;
}

.e-control-wrapper.e-slider-container .e-slider .e-handle.e-handle-active {
  cursor: grabbing;
}

.e-control-wrapper.e-slider-container .e-slider .e-handle.e-handle-start {
  background-color: #fff;
  border: 2px solid rgba(0, 0, 0, 0.26);
}

.e-control-wrapper.e-slider-container .e-slider .e-handle::after {
  -moz-box-sizing: content-box;
  -ms-transform: scale(0);
  -webkit-box-sizing: content-box;
  -webkit-transform: scale(0);
  border: 0 solid;
  border-radius: 50%;
  box-sizing: content-box;
  content: '';
  filter: alpha(opacity=26);
  height: 32px;
  left: calc(50% - 16px);
  opacity: .26;
  position: absolute;
  top: calc(50% - 16px);
  transform: scale(0);
  transition: transform 0.4s cubic-bezier(0.25, 0.8, 0.25, 1);
  width: 32px;
  z-index: -1;
}

.e-control-wrapper.e-slider-container .e-slider .e-handle.e-tab-handle.e-handle-start::after {
  background-color: #e0e0e0;
  opacity: .38;
}

.e-control-wrapper.e-slider-container .e-slider .e-handle.e-tab-handle::after {
  -ms-transform: scale(1);
  -webkit-transform: scale(1);
  background-color: #e3165b;
  transform: scale(1);
}

.e-control-wrapper.e-slider-container .e-tick-before.e-scale.e-h-scale .e-tick {
  background-position: bottom;
  height: 50%;
  top: 1px;
}

.e-control-wrapper.e-slider-container .e-tick-before.e-scale.e-v-scale .e-tick {
  background-position: right;
  left: 1px;
  width: 50%;
}

.e-control-wrapper.e-slider-container .e-tick-after.e-scale.e-h-scale .e-tick {
  background-position-x: center;
  height: 50%;
  top: calc(50% - 1px);
}

.e-control-wrapper.e-slider-container .e-tick-after.e-scale.e-v-scale .e-tick {
  background-position: left;
  left: calc(50% - 1px);
  width: 50%;
}

.e-control-wrapper.e-slider-container .e-tick-both.e-scale.e-h-scale .e-tick {
  background-position: center;
  height: 99%;
}

.e-control-wrapper.e-slider-container .e-tick-both.e-scale.e-v-scale .e-tick {
  background-position: center;
  width: 100%;
}

.e-control-wrapper.e-slider-container .e-tick-both.e-scale.e-v-scale .e-large {
  width: 100%;
}

.e-control-wrapper.e-slider-container .e-scale {
  box-sizing: content-box;
  font-family: "Roboto", "Segoe UI", "GeezaPro", "DejaVu Serif", "sans-serif", "-apple-system", "BlinkMacSystemFont";
  height: 28px;
  line-height: normal;
  list-style: none outside none;
  margin: 0;
  outline: 0 none;
  padding: 0;
  position: absolute;
  top: calc(50% - 14px);
  width: 100%;
  z-index: 1;
}

.e-control-wrapper.e-slider-container .e-scale .e-tick {
  background-image: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAIAAAACCAYAAABytg0kAAAACXBIWXMAAAsSAAALEgHS3X78AAAAFElEQVQI12NkYGAIZ2BgYGBigAIABPgAW+yfXroAAAAASUVORK5CYII=");
  cursor: default;
  outline: none;
  position: relative;
  -webkit-user-select: none;
     -moz-user-select: none;
      -ms-user-select: none;
          user-select: none;
}

.e-control-wrapper.e-slider-container .e-scale .e-tick .e-tick-value {
  color: #000;
  font-family: "Roboto", "Segoe UI", "GeezaPro", "DejaVu Serif", "sans-serif", "-apple-system", "BlinkMacSystemFont";
  font-size: 10px;
  outline: none;
  position: absolute;
  -webkit-user-select: none;
     -moz-user-select: none;
      -ms-user-select: none;
          user-select: none;
  white-space: nowrap;
}

.e-control-wrapper.e-slider-container .e-scale.e-v-scale {
  height: 100%;
  left: calc(50% - 14px);
  top: 0;
  width: 28px;
}

.e-control-wrapper.e-slider-container .e-scale.e-v-scale .e-tick {
  background-repeat: no-repeat;
}

.e-control-wrapper.e-slider-container .e-scale.e-v-scale .e-tick.e-first-tick {
  background-position-y: top;
}

.e-control-wrapper.e-slider-container .e-scale.e-v-scale .e-tick.e-last-tick {
  background-position-y: bottom;
}

.e-control-wrapper.e-slider-container .e-scale.e-h-scale .e-tick {
  display: inline-block;
  background-repeat: no-repeat;
}

.e-control-wrapper.e-slider-container .e-scale.e-h-scale .e-tick .e-tick-value.e-tick-before {
  top: -18px;
}

.e-control-wrapper.e-slider-container .e-scale.e-h-scale .e-tick .e-tick-value.e-tick-after {
  bottom: -20px;
}

.e-control-wrapper.e-slider-container .e-scale.e-h-scale .e-tick .e-tick-value.e-tick-both {
  bottom: -20px;
}

.e-control-wrapper.e-slider-container .e-scale.e-h-scale .e-tick .e-tick-value.e-tick-both:first-child {
  top: -18px;
}

.e-control-wrapper.e-slider-container .e-scale.e-h-scale .e-tick.e-first-tick {
  background-position-x: left;
}

.e-control-wrapper.e-slider-container .e-scale.e-h-scale .e-tick.e-last-tick {
  background-position-x: right;
}

.e-control-wrapper.e-slider-container .e-scale.e-v-scale .e-tick .e-tick-value.e-tick-before {
  right: 26px;
}

.e-control-wrapper.e-slider-container .e-scale.e-v-scale .e-tick .e-tick-value.e-tick-after {
  left: 27px;
}

.e-control-wrapper.e-slider-container .e-scale.e-v-scale .e-tick .e-tick-value.e-tick-both {
  right: 41px;
}

.e-control-wrapper.e-slider-container .e-scale.e-v-scale .e-tick .e-tick-value.e-tick-both:first-child {
  left: 39px;
}

.e-control-wrapper.e-slider-container .e-scale .e-tick {
  visibility: hidden;
}

.e-control-wrapper.e-slider-container .e-scale .e-tick .e-tick-value {
  visibility: visible;
}

.e-control-wrapper.e-slider-container:hover .e-scale .e-tick {
  visibility: visible;
}

.e-control-wrapper.e-slider-container:active .e-scale .e-tick {
  visibility: visible;
}

.e-slider-hover .e-scale .e-tick {
  visibility: visible;
}

/*! component theme */
.e-control-wrapper.e-slider-container.e-material-slider .e-slider .e-handle.e-handle-first {
  background: transparent;
  border-color: transparent;
}

.e-control-wrapper.e-slider-container.e-material-slider .e-slider .e-handle.e-handle-second {
  background: transparent;
  border-color: transparent;
}

.e-control-wrapper.e-slider-container .e-slider-button {
  background-color: rgba(0, 0, 0, 0.54);
  border: 1px solid transparent;
  border-radius: 50%;
  box-sizing: border-box;
  cursor: pointer;
  height: 18px;
  outline: none;
  padding: 0;
  position: absolute;
  width: 18px;
}

.e-control-wrapper.e-slider-container .e-slider .e-range {
  background-color: #e3165b;
}

.e-control-wrapper.e-slider-container .e-slider .e-handle {
  background-color: #e3165b;
  border-color: #e3165b;
}

.e-control-wrapper.e-slider-container .e-slider .e-handle.e-material-tooltip {
  background-color: transparent;
  border-color: transparent;
}

.e-control-wrapper.e-slider-container.e-slider-hover .e-slider-track {
  background-color: rgba(0, 0, 0, 0.26);
}

.e-control-wrapper.e-slider-container.e-slider-hover .e-range {
  background-color: #e3165b;
}

.e-control-wrapper.e-slider-container.e-slider-hover .e-handle {
  border-color: #e3165b;
}

.e-control .e-js [class^='e-'], .e-control .e-js [class*=' e-'] {
  box-sizing: content-box;
}
