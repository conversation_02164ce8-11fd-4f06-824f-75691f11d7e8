/*! calendar fabric theme variables */
/*! component icons */
.e-calendar .e-header .e-date-icon-prev::before {
  content: '\e85e';
}

.e-calendar .e-header .e-date-icon-next::before {
  content: '\e84f';
}

/*! calendar layout */
.e-controlejs-calendar {
  display: block;
}

.e-control.e-calendar, .e-bigger.e-small .e-control.e-calendar {
  -webkit-tap-highlight-color: transparent;
  border-radius: 0;
  display: block;
  overflow: auto;
  -webkit-user-select: none;
     -moz-user-select: none;
      -ms-user-select: none;
          user-select: none;
}

.e-control.e-calendar.e-rtl .e-header .e-title, .e-bigger.e-small .e-control.e-calendar.e-rtl .e-header .e-title {
  float: right;
  text-align: right;
}

.e-control.e-calendar.e-rtl .e-header .e-icon-container, .e-bigger.e-small .e-control.e-calendar.e-rtl .e-header .e-icon-container {
  float: left;
}

.e-control.e-calendar .e-header, .e-bigger.e-small .e-control.e-calendar .e-header {
  background: none;
  display: table;
  font-weight: 300;
  position: relative;
  text-align: center;
  width: 100%;
}

.e-control.e-calendar .e-header button, .e-bigger.e-small .e-control.e-calendar .e-header button {
  background: transparent;
  border: 0;
  padding: 0;
  text-decoration: none;
}

.e-control.e-calendar .e-header span, .e-bigger.e-small .e-control.e-calendar .e-header span {
  cursor: pointer;
  display: inline-block;
  font-size: 14px;
  font-weight: 300;
  line-height: 16px;
  padding: 7px;
  vertical-align: middle;
}

.e-control.e-calendar .e-header span.e-disabled, .e-bigger.e-small .e-control.e-calendar .e-header span.e-disabled {
  cursor: default;
}

.e-control.e-calendar .e-week-header, .e-bigger.e-small .e-control.e-calendar .e-week-header {
  padding: 0;
}

.e-control.e-calendar th, .e-bigger.e-small .e-control.e-calendar th {
  cursor: default;
  font-size: 12px;
  font-weight: normal;
  text-align: center;
}

.e-control.e-calendar .e-content .e-selected, .e-control.e-calendar .e-content .e-state-hover, .e-bigger.e-small .e-control.e-calendar .e-content .e-selected, .e-bigger.e-small .e-control.e-calendar .e-content .e-state-hover {
  border-radius: 0;
}

.e-control.e-calendar .e-content span.e-day, .e-bigger.e-small .e-control.e-calendar .e-content span.e-day {
  border-radius: 0;
  cursor: pointer;
  display: inline-block;
  font-size: 12px;
  overflow: hidden;
  padding: 0;
  text-align: center;
  text-decoration: none;
  vertical-align: middle;
}

.e-control.e-calendar .e-content th, .e-control.e-calendar .e-content td, .e-bigger.e-small .e-control.e-calendar .e-content th, .e-bigger.e-small .e-control.e-calendar .e-content td {
  box-sizing: border-box;
}

.e-control.e-calendar .e-content td.e-disabled, .e-bigger.e-small .e-control.e-calendar .e-content td.e-disabled {
  opacity: 0.35;
  pointer-events: none;
  -ms-touch-action: none;
      touch-action: none;
}

.e-control.e-calendar .e-content td, .e-bigger.e-small .e-control.e-calendar .e-content td {
  cursor: pointer;
  padding: 0;
  text-align: center;
}

.e-control.e-calendar .e-content td.e-week-number, .e-bigger.e-small .e-control.e-calendar .e-content td.e-week-number {
  color: #333;
  font-family: "Segoe UI", "GeezaPro", "DejaVu Serif", sans-serif, "-apple-system", "BlinkMacSystemFont";
  font-size: 12px;
  font-weight: 500;
}

.e-control.e-calendar .e-content td.e-overlay, .e-bigger.e-small .e-control.e-calendar .e-content td.e-overlay {
  background: none;
  width: initial;
}

.e-control.e-calendar .e-content table, .e-bigger.e-small .e-control.e-calendar .e-content table {
  border-collapse: separate;
  border-spacing: 0;
  border-width: 0;
  float: left;
  margin: 0;
  outline: 0;
  padding: 0 10px 10px 10px;
  table-layout: fixed;
  width: 100%;
}

.e-control.e-calendar .e-content td.e-other-month > span.e-day, .e-control.e-calendar .e-content td.e-other-year > span.e-day, .e-bigger.e-small .e-control.e-calendar .e-content td.e-other-month > span.e-day, .e-bigger.e-small .e-control.e-calendar .e-content td.e-other-year > span.e-day {
  display: "";
  font-weight: 500;
}

.e-control.e-calendar .e-content tr.e-month-hide, .e-bigger.e-small .e-control.e-calendar .e-content tr.e-month-hide {
  display: none;
  font-weight: 500;
}

.e-control.e-calendar .e-content tr.e-month-hide, .e-control.e-calendar .e-content td.e-other-month, .e-control.e-calendar .e-content td.e-other-year, .e-bigger.e-small .e-control.e-calendar .e-content tr.e-month-hide, .e-bigger.e-small .e-control.e-calendar .e-content td.e-other-month, .e-bigger.e-small .e-control.e-calendar .e-content td.e-other-year {
  pointer-events: initial;
  -ms-touch-action: initial;
      touch-action: initial;
}

.e-control.e-calendar .e-content tr.e-month-hide, .e-control.e-calendar .e-content td.e-other-month.e-disabled, .e-control.e-calendar .e-content td.e-other-year.e-disabled, .e-bigger.e-small .e-control.e-calendar .e-content tr.e-month-hide, .e-bigger.e-small .e-control.e-calendar .e-content td.e-other-month.e-disabled, .e-bigger.e-small .e-control.e-calendar .e-content td.e-other-year.e-disabled {
  pointer-events: none;
  -ms-touch-action: none;
      touch-action: none;
}

.e-control.e-calendar .e-content td.e-week-number:hover span.e-day, .e-control.e-calendar .e-content td.e-week-number:hover, .e-bigger.e-small .e-control.e-calendar .e-content td.e-week-number:hover span.e-day, .e-bigger.e-small .e-control.e-calendar .e-content td.e-week-number:hover {
  background-color: #fff;
  cursor: default;
}

.e-control.e-calendar .e-header .e-prev, .e-control.e-calendar .e-header .e-next, .e-bigger.e-small .e-control.e-calendar .e-header .e-prev, .e-bigger.e-small .e-control.e-calendar .e-header .e-next {
  border-radius: 0;
  display: inline-block;
  font-size: 16px;
  vertical-align: middle;
}

.e-control.e-calendar .e-header .e-title, .e-bigger.e-small .e-control.e-calendar .e-header .e-title {
  cursor: pointer;
  display: inline-block;
  float: left;
  font-size: 17px;
  font-weight: 300;
  text-align: left;
}

.e-control.e-calendar .e-header .e-title, .e-bigger.e-small .e-control.e-calendar .e-header .e-title {
  margin-left: 7px;
}

.e-control.e-calendar .e-header .e-prev:hover, .e-control.e-calendar .e-header .e-next:hover, .e-bigger.e-small .e-control.e-calendar .e-header .e-prev:hover, .e-bigger.e-small .e-control.e-calendar .e-header .e-next:hover {
  cursor: pointer;
}

.e-control.e-calendar .e-header .e-prev.e-overlay, .e-control.e-calendar .e-header .e-next.e-overlay, .e-bigger.e-small .e-control.e-calendar .e-header .e-prev.e-overlay, .e-bigger.e-small .e-control.e-calendar .e-header .e-next.e-overlay {
  background: none;
}

.e-control.e-calendar .e-header.e-decade .e-title, .e-control.e-calendar .e-header.e-year .e-title, .e-bigger.e-small .e-control.e-calendar .e-header.e-decade .e-title, .e-bigger.e-small .e-control.e-calendar .e-header.e-year .e-title {
  margin-left: 7px;
}

.e-control.e-calendar .e-header.e-decade .e-title, .e-bigger.e-small .e-control.e-calendar .e-header.e-decade .e-title {
  cursor: default;
}

.e-control.e-calendar .e-header .e-icon-container, .e-bigger.e-small .e-control.e-calendar .e-header .e-icon-container {
  display: inline-block;
  float: right;
}

.e-control.e-calendar .e-footer-container, .e-bigger.e-small .e-control.e-calendar .e-footer-container {
  text-transform: uppercase;
}

.e-control.e-calendar, .e-bigger.e-small .e-control.e-calendar {
  max-width: 232px;
  min-width: 232px;
  padding: 0;
}

.e-control.e-calendar.e-calendar-day-header-lg, .e-bigger.e-small .e-control.e-calendar.e-calendar-day-header-lg {
  max-width: 100%;
  min-width: 540px;
}

.e-control.e-calendar.e-week-number, .e-bigger.e-small .e-control.e-calendar.e-week-number {
  min-width: 262px;
}

.e-control.e-calendar.e-week, .e-bigger.e-small .e-control.e-calendar.e-week {
  max-width: 232px;
  min-width: 232px;
}

.e-control.e-calendar .e-header .e-title, .e-bigger.e-small .e-control.e-calendar .e-header .e-title {
  line-height: 36px;
}

.e-control.e-calendar.e-rtl .e-header .e-title, .e-bigger.e-small .e-control.e-calendar.e-rtl .e-header .e-title {
  text-align: right;
  text-indent: 4px;
}

.e-control.e-calendar .e-header, .e-bigger.e-small .e-control.e-calendar .e-header {
  height: 36px;
}

.e-control.e-calendar .e-header.e-month, .e-bigger.e-small .e-control.e-calendar .e-header.e-month {
  padding: 10px 10px 0 10px;
}

.e-control.e-calendar .e-header.e-year, .e-control.e-calendar .e-header.e-decade, .e-bigger.e-small .e-control.e-calendar .e-header.e-year, .e-bigger.e-small .e-control.e-calendar .e-header.e-decade {
  padding: 10px;
}

.e-control.e-calendar th, .e-bigger.e-small .e-control.e-calendar th {
  font-weight: 500;
  height: 30px;
}

.e-control.e-calendar .e-content .e-selected, .e-control.e-calendar .e-content .e-state-hover, .e-bigger.e-small .e-control.e-calendar .e-content .e-selected, .e-bigger.e-small .e-control.e-calendar .e-content .e-state-hover {
  border-radius: 0;
}

.e-control.e-calendar .e-content span.e-day, .e-bigger.e-small .e-control.e-calendar .e-content span.e-day {
  border: none;
  font-size: 12px;
  font-weight: 500;
  height: 30px;
  line-height: 30px;
  width: 30px;
}

.e-control.e-calendar .e-content.e-year table, .e-control.e-calendar .e-content.e-decade table, .e-bigger.e-small .e-control.e-calendar .e-content.e-year table, .e-bigger.e-small .e-control.e-calendar .e-content.e-decade table {
  border-spacing: 0;
  padding: 0 6px 0;
}

.e-control.e-calendar .e-content.e-month td, .e-bigger.e-small .e-control.e-calendar .e-content.e-month td {
  height: 30px;
  padding: 0;
}

.e-control.e-calendar .e-content .tfooter > tr > td, .e-bigger.e-small .e-control.e-calendar .e-content .tfooter > tr > td {
  height: 36px;
  line-height: 36px;
}

.e-control.e-calendar .e-content.e-year td, .e-control.e-calendar .e-content.e-decade td, .e-bigger.e-small .e-control.e-calendar .e-content.e-year td, .e-bigger.e-small .e-control.e-calendar .e-content.e-decade td {
  height: 45px;
  padding: 0 0 10px 0;
}

.e-control.e-calendar .e-content.e-year td > span.e-day, .e-control.e-calendar .e-content.e-decade td > span.e-day, .e-bigger.e-small .e-control.e-calendar .e-content.e-year td > span.e-day, .e-bigger.e-small .e-control.e-calendar .e-content.e-decade td > span.e-day {
  font-weight: normal;
  height: 45px;
  line-height: 45px;
  width: 45px;
}

.e-control.e-calendar .e-header .e-icon-container .e-prev, .e-control.e-calendar .e-header .e-icon-container .e-next, .e-bigger.e-small .e-control.e-calendar .e-header .e-icon-container .e-prev, .e-bigger.e-small .e-control.e-calendar .e-header .e-icon-container .e-next {
  height: 30px;
  width: 30px;
}

.e-control.e-calendar .e-footer-container, .e-bigger.e-small .e-control.e-calendar .e-footer-container {
  border-top: none;
  cursor: default;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-direction: row;
      flex-direction: row;
  -ms-flex-pack: end;
      justify-content: flex-end;
  padding: 0 10px 10px 10px;
  text-align: center;
  width: 100%;
}

.e-small.e-bigger.e-control.e-calendar .e-content.e-year span.e-day, .e-small.e-bigger .e-control.e-calendar .e-content.e-year span.e-day {
  font-size: 12px;
}

.e-small.e-bigger.e-control.e-calendar .e-content.e-month table, .e-small.e-bigger .e-control.e-calendar .e-content.e-month table {
  padding: 0 10px 10px 10px;
}

.e-bigger.e-control.e-calendar,
.e-control.e-bigger .e-control.e-calendar {
  max-width: 288px;
  min-width: 288px;
  padding: 0;
}

.e-bigger.e-control.e-calendar.e-calendar-day-header-lg,
.e-control.e-bigger .e-control.e-calendar.e-calendar-day-header-lg {
  max-width: 100%;
  min-width: 540px;
}

.e-bigger.e-control.e-calendar.e-week,
.e-control.e-bigger .e-control.e-calendar.e-week {
  max-width: 288px;
  min-width: 288px;
}

.e-bigger.e-control.e-calendar.e-week-number,
.e-control.e-bigger .e-control.e-calendar.e-week-number {
  min-width: 320px;
}

.e-bigger.e-control.e-calendar .e-header .e-title,
.e-control.e-bigger .e-control.e-calendar .e-header .e-title {
  font-size: 19px;
  line-height: 48px;
  width: 60%;
}

.e-bigger.e-control.e-calendar.e-rtl .e-header .e-title,
.e-control.e-bigger .e-control.e-calendar.e-rtl .e-header .e-title {
  line-height: 48px;
  text-indent: 6px;
}

.e-bigger.e-control.e-calendar .e-header,
.e-control.e-bigger .e-control.e-calendar .e-header {
  height: 48px;
  padding: 12px 12px 0 12px;
}

.e-bigger.e-control.e-calendar .e-header span,
.e-control.e-bigger .e-control.e-calendar .e-header span {
  font-size: 18px;
  padding: 13px;
}

.e-bigger.e-control.e-calendar .e-header.e-year, .e-bigger.e-control.e-calendar .e-header.e-decade,
.e-control.e-bigger .e-control.e-calendar .e-header.e-year,
.e-control.e-bigger .e-control.e-calendar .e-header.e-decade {
  padding: 12px 12px 10px 12px;
}

.e-bigger.e-control.e-calendar th,
.e-control.e-bigger .e-control.e-calendar th {
  font-size: 15px;
  height: 37px;
}

.e-bigger.e-control.e-calendar .e-content.e-year span.e-day,
.e-control.e-bigger .e-control.e-calendar .e-content.e-year span.e-day {
  font-size: 14px;
  font-weight: normal;
}

.e-bigger.e-control.e-calendar .e-content.e-month table,
.e-control.e-bigger .e-control.e-calendar .e-content.e-month table {
  padding: 0 12px 12px 12px;
}

.e-bigger.e-control.e-calendar .e-content.e-year table, .e-bigger.e-control.e-calendar .e-content.e-decade table,
.e-control.e-bigger .e-control.e-calendar .e-content.e-year table,
.e-control.e-bigger .e-control.e-calendar .e-content.e-decade table {
  padding: 0 8.5px 3.5px 8.5px;
}

.e-bigger.e-control.e-calendar .e-content .e-selected, .e-bigger.e-control.e-calendar .e-content .e-state-hover,
.e-control.e-bigger .e-control.e-calendar .e-content .e-selected,
.e-control.e-bigger .e-control.e-calendar .e-content .e-state-hover {
  border-radius: 0;
}

.e-bigger.e-control.e-calendar .e-content span.e-day,
.e-control.e-bigger .e-control.e-calendar .e-content span.e-day {
  font-size: 14px;
  height: 37px;
  line-height: 37px;
  width: 37px;
}

.e-bigger.e-control.e-calendar .e-content.e-month td,
.e-control.e-bigger .e-control.e-calendar .e-content.e-month td {
  height: 0;
  padding: 0;
}

.e-bigger.e-control.e-calendar .e-content.e-year td, .e-bigger.e-control.e-calendar .e-content.e-decade td,
.e-control.e-bigger .e-control.e-calendar .e-content.e-year td,
.e-control.e-bigger .e-control.e-calendar .e-content.e-decade td {
  height: 54px;
  padding: 0 0 13px 0;
}

.e-bigger.e-control.e-calendar .e-content.e-year td > span.e-day, .e-bigger.e-control.e-calendar .e-content.e-decade td > span.e-day,
.e-control.e-bigger .e-control.e-calendar .e-content.e-year td > span.e-day,
.e-control.e-bigger .e-control.e-calendar .e-content.e-decade td > span.e-day {
  height: 54px;
  line-height: 54px;
  width: 54px;
}

.e-bigger.e-control.e-calendar .e-header .e-icon-container .e-prev, .e-bigger.e-control.e-calendar .e-header .e-icon-container .e-next,
.e-control.e-bigger .e-control.e-calendar .e-header .e-icon-container .e-prev,
.e-control.e-bigger .e-control.e-calendar .e-header .e-icon-container .e-next {
  height: 45px;
  width: 45px;
}

.e-bigger.e-control.e-calendar .e-footer-container,
.e-control.e-bigger .e-control.e-calendar .e-footer-container {
  border-top: none;
  padding: 0 10px 10px 10px;
}

.e-small.e-control.e-calendar, .e-small .e-control.e-calendar {
  max-width: 232px;
  min-width: 232px;
  padding: 0;
}

.e-small.e-control.e-calendar.e-calendar-day-header-lg, .e-small .e-control.e-calendar.e-calendar-day-header-lg {
  max-width: 100%;
  min-width: 540px;
}

.e-small.e-control.e-calendar .e-content span.e-day, .e-small .e-control.e-calendar .e-content span.e-day {
  font-size: 12px;
  height: 24px;
  line-height: 24px;
  width: 24px;
}

.e-small.e-control.e-calendar .e-content.e-month td, .e-small .e-control.e-calendar .e-content.e-month td {
  height: 24px;
}

.e-small.e-control.e-calendar .e-header, .e-small .e-control.e-calendar .e-header {
  height: 32px;
}

.e-small.e-control.e-calendar .e-header span, .e-small .e-control.e-calendar .e-header span {
  font-size: 12px;
  padding: 6px;
}

.e-small.e-control.e-calendar .e-header .e-title, .e-small .e-control.e-calendar .e-header .e-title {
  font-size: 14px;
  line-height: 32px;
}

.e-small.e-control.e-calendar .e-header .e-icon-container .e-prev, .e-small.e-control.e-calendar .e-header .e-icon-container .e-next, .e-small .e-control.e-calendar .e-header .e-icon-container .e-prev, .e-small .e-control.e-calendar .e-header .e-icon-container .e-next {
  height: 28px;
  width: 28px;
}

.e-small.e-control.e-calendar th, .e-small .e-control.e-calendar th {
  font-size: 12px;
  height: 24px;
}

.e-control.e-calendar .e-btn.e-today.e-flat.e-disabled, .e-control.e-calendar .e-btn.e-today.e-flat.e-disabled:hover, .e-control.e-calendar .e-btn.e-today.e-flat.e-disabled:active, .e-control.e-calendar .e-btn.e-today.e-flat.e-disabled:focus, .e-control.e-calendar .e-btn.e-today.e-flat.e-disabled:hover:active {
  background: #f4f4f4;
  border-color: #f4f4f4;
  box-shadow: none;
  color: #a6a6a6;
  cursor: default;
  opacity: 0.35;
  outline: none;
  pointer-events: none;
  -ms-touch-action: none;
      touch-action: none;
}

.e-content-placeholder.e-calendar.e-placeholder-calendar {
  background-size: 250px 336px;
  min-height: 336px;
}

.e-bigger .e-content-placeholder.e-calendar.e-placeholder-calendar, .e-bigger.e-content-placeholder.e-calendar.e-placeholder-calendar {
  background-size: 300px 392px;
  min-height: 392px;
}

.e-control.e-calendar, .e-bigger.e-small .e-control.e-calendar {
  background-color: #fff;
  border: 1px solid #eaeaea;
  box-shadow: none;
}

.e-control.e-calendar .e-date-icon-prev, .e-control.e-calendar .e-date-icon-next, .e-bigger.e-small .e-control.e-calendar .e-date-icon-prev, .e-bigger.e-small .e-control.e-calendar .e-date-icon-next {
  color: #333;
}

.e-control.e-calendar th, .e-bigger.e-small .e-control.e-calendar th {
  border-bottom: 0;
  color: #333;
}

.e-control.e-calendar .e-header, .e-bigger.e-small .e-control.e-calendar .e-header {
  border-bottom: 0;
}

.e-control.e-calendar .e-header a span, .e-bigger.e-small .e-control.e-calendar .e-header a span {
  border: none;
  color: #333;
}

.e-control.e-calendar .e-header .e-title, .e-bigger.e-small .e-control.e-calendar .e-header .e-title {
  color: #0078d6;
}

.e-control.e-calendar .e-header .e-title:hover, .e-bigger.e-small .e-control.e-calendar .e-header .e-title:hover {
  color: #00457a;
  cursor: pointer;
  text-decoration: none;
}

.e-control.e-calendar .e-header .e-prev:hover > span, .e-control.e-calendar .e-header .e-next:hover > span, .e-bigger.e-small .e-control.e-calendar .e-header .e-prev:hover > span, .e-bigger.e-small .e-control.e-calendar .e-header .e-next:hover > span {
  border: none;
  color: #000;
  cursor: pointer;
}

.e-control.e-calendar .e-header .e-prev:hover, .e-control.e-calendar .e-header .e-next:hover, .e-bigger.e-small .e-control.e-calendar .e-header .e-prev:hover, .e-bigger.e-small .e-control.e-calendar .e-header .e-next:hover {
  background: none;
}

.e-control.e-calendar .e-header .e-prev:active, .e-control.e-calendar .e-header .e-next:active, .e-bigger.e-small .e-control.e-calendar .e-header .e-prev:active, .e-bigger.e-small .e-control.e-calendar .e-header .e-next:active {
  background: #fff;
  color: #fff;
}

.e-control.e-calendar .e-header button.e-prev:active span, .e-control.e-calendar .e-header button.e-next:active span, .e-bigger.e-small .e-control.e-calendar .e-header button.e-prev:active span, .e-bigger.e-small .e-control.e-calendar .e-header button.e-next:active span {
  border: none;
  color: #333;
}

.e-control.e-calendar .e-header.e-decade .e-title, .e-bigger.e-small .e-control.e-calendar .e-header.e-decade .e-title {
  color: #000;
  cursor: default;
}

.e-control.e-calendar .e-header .e-next.e-disabled span, .e-control.e-calendar .e-header .e-prev.e-disabled span, .e-bigger.e-small .e-control.e-calendar .e-header .e-next.e-disabled span, .e-bigger.e-small .e-control.e-calendar .e-header .e-prev.e-disabled span {
  color: #000;
  font-weight: 500;
}

.e-control.e-calendar .e-header .e-next.e-disabled, .e-control.e-calendar .e-header .e-prev.e-disabled, .e-bigger.e-small .e-control.e-calendar .e-header .e-next.e-disabled, .e-bigger.e-small .e-control.e-calendar .e-header .e-prev.e-disabled {
  opacity: 0.35;
}

.e-control.e-calendar .e-content.e-decade tr:first-child .e-cell:first-child span.e-day, .e-control.e-calendar .e-content.e-decade tr:last-child .e-cell:last-child span.e-day, .e-bigger.e-small .e-control.e-calendar .e-content.e-decade tr:first-child .e-cell:first-child span.e-day, .e-bigger.e-small .e-control.e-calendar .e-content.e-decade tr:last-child .e-cell:last-child span.e-day {
  color: #a6a6a6;
}

.e-control.e-calendar .e-content.e-decade tr:first-child .e-cell:first-child.e-selected span.e-day, .e-control.e-calendar .e-content.e-decade tr:last-child .e-cell:last-child.e-selected span.e-day, .e-bigger.e-small .e-control.e-calendar .e-content.e-decade tr:first-child .e-cell:first-child.e-selected span.e-day, .e-bigger.e-small .e-control.e-calendar .e-content.e-decade tr:last-child .e-cell:last-child.e-selected span.e-day {
  color: #fff;
}

.e-control.e-calendar .e-content.e-decade tr:first-child .e-cell.e-disabled:first-child span.e-day, .e-control.e-calendar .e-content.e-decade tr:last-child .e-cell.e-disabled:last-child span.e-day, .e-bigger.e-small .e-control.e-calendar .e-content.e-decade tr:first-child .e-cell.e-disabled:first-child span.e-day, .e-bigger.e-small .e-control.e-calendar .e-content.e-decade tr:last-child .e-cell.e-disabled:last-child span.e-day {
  color: #000;
}

.e-control.e-calendar .e-content.e-year td:hover span.e-day, .e-control.e-calendar .e-content.e-decade td:hover span.e-day, .e-bigger.e-small .e-control.e-calendar .e-content.e-year td:hover span.e-day, .e-bigger.e-small .e-control.e-calendar .e-content.e-decade td:hover span.e-day {
  background-color: #dadada;
}

.e-control.e-calendar .e-content.e-year td.e-selected:hover span.e-day, .e-control.e-calendar .e-content.e-decade td.e-selected:hover span.e-day, .e-bigger.e-small .e-control.e-calendar .e-content.e-year td.e-selected:hover span.e-day, .e-bigger.e-small .e-control.e-calendar .e-content.e-decade td.e-selected:hover span.e-day {
  background-color: #0078d6;
}

.e-control.e-calendar .e-content.e-year td > span.e-day, .e-control.e-calendar .e-content.e-decade td > span.e-day, .e-bigger.e-small .e-control.e-calendar .e-content.e-year td > span.e-day, .e-bigger.e-small .e-control.e-calendar .e-content.e-decade td > span.e-day {
  background: #f4f4f4;
}

.e-control.e-calendar .e-content .e-week-number span, .e-bigger.e-small .e-control.e-calendar .e-content .e-week-number span {
  color: #a6a6a6;
}

.e-control.e-calendar .e-content td.e-focused-date span.e-day, .e-control.e-calendar .e-content td.e-focused-date:hover span.e-day, .e-control.e-calendar .e-content td.e-focused-date:focus span.e-day, .e-bigger.e-small .e-control.e-calendar .e-content td.e-focused-date span.e-day, .e-bigger.e-small .e-control.e-calendar .e-content td.e-focused-date:hover span.e-day, .e-bigger.e-small .e-control.e-calendar .e-content td.e-focused-date:focus span.e-day {
  background: #dadada;
  border: none;
  border-radius: 0;
}

.e-control.e-calendar .e-content td.e-focused-date:hover span.e-day, .e-bigger.e-small .e-control.e-calendar .e-content td.e-focused-date:hover span.e-day {
  background-color: #eaeaea;
  border: none;
  border-radius: 0;
  color: #333;
}

.e-control.e-calendar .e-content td.e-today span.e-day, .e-control.e-calendar .e-content td.e-focused-date.e-today span.e-day, .e-bigger.e-small .e-control.e-calendar .e-content td.e-today span.e-day, .e-bigger.e-small .e-control.e-calendar .e-content td.e-focused-date.e-today span.e-day {
  background: #b7e0ff;
  border: none;
  border-radius: 0;
  color: #333;
}

.e-control.e-calendar .e-content td.e-focused-date.e-today span.e-day, .e-bigger.e-small .e-control.e-calendar .e-content td.e-focused-date.e-today span.e-day {
  background: #eaeaea;
  border: none;
  color: #333;
}

.e-control.e-calendar .e-content td.e-today:focus span.e-day, .e-control.e-calendar .e-content td.e-focused-date.e-today:focus span.e-day, .e-bigger.e-small .e-control.e-calendar .e-content td.e-today:focus span.e-day, .e-bigger.e-small .e-control.e-calendar .e-content td.e-focused-date.e-today:focus span.e-day {
  background-color: #deecf9;
  border: none;
  border-radius: 0;
  color: #333;
}

.e-control.e-calendar .e-content td.e-today:hover span.e-day, .e-control.e-calendar .e-content td.e-focused-date.e-today:hover span.e-day, .e-control.e-calendar .e-content td.e-focused-date.e-today:focus span.e-day, .e-bigger.e-small .e-control.e-calendar .e-content td.e-today:hover span.e-day, .e-bigger.e-small .e-control.e-calendar .e-content td.e-focused-date.e-today:hover span.e-day, .e-bigger.e-small .e-control.e-calendar .e-content td.e-focused-date.e-today:focus span.e-day {
  background-color: #eaeaea;
  border: none;
  color: #333;
}

.e-control.e-calendar .e-content td.e-today.e-selected span.e-day, .e-bigger.e-small .e-control.e-calendar .e-content td.e-today.e-selected span.e-day {
  background-color: #0078d6;
  border: none;
  box-shadow: none;
  color: #fff;
}

.e-control.e-calendar .e-content td.e-today.e-selected:hover span.e-day, .e-control.e-calendar .e-content td.e-selected:hover span.e-day, .e-control.e-calendar .e-content td.e-selected.e-focused-date span.e-day, .e-bigger.e-small .e-control.e-calendar .e-content td.e-today.e-selected:hover span.e-day, .e-bigger.e-small .e-control.e-calendar .e-content td.e-selected:hover span.e-day, .e-bigger.e-small .e-control.e-calendar .e-content td.e-selected.e-focused-date span.e-day {
  background-color: #005ba3;
  color: #fff;
}

.e-control.e-calendar .e-content span, .e-bigger.e-small .e-control.e-calendar .e-content span {
  color: #333;
}

.e-control.e-calendar .e-content .e-disabled span.e-day:hover, .e-bigger.e-small .e-control.e-calendar .e-content .e-disabled span.e-day:hover {
  background: none;
  border: 0;
  color: #000;
}

.e-control.e-calendar .e-content .e-other-month:hover span.e-day, .e-bigger.e-small .e-control.e-calendar .e-content .e-other-month:hover span.e-day {
  color: #000;
}

.e-control.e-calendar .e-content .e-other-month span.e-day, .e-control.e-calendar .e-content .e-other-month.e-today span.e-day, .e-bigger.e-small .e-control.e-calendar .e-content .e-other-month span.e-day, .e-bigger.e-small .e-control.e-calendar .e-content .e-other-month.e-today span.e-day {
  color: #a6a6a6;
}

.e-control.e-calendar .e-content .e-other-month.e-today:hover span.e-day, .e-bigger.e-small .e-control.e-calendar .e-content .e-other-month.e-today:hover span.e-day {
  background-color: #eaeaea;
  color: #a6a6a6;
}

.e-control.e-calendar .e-content thead, .e-bigger.e-small .e-control.e-calendar .e-content thead {
  background: none;
  border-bottom: 0;
}

.e-control.e-calendar .e-content td:hover span.e-day, .e-control.e-calendar .e-content td:focus span.e-day, .e-bigger.e-small .e-control.e-calendar .e-content td:hover span.e-day, .e-bigger.e-small .e-control.e-calendar .e-content td:focus span.e-day {
  background-color: #eaeaea;
  border: none;
  border-radius: 0;
  color: #333;
}

.e-control.e-calendar .e-content td:focus span.e-day, .e-bigger.e-small .e-control.e-calendar .e-content td:focus span.e-day {
  background-color: #deecf9;
  border: none;
  border-radius: 0;
  color: #333;
}

.e-control.e-calendar .e-content td.e-disabled span.e-day, .e-control.e-calendar .e-content td.e-disabled:hover span.e-day, .e-control.e-calendar .e-content td.e-disabled:focus span.e-day, .e-bigger.e-small .e-control.e-calendar .e-content td.e-disabled span.e-day, .e-bigger.e-small .e-control.e-calendar .e-content td.e-disabled:hover span.e-day, .e-bigger.e-small .e-control.e-calendar .e-content td.e-disabled:focus span.e-day {
  background: none;
  border: none;
  color: #000;
}

.e-control.e-calendar .e-content td.e-selected span.e-day, .e-bigger.e-small .e-control.e-calendar .e-content td.e-selected span.e-day {
  background-color: #0078d6;
  border: none;
  border-radius: 0;
  color: #fff;
}

.e-control.e-calendar .e-content .e-footer, .e-bigger.e-small .e-control.e-calendar .e-content .e-footer {
  color: #0078d6;
}

.e-control.e-calendar.e-device .e-prev:hover, .e-control.e-calendar.e-device .e-next:hover, .e-control.e-calendar.e-device .e-prev:active, .e-control.e-calendar.e-device .e-next:active, .e-control.e-calendar.e-device .e-prev:focus, .e-control.e-calendar.e-device .e-next:focus, .e-bigger.e-small .e-control.e-calendar.e-device .e-prev:hover, .e-bigger.e-small .e-control.e-calendar.e-device .e-next:hover, .e-bigger.e-small .e-control.e-calendar.e-device .e-prev:active, .e-bigger.e-small .e-control.e-calendar.e-device .e-next:active, .e-bigger.e-small .e-control.e-calendar.e-device .e-prev:focus, .e-bigger.e-small .e-control.e-calendar.e-device .e-next:focus {
  background: none;
}

.e-control.e-calendar.e-device button.e-prev:active span, .e-control.e-calendar.e-device button.e-next:active span, .e-bigger.e-small .e-control.e-calendar.e-device button.e-prev:active span, .e-bigger.e-small .e-control.e-calendar.e-device button.e-next:active span {
  color: #333;
}

.e-small.e-control.e-calendar .e-header .e-title, .e-small .e-control.e-calendar .e-header .e-title {
  color: #0078d6;
}

.e-zoomin {
  animation: animatezoom .3s;
}

@keyframes animatezoom {
  from {
    transform: scale(0);
  }
  to {
    transform: scale(1);
  }
}

.e-control .e-js [class^='e-'], .e-control .e-js [class*=' e-'] {
  box-sizing: content-box;
}
