ejs-sidebar {
  display: block;
}

.e-sidebar {
  -webkit-tap-highlight-color: transparent;
  background: #fff;
  height: 100%;
  overflow: auto;
  position: fixed;
  top: 0;
  transition: none;
  vertical-align: middle;
  visibility: hidden;
  will-change: transform;
}

.e-sidebar.e-right.e-open {
  transform: translateX(0%);
  transition: transform .5s ease;
  visibility: visible;
}

.e-sidebar.e-right.e-close {
  box-shadow: none;
  transform: translateX(100%);
  transition: transform .5s ease, visibility 500ms;
  visibility: hidden;
}

.e-sidebar.e-left.e-open {
  transform: translateX(0%);
  transition: transform .5s ease;
  visibility: visible;
}

.e-sidebar.e-left.e-transition.e-close, .e-sidebar.e-right.e-transition.e-close {
  transition: transform .5s ease, visibility 500ms;
}

.e-sidebar.e-left.e-close {
  box-shadow: none;
  transform: translateX(-100%);
  visibility: hidden;
}

.e-sidebar.e-right.e-close {
  box-shadow: none;
  transform: translateX(100%);
  transition: transform .5s ease, visibility 500ms;
  visibility: hidden;
}

.e-sidebar.e-right {
  border-left: 1px solid #eaeaea;
  left: auto;
  right: 0;
  top: 0;
}

.e-sidebar.e-left {
  border-right: 1px solid #eaeaea;
  left: 0;
  right: auto;
  top: 0;
}

.e-sidebar.e-left.e-close.e-dock {
  transform: translateX(0%);
  transition: width .5s ease, visibility 500ms;
  visibility: visible;
}

.e-sidebar.e-right.e-close.e-dock {
  transform: translateX(0%);
  transition: width .5s ease, visibility 500ms;
  visibility: visible;
}

.e-sidebar.e-left.e-open.e-disable-animation, .e-sidebar.e-right.e-open.e-disable-animation, .e-sidebar.e-right.e-close.e-disable-animation, .e-sidebar.e-left.e-close.e-disable-animation {
  transition: none;
}

.e-sidebar.e-visibility {
  visibility: hidden;
}

.e-sidebar.e-over {
  box-shadow: 0 0 30px rgba(0, 0, 0, 0.3);
}

.e-sidebar-absolute {
  position: absolute;
}

.e-sidebar-context {
  overflow-x: hidden;
  position: relative;
}

.e-backdrop {
  background-color: rgba(0, 0, 0, 0.6);
  height: 100%;
  left: 0;
  opacity: .5;
  pointer-events: auto;
  top: 0;
  width: auto;
  z-index: 999;
}

.e-content-animation {
  transition: margin .5s ease, transform .5s ease;
}

.e-content-animation.e-overlay {
  box-sizing: border-box;
  overflow: auto;
  -webkit-user-select: none;
     -moz-user-select: none;
      -ms-user-select: none;
          user-select: none;
}

.e-disable-interaction {
  pointer-events: none;
  -ms-touch-action: none;
      touch-action: none;
}

.e-sidebar-overflow {
  overflow-x: hidden !important;
}

.e-sidebar-overlay {
  background-color: rgba(0, 0, 0, 0.6);
  height: 100%;
  left: 0;
  opacity: .5;
  pointer-events: auto;
  position: fixed;
  top: 0;
  width: 100%;
  z-index: 999;
}

.e-content-placeholder.e-sidebar.e-placeholder-sidebar {
  left: 0;
  position: fixed;
  right: auto;
  top: 0;
  visibility: visible;
}
