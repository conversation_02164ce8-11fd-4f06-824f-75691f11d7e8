@charset "UTF-8";
.e-bigger .e-ddl.e-popup .e-input-group .e-clear-icon {
  height: 36px;
}

.e-ddl.e-control.e-popup {
  border: 0;
  box-shadow: 0 0 5px 0 rgba(0, 0, 0, 0.4);
  margin-top: 1px;
}

.e-ddl.e-control.e-popup .e-content.e-nodata {
  background-color: #000;
}

.e-ddl.e-control.e-popup .e-dropdownbase .e-list-item .e-highlight {
  color: #ffd939;
}

.e-ddl.e-control.e-popup .e-input-group {
  display: -ms-flexbox;
  display: flex;
  width: auto;
}

.e-ddl.e-control.e-popup .e-input-group input {
  line-height: 15px;
}

.e-ddl.e-control.e-popup .e-input-group .e-clear-icon {
  border-radius: 20px;
  height: 20px;
  margin: 5px;
  min-width: 20px;
}

.e-ddl.e-control.e-popup .e-input-group .e-clear-icon::before {
  font-size: 10px;
}

.e-ddl.e-control.e-popup .e-filter-parent {
  border-left-width: 0;
  border-right-width: 0;
}

.e-ddl.e-control.e-popup .e-filter-parent .e-input-group.e-control-wrapper:hover:active {
  border-color: #fff;
}

.e-bigger .e-ddl.e-control.e-popup.e-ddl-device .e-input-group {
  margin: 0 0 0 55px;
}

.e-bigger .e-ddl.e-control.e-popup.e-ddl-device-filter {
  margin-top: 0;
}

.e-bigger .e-ddl.e-control.e-popup .e-list-item, .e-bigger .e-ddl.e-control.e-popup .e-list-group-item, .e-bigger .e-ddl.e-control.e-popup .e-fixed-head {
  font-size: 15px;
  line-height: 45px;
  padding-left: 0;
  text-indent: 16px;
}

.e-bigger .e-ddl.e-control.e-popup .e-dd-group .e-list-item {
  padding-left: 4px;
}

.e-bigger .e-ddl.e-control.e-popup .e-input-group {
  padding: 4px 0;
}

.e-bigger .e-ddl.e-control.e-popup .e-input-group input {
  height: 30px;
}

.e-bigger.e-small .e-ddl.e-control.e-popup .e-list-item, .e-bigger.e-small .e-ddl.e-control.e-popup .e-list-group-item, .e-bigger.e-small .e-ddl.e-control.e-popup .e-fixed-head {
  font-size: 14px;
  line-height: 40px;
  padding-left: 0;
  text-indent: 16px;
}

.e-bigger.e-small .e-ddl.e-control.e-popup .e-dd-group .e-list-item {
  padding-left: 4px;
}

.e-bigger.e-small .e-ddl.e-control.e-popup .e-input-group {
  padding: 0;
}

.e-bigger.e-small .e-ddl.e-control.e-popup .e-input-group input {
  height: 34px;
}

@keyframes material-spinner-rotate {
  0% {
    transform: rotate(0);
  }
  100% {
    transform: rotate(360deg);
  }
}

@keyframes fabric-spinner-rotate {
  0% {
    transform: rotate(0);
  }
  100% {
    transform: rotate(360deg);
  }
}

.e-treegrid .e-treegridexpand::before, .e-treegrid .e-treegridcollapse::before {
  content: "";
}

.e-treegrid .e-toolbar-item .e-expand::before {
  content: "";
}

.e-treegrid .e-toolbar-item .e-collapse::before {
  content: "";
}

.e-treegrid .e-toolbar-item .e-indent::before {
  content: "";
}

.e-treegrid .e-toolbar-item .e-outdent::before {
  content: "";
}

.e-bigger .e-treegrid .e-treegridexpand, .e-bigger .e-treegrid .e-treegridcollapse {
  height: 18px;
  width: 18px;
}

.e-bigger .e-treegrid .e-rowcell.e-treerowcell {
  padding-left: 9px;
}

.e-bigger .e-treegrid .e-hierarchycheckbox {
  padding-left: 2px;
}

.e-treegrid .e-treegridexpand::before, .e-treegrid .e-treegridcollapse::before {
  vertical-align: middle;
}

.e-treegrid .e-rowcell:not(.e-gridclip) .e-treecolumn-container {
  display: block;
  overflow: hidden;
  text-overflow: ellipsis;
}

.e-treegrid .e-rowcell:not(.e-gridclip) .e-treecolumn-container span.e-treecell {
  display: inline;
  overflow: hidden;
  text-overflow: ellipsis;
}

.e-treegrid .e-rowcell.e-treerowcell {
  padding-left: 5px;
}

.e-treegrid .e-rtl .e-treegridcollapse {
  transform: scale(-1);
}

.e-treegrid .e-treegridexpand {
  transform: rotate(90deg);
}

.e-treegrid .e-treecolumn-container span {
  display: inline-block;
  vertical-align: middle;
}

.e-treegrid .e-treegridexpand, .e-treegrid .e-treegridcollapse {
  color: #fff;
  cursor: pointer;
  font-size: 10px;
  height: 16px;
  text-align: center;
  vertical-align: bottom;
  width: 16px;
}

.e-treegrid .e-treecell {
  display: table-cell;
  line-height: normal;
}

.e-treegrid .e-grid.e-wrap .e-rowcell .e-treecolumn-container {
  white-space: nowrap;
}

.e-treegrid .e-grid.e-wrap .e-rowcell .e-treecolumn-container .e-treecell {
  display: inline-block;
  white-space: normal;
  word-wrap: break-word;
}

.e-treegrid .e-summarycell {
  pointer-events: none;
}

.e-treegrid.e-grid.e-print-grid-layout .e-icons {
  display: inline-block;
}

.e-treegrid .e-treecheckselect, .e-treegrid .e-treeselectall {
  margin: 0;
  opacity: 0;
  position: absolute;
  width: 0;
}

.e-treegrid .e-detailheadercell, .e-treegrid .e-headercontent .e-table .e-detail-intent, .e-treegrid .e-gridcontent .e-table .e-detail-intent, .e-treegrid .e-table .e-detailrow .e-detailindentcell, .e-treegrid .e-table .e-detailrowexpand, .e-treegrid .e-table .e-detailrowcollapse, .e-treegrid .e-filterbarcell.e-mastercell {
  display: none;
}

.e-treegrid .e-icons.e-errorelem {
  display: inline-block;
  padding-left: 10px;
  vertical-align: middle;
}

.e-treegrid .e-errorelem::before {
  color: #e3165b;
  content: '\e22a';
  transform: rotate(180deg);
}

.e-treegrid .e-gridcontent td.e-childborder {
  border-color: #ffd939;
  border-width: 1px 0 0;
  box-shadow: 0 1px 0 0 #ffd939;
  position: relative;
  z-index: 1;
}

.e-treegrid .e-gridcontent td.e-rowcell.e-childborder.e-dragborder, .e-treegrid .e-gridcontent td.e-rowcell.e-dropbottom.e-dragborder td.e-rowdragdrop.e-dropbottom.e-dragborder {
  box-shadow: 0 1px 0 0 #ffd939;
}

.e-treegrid .e-gridcontent td.e-rowcell.e-dragborder, .e-treegrid .e-gridcontent tr.e-row:first-child .e-rowcell.e-dragborder, .e-treegrid .e-gridcontent .e-rowdragdrop.e-dragborder {
  box-shadow: 0 0 0 0;
}

.e-treegrid .e-gridcontent td.e-childborder:first-child {
  border-left: 1px solid #ffd939;
  box-shadow: 0 1px 0 0 #ffd939;
  position: relative;
  z-index: 1;
}

.e-treegrid .e-gridcontent td.e-rowcell.e-childborder:last-child {
  border-right: 1px solid #ffd939;
}

.e-treegrid .e-gridcontent td.e-lastrowcell.e-childborder {
  border-width: 1px 0;
}

.e-treegrid .e-gridcontent td.e-lastrowcell.e-childborder:last-child {
  border-right: 1px solid #ffd939;
}

.e-treegrid .e-gridcontent td.e-lastrowcell.e-childborder:first-child {
  border-left: 1px solid #ffd939;
}

.e-treegrid .e-gridcontent td.e-dropbottom:first-child, .e-treegrid .e-gridcontent td.e-rowcell.e-dropbottom, .e-treegrid .e-gridcontent td.e-dropbottom {
  box-shadow: 0 1px 0 0 #ffd939;
  position: relative;
  z-index: 1;
}

.e-treegrid .e-gridcontent td.e-droptop:first-child, .e-treegrid .e-gridcontent td.e-rowcell.e-droptop {
  border-top-color: #ffd939;
}

.e-treegrid .e-gridcontent td.e-lastrowcell.e-dropbottom {
  border-bottom-color: #ffd939;
}

.e-treegrid .e-gridcontent td.e-rowdragdrop.e-droptop.e-dragborder {
  box-shadow: 0 0 0 0;
}

.e-treegrid .e-gridcontent .e-gridheader .e-firstrow-dragborder {
  height: 1px;
}

.e-treegrid .e-gridcontent .e-lastrow-dragborder {
  height: 0;
}

.e-treegrid .e-gridheader .e-firstrow-dragborder {
  height: 0;
}

.e-treegrid .e-droptop .e-lastrow-dragborder {
  height: 0;
}

.e-treegrid .e-treegrid-relative {
  position: relative;
}

.e-treegrid .e-gridheader .e-firstrow-dragborder tr:first-child.e-dropbottom {
  height: 0;
}

.e-treegrid .e-gridheader .e-firstrow-border, .e-treegrid .e-gridcontent .e-lastrow-border {
  background-color: #ffd939;
  bottom: 0;
  height: 1px;
  position: absolute;
  z-index: 1;
}

.e-control .e-js [class^='e-'], .e-control .e-js [class*=' e-'] {
  box-sizing: content-box;
}
