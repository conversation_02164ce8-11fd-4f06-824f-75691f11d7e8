@import url("https://fonts.googleapis.com/css?family=Roboto:400,500");
.e-control.e-dropdown-btn .e-caret::before, .e-control.e-dropdown-btn.e-btn .e-caret::before {
  content: '\e969';
}

/*! drop-down button layout */
.e-control.e-dropdown-btn, .e-control.e-dropdown-btn.e-btn {
  box-shadow: none;
  text-transform: none;
  white-space: normal;
}

.e-control.e-dropdown-btn:hover, .e-control.e-dropdown-btn:focus, .e-control.e-dropdown-btn.e-btn:hover, .e-control.e-dropdown-btn.e-btn:focus {
  box-shadow: none;
}

.e-control.e-dropdown-btn:active, .e-control.e-dropdown-btn.e-btn:active {
  box-shadow: 0 5px 5px -3px rgba(0, 0, 0, 0.2), 0 8px 10px 1px rgba(0, 0, 0, 0.14), 0 3px 14px 2px rgba(0, 0, 0, 0.12);
}

.e-control.e-dropdown-btn .e-btn-icon, .e-control.e-dropdown-btn.e-btn .e-btn-icon {
  font-size: 14px;
}

.e-control.e-dropdown-btn .e-caret, .e-control.e-dropdown-btn.e-btn .e-caret {
  font-size: 8px;
}

.e-control.e-dropdown-btn.e-vertical, .e-control.e-dropdown-btn.e-btn.e-vertical {
  line-height: 1;
  padding: 12px 12px;
}

.e-control.e-dropdown-btn.e-caret-hide .e-caret, .e-control.e-dropdown-btn.e-btn.e-caret-hide .e-caret {
  display: none;
}

.e-control.e-dropdown-btn .e-control.e-dropdown-btn.e-small .e-control.e-dropdown-btn .e-control.e-dropdown-btn.e-small, .e-control.e-dropdown-btn .e-control.e-dropdown-btn.e-small .e-control.e-dropdown-btn .e-control.e-dropdown-btn.e-btn.e-small, .e-control.e-dropdown-btn .e-control.e-dropdown-btn.e-small .e-control.e-dropdown-btn.e-btn .e-control.e-dropdown-btn.e-small, .e-control.e-dropdown-btn .e-control.e-dropdown-btn.e-small .e-control.e-dropdown-btn.e-btn .e-control.e-dropdown-btn.e-btn.e-small.e-dropdown-popup ul, .e-control.e-dropdown-btn .e-control.e-dropdown-btn.e-btn.e-small .e-control.e-dropdown-btn .e-control.e-dropdown-btn.e-small, .e-control.e-dropdown-btn .e-control.e-dropdown-btn.e-btn.e-small .e-control.e-dropdown-btn .e-control.e-dropdown-btn.e-btn.e-small, .e-control.e-dropdown-btn .e-control.e-dropdown-btn.e-btn.e-small .e-control.e-dropdown-btn.e-btn .e-control.e-dropdown-btn.e-small, .e-control.e-dropdown-btn .e-control.e-dropdown-btn.e-btn.e-small .e-control.e-dropdown-btn.e-btn .e-control.e-dropdown-btn.e-btn.e-small.e-dropdown-popup ul, .e-control.e-dropdown-btn.e-btn .e-control.e-dropdown-btn.e-small .e-control.e-dropdown-btn .e-control.e-dropdown-btn.e-small, .e-control.e-dropdown-btn.e-btn .e-control.e-dropdown-btn.e-small .e-control.e-dropdown-btn .e-control.e-dropdown-btn.e-btn.e-small, .e-control.e-dropdown-btn.e-btn .e-control.e-dropdown-btn.e-small .e-control.e-dropdown-btn.e-btn .e-control.e-dropdown-btn.e-small, .e-control.e-dropdown-btn.e-btn .e-control.e-dropdown-btn.e-small .e-control.e-dropdown-btn.e-btn .e-control.e-dropdown-btn.e-btn.e-small.e-dropdown-popup ul, .e-control.e-dropdown-btn.e-btn .e-control.e-dropdown-btn.e-btn.e-small .e-control.e-dropdown-btn .e-control.e-dropdown-btn.e-small, .e-control.e-dropdown-btn.e-btn .e-control.e-dropdown-btn.e-btn.e-small .e-control.e-dropdown-btn .e-control.e-dropdown-btn.e-btn.e-small, .e-control.e-dropdown-btn.e-btn .e-control.e-dropdown-btn.e-btn.e-small .e-control.e-dropdown-btn.e-btn .e-control.e-dropdown-btn.e-small, .e-control.e-dropdown-btn.e-btn .e-control.e-dropdown-btn.e-btn.e-small .e-control.e-dropdown-btn.e-btn .e-control.e-dropdown-btn.e-btn.e-small.e-dropdown-popup ul {
  padding: 8px 0;
}

.e-control.e-dropdown-btn .e-control.e-dropdown-btn.e-small .e-control.e-dropdown-btn .e-control.e-dropdown-btn.e-small .e-item, .e-control.e-dropdown-btn .e-control.e-dropdown-btn.e-small .e-control.e-dropdown-btn .e-control.e-dropdown-btn.e-btn.e-small .e-item, .e-control.e-dropdown-btn .e-control.e-dropdown-btn.e-small .e-control.e-dropdown-btn.e-btn .e-control.e-dropdown-btn.e-small .e-item, .e-control.e-dropdown-btn .e-control.e-dropdown-btn.e-small .e-control.e-dropdown-btn.e-btn .e-control.e-dropdown-btn.e-btn.e-small.e-dropdown-popup ul .e-item, .e-control.e-dropdown-btn .e-control.e-dropdown-btn.e-btn.e-small .e-control.e-dropdown-btn .e-control.e-dropdown-btn.e-small .e-item, .e-control.e-dropdown-btn .e-control.e-dropdown-btn.e-btn.e-small .e-control.e-dropdown-btn .e-control.e-dropdown-btn.e-btn.e-small .e-item, .e-control.e-dropdown-btn .e-control.e-dropdown-btn.e-btn.e-small .e-control.e-dropdown-btn.e-btn .e-control.e-dropdown-btn.e-small .e-item, .e-control.e-dropdown-btn .e-control.e-dropdown-btn.e-btn.e-small .e-control.e-dropdown-btn.e-btn .e-control.e-dropdown-btn.e-btn.e-small.e-dropdown-popup ul .e-item, .e-control.e-dropdown-btn.e-btn .e-control.e-dropdown-btn.e-small .e-control.e-dropdown-btn .e-control.e-dropdown-btn.e-small .e-item, .e-control.e-dropdown-btn.e-btn .e-control.e-dropdown-btn.e-small .e-control.e-dropdown-btn .e-control.e-dropdown-btn.e-btn.e-small .e-item, .e-control.e-dropdown-btn.e-btn .e-control.e-dropdown-btn.e-small .e-control.e-dropdown-btn.e-btn .e-control.e-dropdown-btn.e-small .e-item, .e-control.e-dropdown-btn.e-btn .e-control.e-dropdown-btn.e-small .e-control.e-dropdown-btn.e-btn .e-control.e-dropdown-btn.e-btn.e-small.e-dropdown-popup ul .e-item, .e-control.e-dropdown-btn.e-btn .e-control.e-dropdown-btn.e-btn.e-small .e-control.e-dropdown-btn .e-control.e-dropdown-btn.e-small .e-item, .e-control.e-dropdown-btn.e-btn .e-control.e-dropdown-btn.e-btn.e-small .e-control.e-dropdown-btn .e-control.e-dropdown-btn.e-btn.e-small .e-item, .e-control.e-dropdown-btn.e-btn .e-control.e-dropdown-btn.e-btn.e-small .e-control.e-dropdown-btn.e-btn .e-control.e-dropdown-btn.e-small .e-item, .e-control.e-dropdown-btn.e-btn .e-control.e-dropdown-btn.e-btn.e-small .e-control.e-dropdown-btn.e-btn .e-control.e-dropdown-btn.e-btn.e-small.e-dropdown-popup ul .e-item {
  padding: 0 16px;
}

.e-control.e-dropdown-btn .e-control.e-dropdown-btn.e-small.e-vertical, .e-control.e-dropdown-btn .e-control.e-dropdown-btn.e-btn.e-small.e-vertical, .e-control.e-dropdown-btn.e-btn .e-control.e-dropdown-btn.e-small.e-vertical, .e-control.e-dropdown-btn.e-btn .e-control.e-dropdown-btn.e-btn.e-small.e-vertical {
  line-height: 1;
  padding: 12px 12px;
}

.e-bigger .e-control.e-dropdown-btn .e-btn-icon, .e-bigger.e-control.e-dropdown-btn .e-btn-icon {
  font-size: 16px;
}

.e-bigger .e-control.e-dropdown-btn .e-caret, .e-bigger.e-control.e-dropdown-btn .e-caret {
  font-size: 10px;
}

.e-bigger .e-control.e-dropdown-btn.e-vertical, .e-bigger.e-control.e-dropdown-btn.e-vertical {
  line-height: 1;
  padding: 16px 16px;
}

.e-bigger .e-control.e-dropdown-btn.e-small.e-vertical, .e-bigger.e-control.e-dropdown-btn.e-small.e-vertical {
  line-height: 1;
  padding: 16px 16px;
}

.e-control.e-dropdown-popup {
  position: absolute;
}

.e-control.e-dropdown-popup ul {
  border: none;
  border-radius: 0;
  box-shadow: 0 3px 8px 0 rgba(0, 0, 0, 0.26);
  font-size: 14px;
  font-weight: normal;
  list-style-image: none;
  list-style-position: outside;
  list-style-type: none;
  margin: 0;
  min-width: 120px;
  overflow: hidden;
  padding: 8px 0;
  -webkit-user-select: none;
     -moz-user-select: none;
      -ms-user-select: none;
          user-select: none;
  white-space: nowrap;
}

.e-control.e-dropdown-popup ul .e-item {
  cursor: pointer;
  display: -ms-flexbox;
  display: flex;
  height: 36px;
  line-height: 36px;
  padding: 0 16px;
}

.e-control.e-dropdown-popup ul .e-item .e-menu-url {
  display: inline-block;
  text-decoration: none;
}

.e-control.e-dropdown-popup ul .e-item .e-menu-icon {
  float: left;
  font-size: 14px;
  line-height: 36px;
  margin-right: 10px;
  vertical-align: middle;
}

.e-control.e-dropdown-popup ul .e-item.e-blank-icon {
  padding-left: 40px;
}

.e-control.e-dropdown-popup ul .e-item.e-disabled {
  cursor: auto;
  pointer-events: none;
}

.e-control.e-dropdown-popup ul .e-item.e-separator {
  border-bottom-style: solid;
  border-bottom-width: 1px;
  cursor: auto;
  height: auto;
  line-height: normal;
  margin: 8px 0;
  pointer-events: none;
}

.e-rtl.e-control.e-dropdown-popup .e-item.e-blank-icon {
  padding-right: 40px;
}

.e-rtl.e-control.e-dropdown-popup .e-item .e-menu-icon {
  float: right;
  margin-left: 10px;
  margin-right: 0;
}

.e-control.e-bigger .e-control.e-dropdown-popup ul,
.e-control.e-bigger.e-control.e-dropdown-popup ul {
  box-shadow: 0 3px 8px 0 rgba(0, 0, 0, 0.26);
  font-size: 15px;
  max-width: 280px;
  min-width: 112px;
  padding: 8px 0;
}

.e-control.e-bigger .e-control.e-dropdown-popup ul .e-item,
.e-control.e-bigger.e-control.e-dropdown-popup ul .e-item {
  height: 48px;
  line-height: 48px;
  padding: 0 16px;
}

.e-control.e-bigger .e-control.e-dropdown-popup ul .e-item .e-menu-icon,
.e-control.e-bigger.e-control.e-dropdown-popup ul .e-item .e-menu-icon {
  font-size: 16px;
  line-height: 48px;
}

.e-control.e-bigger .e-control.e-dropdown-popup ul .e-item.e-blank-icon,
.e-control.e-bigger.e-control.e-dropdown-popup ul .e-item.e-blank-icon {
  padding-left: 42px;
}

.e-control.e-bigger .e-control.e-dropdown-popup ul .e-item.e-separator,
.e-control.e-bigger.e-control.e-dropdown-popup ul .e-item.e-separator {
  height: auto;
  line-height: normal;
}

/*! drop-down button theme */
.e-control.e-dropdown-popup {
  background-color: #fff;
  color: rgba(0, 0, 0, 0.87);
}

.e-control.e-dropdown-popup ul .e-item .e-menu-url {
  color: rgba(0, 0, 0, 0.87);
}

.e-control.e-dropdown-popup ul .e-item .e-menu-icon {
  color: rgba(0, 0, 0, 0.87);
}

.e-control.e-dropdown-popup ul .e-item.e-focused {
  background-color: #e0e0e0;
  box-shadow: none;
}

.e-control.e-dropdown-popup ul .e-item:hover {
  background-color: #e0e0e0;
  color: rgba(0, 0, 0, 0.87);
}

.e-control.e-dropdown-popup ul .e-item:active, .e-control.e-dropdown-popup ul .e-item.e-selected {
  background-color: #bdbdbd;
  color: rgba(0, 0, 0, 0.87);
}

.e-control.e-dropdown-popup ul .e-item:active .e-menu-url, .e-control.e-dropdown-popup ul .e-item.e-selected .e-menu-url {
  color: rgba(0, 0, 0, 0.87);
}

.e-control.e-dropdown-popup ul .e-item:active .e-menu-icon, .e-control.e-dropdown-popup ul .e-item.e-selected .e-menu-icon {
  color: rgba(0, 0, 0, 0.87);
}

.e-control.e-dropdown-popup ul .e-separator {
  border-bottom-color: rgba(0, 0, 0, 0.12);
}

.e-control .e-js [class^='e-'], .e-control .e-js [class*=' e-'] {
  box-sizing: content-box;
}
