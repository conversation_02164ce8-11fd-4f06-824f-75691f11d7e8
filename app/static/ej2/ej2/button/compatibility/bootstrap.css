/*! button layout */
.e-control.e-btn, .e-css.e-btn {
  -webkit-font-smoothing: antialiased;
  border: 1px solid;
  border-radius: 4px;
  box-sizing: border-box;
  cursor: pointer;
  display: inline-block;
  font-family: "Helvetica Neue", "Helvetica", "Arial", sans-serif, "-apple-system", "BlinkMacSystemFont";
  font-size: 14px;
  font-weight: 400;
  -ms-flex-pack: center;
      justify-content: center;
  line-height: 2.286em;
  outline: none;
  padding: 0 12px;
  text-align: center;
  text-decoration: none;
  text-transform: none;
  -webkit-user-select: none;
     -moz-user-select: none;
      -ms-user-select: none;
          user-select: none;
  vertical-align: middle;
  white-space: nowrap;
}

.e-control.e-btn:disabled, .e-css.e-btn:disabled {
  cursor: default;
}

.e-control.e-btn:hover, .e-control.e-btn:focus, .e-css.e-btn:hover, .e-css.e-btn:focus {
  text-decoration: none;
}

.e-control.e-btn::-moz-focus-inner, .e-css.e-btn::-moz-focus-inner {
  border: 0;
  padding: 0;
}

.e-control.e-btn .e-btn-icon, .e-css.e-btn .e-btn-icon {
  display: inline-block;
  font-size: 12px;
  margin-top: -2px;
  vertical-align: middle;
  width: 1em;
}

.e-control.e-btn .e-btn-icon.e-icon-left, .e-css.e-btn .e-btn-icon.e-icon-left {
  margin-left: -0.5em;
  width: 1.688em;
}

.e-control.e-btn .e-btn-icon.e-icon-right, .e-css.e-btn .e-btn-icon.e-icon-right {
  margin-right: -0.5em;
  width: 1.688em;
}

.e-control.e-btn .e-btn-icon.e-icon-top, .e-css.e-btn .e-btn-icon.e-icon-top {
  display: block;
  margin-top: 0;
  padding-bottom: 6px;
  width: auto;
}

.e-control.e-btn .e-btn-icon.e-icon-bottom, .e-css.e-btn .e-btn-icon.e-icon-bottom {
  display: block;
  margin-top: 0;
  padding-top: 6px;
  width: auto;
}

.e-control.e-btn.e-icon-btn, .e-css.e-btn.e-icon-btn {
  padding: 0 8px;
}

.e-control.e-btn.e-top-icon-btn, .e-control.e-btn.e-bottom-icon-btn, .e-css.e-btn.e-top-icon-btn, .e-css.e-btn.e-bottom-icon-btn {
  line-height: 1;
  padding: 6px 12px;
}

.e-control.e-btn.e-round, .e-css.e-btn.e-round {
  border-radius: 50%;
  height: 2.429em;
  line-height: 1;
  padding: 0;
  width: 2.429em;
}

.e-control.e-btn.e-round .e-btn-icon, .e-css.e-btn.e-round .e-btn-icon {
  font-size: 16px;
  line-height: 2em;
  margin-top: 0;
  width: auto;
}

.e-control.e-btn.e-rtl .e-icon-right, .e-css.e-btn.e-rtl .e-icon-right {
  margin-left: -0.5em;
  margin-right: 0;
}

.e-control.e-btn.e-rtl .e-icon-left, .e-css.e-btn.e-rtl .e-icon-left {
  margin-left: 0;
  margin-right: -0.5em;
}

.e-control.e-btn.e-flat, .e-css.e-btn.e-flat {
  border: 1px solid;
}

.e-control.e-btn.e-small, .e-css.e-btn.e-small {
  font-size: 12px;
  line-height: 2.167em;
  padding: 0 10px;
}

.e-control.e-btn.e-small .e-btn-icon, .e-css.e-btn.e-small .e-btn-icon {
  font-size: 10px;
  width: 1em;
}

.e-control.e-btn.e-small .e-btn-icon.e-icon-left, .e-css.e-btn.e-small .e-btn-icon.e-icon-left {
  margin-left: -0.57143em;
  width: 1.7143em;
}

.e-control.e-btn.e-small .e-btn-icon.e-icon-right, .e-css.e-btn.e-small .e-btn-icon.e-icon-right {
  margin-right: -0.57143em;
  width: 1.7143em;
}

.e-control.e-btn.e-small .e-btn-icon.e-icon-top, .e-css.e-btn.e-small .e-btn-icon.e-icon-top {
  padding-bottom: 6px;
  width: auto;
}

.e-control.e-btn.e-small .e-btn-icon.e-icon-bottom, .e-css.e-btn.e-small .e-btn-icon.e-icon-bottom {
  padding-top: 6px;
  width: auto;
}

.e-control.e-btn.e-small.e-icon-btn, .e-css.e-btn.e-small.e-icon-btn {
  padding: 0 6px;
}

.e-control.e-btn.e-small.e-top-icon-btn, .e-control.e-btn.e-small.e-bottom-icon-btn, .e-css.e-btn.e-small.e-top-icon-btn, .e-css.e-btn.e-small.e-bottom-icon-btn {
  line-height: 1;
  padding: 6px 12px;
}

.e-control.e-btn.e-small.e-round, .e-css.e-btn.e-small.e-round {
  height: 2.334em;
  line-height: 1;
  padding: 0;
  width: 2.334em;
}

.e-control.e-btn.e-small.e-round .e-btn-icon, .e-css.e-btn.e-small.e-round .e-btn-icon {
  font-size: 14px;
  line-height: 1.8572em;
  width: auto;
}

.e-control.e-btn.e-small.e-rtl .e-icon-right, .e-css.e-btn.e-small.e-rtl .e-icon-right {
  margin-left: -0.57143em;
  margin-right: 0;
}

.e-control.e-btn.e-small.e-rtl .e-icon-left, .e-css.e-btn.e-small.e-rtl .e-icon-left {
  margin-left: 0;
  margin-right: btn-small-icon-margin;
}

.e-control.e-btn.e-block, .e-css.e-btn.e-block {
  display: block;
  width: 100%;
}

.e-small .e-control.e-btn, .e-small.e-btn, .e-small .e-css.e-btn, .e-small.e-css.e-btn {
  font-size: 12px;
  line-height: 2.167em;
  padding: 0 10px;
}

.e-small .e-control.e-btn .e-btn-icon, .e-small.e-btn .e-btn-icon, .e-small .e-css.e-btn .e-btn-icon, .e-small.e-css.e-btn .e-btn-icon {
  font-size: 10px;
  width: 1em;
}

.e-small .e-control.e-btn .e-btn-icon.e-icon-left, .e-small.e-btn .e-btn-icon.e-icon-left, .e-small .e-css.e-btn .e-btn-icon.e-icon-left, .e-small.e-css.e-btn .e-btn-icon.e-icon-left {
  margin-left: -0.57143em;
  width: 1.7143em;
}

.e-small .e-control.e-btn .e-btn-icon.e-icon-right, .e-small.e-btn .e-btn-icon.e-icon-right, .e-small .e-css.e-btn .e-btn-icon.e-icon-right, .e-small.e-css.e-btn .e-btn-icon.e-icon-right {
  margin-right: -0.57143em;
  width: 1.7143em;
}

.e-small .e-control.e-btn .e-btn-icon.e-icon-top, .e-small.e-btn .e-btn-icon.e-icon-top, .e-small .e-css.e-btn .e-btn-icon.e-icon-top, .e-small.e-css.e-btn .e-btn-icon.e-icon-top {
  padding-bottom: 6px;
  width: auto;
}

.e-small .e-control.e-btn .e-btn-icon.e-icon-bottom, .e-small.e-btn .e-btn-icon.e-icon-bottom, .e-small .e-css.e-btn .e-btn-icon.e-icon-bottom, .e-small.e-css.e-btn .e-btn-icon.e-icon-bottom {
  padding-top: 6px;
  width: auto;
}

.e-small .e-control.e-btn.e-icon-btn, .e-small.e-btn.e-icon-btn, .e-small .e-css.e-btn.e-icon-btn, .e-small.e-css.e-btn.e-icon-btn {
  padding: 0 6px;
}

.e-small .e-control.e-btn.e-top-icon-btn, .e-small .e-control.e-btn.e-bottom-icon-btn, .e-small.e-btn.e-top-icon-btn, .e-small.e-btn.e-bottom-icon-btn, .e-small .e-css.e-btn.e-top-icon-btn, .e-small .e-css.e-btn.e-bottom-icon-btn, .e-small.e-css.e-btn.e-top-icon-btn, .e-small.e-css.e-btn.e-bottom-icon-btn {
  line-height: 1;
  padding: 6px 12px;
}

.e-small .e-control.e-btn.e-round, .e-small.e-btn.e-round, .e-small .e-css.e-btn.e-round, .e-small.e-css.e-btn.e-round {
  height: 2.334em;
  line-height: 1;
  padding: 0;
  width: 2.334em;
}

.e-small .e-control.e-btn.e-round .e-btn-icon, .e-small.e-btn.e-round .e-btn-icon, .e-small .e-css.e-btn.e-round .e-btn-icon, .e-small.e-css.e-btn.e-round .e-btn-icon {
  font-size: 14px;
  line-height: 1.8572em;
  width: auto;
}

.e-small .e-control.e-btn.e-rtl .e-icon-right, .e-small.e-btn.e-rtl .e-icon-right, .e-small .e-css.e-btn.e-rtl .e-icon-right, .e-small.e-css.e-btn.e-rtl .e-icon-right {
  margin-left: -0.57143em;
  margin-right: 0;
}

.e-small .e-control.e-btn.e-rtl .e-icon-left, .e-small.e-btn.e-rtl .e-icon-left, .e-small .e-css.e-btn.e-rtl .e-icon-left, .e-small.e-css.e-btn.e-rtl .e-icon-left {
  margin-left: 0;
  margin-right: btn-small-icon-margin;
}

.e-bigger.e-small .e-control.e-btn, .e-bigger.e-small.e-control.e-btn, .e-bigger.e-small .e-css.e-btn, .e-bigger.e-small.e-css.e-btn {
  font-size: 15px;
  line-height: 2.2667em;
  padding: 0 14px;
}

.e-bigger.e-small .e-control.e-btn .e-btn-icon, .e-bigger.e-small.e-control.e-btn .e-btn-icon, .e-bigger.e-small .e-css.e-btn .e-btn-icon, .e-bigger.e-small.e-css.e-btn .e-btn-icon {
  font-size: 12px;
  width: 1em;
}

.e-bigger.e-small .e-control.e-btn .e-btn-icon.e-icon-left, .e-bigger.e-small.e-control.e-btn .e-btn-icon.e-icon-left, .e-bigger.e-small .e-css.e-btn .e-btn-icon.e-icon-left, .e-bigger.e-small.e-css.e-btn .e-btn-icon.e-icon-left {
  margin-left: -0.5em;
  width: 2em;
}

.e-bigger.e-small .e-control.e-btn .e-btn-icon.e-icon-right, .e-bigger.e-small.e-control.e-btn .e-btn-icon.e-icon-right, .e-bigger.e-small .e-css.e-btn .e-btn-icon.e-icon-right, .e-bigger.e-small.e-css.e-btn .e-btn-icon.e-icon-right {
  margin-right: -0.5em;
  width: 2em;
}

.e-bigger.e-small .e-control.e-btn .e-btn-icon.e-icon-top, .e-bigger.e-small.e-control.e-btn .e-btn-icon.e-icon-top, .e-bigger.e-small .e-css.e-btn .e-btn-icon.e-icon-top, .e-bigger.e-small.e-css.e-btn .e-btn-icon.e-icon-top {
  padding-bottom: 6px;
  width: auto;
}

.e-bigger.e-small .e-control.e-btn .e-btn-icon.e-icon-bottom, .e-bigger.e-small.e-control.e-btn .e-btn-icon.e-icon-bottom, .e-bigger.e-small .e-css.e-btn .e-btn-icon.e-icon-bottom, .e-bigger.e-small.e-css.e-btn .e-btn-icon.e-icon-bottom {
  padding-top: 6px;
  width: auto;
}

.e-bigger.e-small .e-control.e-btn.e-icon-btn, .e-bigger.e-small.e-control.e-btn.e-icon-btn, .e-bigger.e-small .e-css.e-btn.e-icon-btn, .e-bigger.e-small.e-css.e-btn.e-icon-btn {
  padding: 0 9px;
}

.e-bigger.e-small .e-control.e-btn.e-top-icon-btn, .e-bigger.e-small .e-control.e-btn.e-bottom-icon-btn, .e-bigger.e-small.e-control.e-btn.e-top-icon-btn, .e-bigger.e-small.e-control.e-btn.e-bottom-icon-btn, .e-bigger.e-small .e-css.e-btn.e-top-icon-btn, .e-bigger.e-small .e-css.e-btn.e-bottom-icon-btn, .e-bigger.e-small.e-css.e-btn.e-top-icon-btn, .e-bigger.e-small.e-css.e-btn.e-bottom-icon-btn {
  line-height: 1;
  padding: 8px 16px;
}

.e-bigger.e-small .e-control.e-btn.e-round, .e-bigger.e-small.e-control.e-btn.e-round, .e-bigger.e-small .e-css.e-btn.e-round, .e-bigger.e-small.e-css.e-btn.e-round {
  height: 2.4em;
  line-height: 1;
  padding: 0;
  width: 2.4em;
}

.e-bigger.e-small .e-control.e-btn.e-round .e-btn-icon, .e-bigger.e-small.e-control.e-btn.e-round .e-btn-icon, .e-bigger.e-small .e-css.e-btn.e-round .e-btn-icon, .e-bigger.e-small.e-css.e-btn.e-round .e-btn-icon {
  font-size: 16px;
  line-height: 2.125em;
  width: auto;
}

.e-bigger.e-small .e-control.e-btn.e-rtl .e-icon-right, .e-bigger.e-small.e-control.e-btn.e-rtl .e-icon-right, .e-bigger.e-small .e-css.e-btn.e-rtl .e-icon-right, .e-bigger.e-small.e-css.e-btn.e-rtl .e-icon-right {
  margin-left: -0.5em;
  margin-right: 0;
}

.e-bigger.e-small .e-control.e-btn.e-rtl .e-icon-left, .e-bigger.e-small.e-control.e-btn.e-rtl .e-icon-left, .e-bigger.e-small .e-css.e-btn.e-rtl .e-icon-left, .e-bigger.e-small.e-css.e-btn.e-rtl .e-icon-left {
  margin-left: 0;
  margin-right: -0.5em;
}

.e-bigger .e-control.e-btn, .e-bigger.e-control.e-btn, .e-bigger .e-css.e-btn, .e-bigger.e-css.e-btn {
  font-size: 16px;
  line-height: 2.375em;
  padding: 0 16px;
}

.e-bigger .e-control.e-btn .e-btn-icon, .e-bigger.e-control.e-btn .e-btn-icon, .e-bigger .e-css.e-btn .e-btn-icon, .e-bigger.e-css.e-btn .e-btn-icon {
  font-size: 14px;
  width: 1em;
}

.e-bigger .e-control.e-btn .e-btn-icon.e-icon-left, .e-bigger.e-control.e-btn .e-btn-icon.e-icon-left, .e-bigger .e-css.e-btn .e-btn-icon.e-icon-left, .e-bigger.e-css.e-btn .e-btn-icon.e-icon-left {
  margin-left: -0.6111em;
  width: 2em;
}

.e-bigger .e-control.e-btn .e-btn-icon.e-icon-right, .e-bigger.e-control.e-btn .e-btn-icon.e-icon-right, .e-bigger .e-css.e-btn .e-btn-icon.e-icon-right, .e-bigger.e-css.e-btn .e-btn-icon.e-icon-right {
  margin-right: -0.6111em;
  width: 2em;
}

.e-bigger .e-control.e-btn .e-btn-icon.e-icon-top, .e-bigger.e-control.e-btn .e-btn-icon.e-icon-top, .e-bigger .e-css.e-btn .e-btn-icon.e-icon-top, .e-bigger.e-css.e-btn .e-btn-icon.e-icon-top {
  padding-bottom: 8px;
  width: auto;
}

.e-bigger .e-control.e-btn .e-btn-icon.e-icon-bottom, .e-bigger.e-control.e-btn .e-btn-icon.e-icon-bottom, .e-bigger .e-css.e-btn .e-btn-icon.e-icon-bottom, .e-bigger.e-css.e-btn .e-btn-icon.e-icon-bottom {
  padding-top: 8px;
  width: auto;
}

.e-bigger .e-control.e-btn.e-icon-btn, .e-bigger.e-control.e-btn.e-icon-btn, .e-bigger .e-css.e-btn.e-icon-btn, .e-bigger.e-css.e-btn.e-icon-btn {
  padding: 0 10px;
}

.e-bigger .e-control.e-btn.e-top-icon-btn, .e-bigger .e-control.e-btn.e-bottom-icon-btn, .e-bigger.e-control.e-btn.e-top-icon-btn, .e-bigger.e-control.e-btn.e-bottom-icon-btn, .e-bigger .e-css.e-btn.e-top-icon-btn, .e-bigger .e-css.e-btn.e-bottom-icon-btn, .e-bigger.e-css.e-btn.e-top-icon-btn, .e-bigger.e-css.e-btn.e-bottom-icon-btn {
  line-height: 1;
  padding: 8px 16px;
}

.e-bigger .e-control.e-btn.e-round, .e-bigger.e-control.e-btn.e-round, .e-bigger .e-css.e-btn.e-round, .e-bigger.e-css.e-btn.e-round {
  height: 2.5em;
  line-height: 1;
  padding: 0;
  width: 2.5em;
}

.e-bigger .e-control.e-btn.e-round .e-btn-icon, .e-bigger.e-control.e-btn.e-round .e-btn-icon, .e-bigger .e-css.e-btn.e-round .e-btn-icon, .e-bigger.e-css.e-btn.e-round .e-btn-icon {
  font-size: 18px;
  line-height: 2.1111em;
  width: auto;
}

.e-bigger .e-control.e-btn.e-rtl .e-icon-right, .e-bigger.e-control.e-btn.e-rtl .e-icon-right, .e-bigger .e-css.e-btn.e-rtl .e-icon-right, .e-bigger.e-css.e-btn.e-rtl .e-icon-right {
  margin-left: -0.6111em;
  margin-right: 0;
}

.e-bigger .e-control.e-btn.e-rtl .e-icon-left, .e-bigger.e-control.e-btn.e-rtl .e-icon-left, .e-bigger .e-css.e-btn.e-rtl .e-icon-left, .e-bigger.e-css.e-btn.e-rtl .e-icon-left {
  margin-left: 0;
  margin-right: -0.6111em;
}

.e-bigger .e-control.e-btn.e-small, .e-bigger.e-control.e-btn.e-small, .e-bigger .e-css.e-btn.e-small, .e-bigger.e-css.e-btn.e-small {
  font-size: 15px;
  line-height: 2.2667em;
  padding: 0 14px;
}

.e-bigger .e-control.e-btn.e-small .e-btn-icon, .e-bigger.e-control.e-btn.e-small .e-btn-icon, .e-bigger .e-css.e-btn.e-small .e-btn-icon, .e-bigger.e-css.e-btn.e-small .e-btn-icon {
  font-size: 12px;
  width: 1em;
}

.e-bigger .e-control.e-btn.e-small .e-btn-icon.e-icon-left, .e-bigger.e-control.e-btn.e-small .e-btn-icon.e-icon-left, .e-bigger .e-css.e-btn.e-small .e-btn-icon.e-icon-left, .e-bigger.e-css.e-btn.e-small .e-btn-icon.e-icon-left {
  margin-left: -0.5em;
  width: 2em;
}

.e-bigger .e-control.e-btn.e-small .e-btn-icon.e-icon-right, .e-bigger.e-control.e-btn.e-small .e-btn-icon.e-icon-right, .e-bigger .e-css.e-btn.e-small .e-btn-icon.e-icon-right, .e-bigger.e-css.e-btn.e-small .e-btn-icon.e-icon-right {
  margin-right: -0.5em;
  width: 2em;
}

.e-bigger .e-control.e-btn.e-small .e-btn-icon.e-icon-top, .e-bigger.e-control.e-btn.e-small .e-btn-icon.e-icon-top, .e-bigger .e-css.e-btn.e-small .e-btn-icon.e-icon-top, .e-bigger.e-css.e-btn.e-small .e-btn-icon.e-icon-top {
  padding-bottom: 6px;
  width: auto;
}

.e-bigger .e-control.e-btn.e-small .e-btn-icon.e-icon-bottom, .e-bigger.e-control.e-btn.e-small .e-btn-icon.e-icon-bottom, .e-bigger .e-css.e-btn.e-small .e-btn-icon.e-icon-bottom, .e-bigger.e-css.e-btn.e-small .e-btn-icon.e-icon-bottom {
  padding-top: 6px;
  width: auto;
}

.e-bigger .e-control.e-btn.e-small.e-icon-btn, .e-bigger.e-control.e-btn.e-small.e-icon-btn, .e-bigger .e-css.e-btn.e-small.e-icon-btn, .e-bigger.e-css.e-btn.e-small.e-icon-btn {
  padding: 0 9px;
}

.e-bigger .e-control.e-btn.e-small.e-top-icon-btn, .e-bigger .e-control.e-btn.e-small.e-bottom-icon-btn, .e-bigger.e-control.e-btn.e-small.e-top-icon-btn, .e-bigger.e-control.e-btn.e-small.e-bottom-icon-btn, .e-bigger .e-css.e-btn.e-small.e-top-icon-btn, .e-bigger .e-css.e-btn.e-small.e-bottom-icon-btn, .e-bigger.e-css.e-btn.e-small.e-top-icon-btn, .e-bigger.e-css.e-btn.e-small.e-bottom-icon-btn {
  line-height: 1;
  padding: 8px 16px;
}

.e-bigger .e-control.e-btn.e-small.e-round, .e-bigger.e-control.e-btn.e-small.e-round, .e-bigger .e-css.e-btn.e-small.e-round, .e-bigger.e-css.e-btn.e-small.e-round {
  height: 2.4em;
  line-height: 1;
  padding: 0;
  width: 2.4em;
}

.e-bigger .e-control.e-btn.e-small.e-round .e-btn-icon, .e-bigger.e-control.e-btn.e-small.e-round .e-btn-icon, .e-bigger .e-css.e-btn.e-small.e-round .e-btn-icon, .e-bigger.e-css.e-btn.e-small.e-round .e-btn-icon {
  font-size: 16px;
  line-height: 2.125em;
  width: auto;
}

.e-bigger .e-control.e-btn.e-small.e-rtl .e-icon-right, .e-bigger.e-control.e-btn.e-small.e-rtl .e-icon-right, .e-bigger .e-css.e-btn.e-small.e-rtl .e-icon-right, .e-bigger.e-css.e-btn.e-small.e-rtl .e-icon-right {
  margin-left: -0.5em;
  margin-right: 0;
}

.e-bigger .e-control.e-btn.e-small.e-rtl .e-icon-left, .e-bigger.e-control.e-btn.e-small.e-rtl .e-icon-left, .e-bigger .e-css.e-btn.e-small.e-rtl .e-icon-left, .e-bigger.e-css.e-btn.e-small.e-rtl .e-icon-left {
  margin-left: 0;
  margin-right: -0.5em;
}

/*! button theme */
.e-control.e-btn, .e-css.e-btn {
  -webkit-tap-highlight-color: transparent;
  background-color: #fff;
  border-color: #ccc;
  box-shadow: none;
  color: #333;
  transition: box-shadow 280ms cubic-bezier(0.4, 0, 0.2, 1);
}

.e-control.e-btn:hover, .e-css.e-btn:hover {
  background-color: #e6e6e6;
  border-color: #adadad;
  box-shadow: none;
  color: #333;
}

.e-control.e-btn:focus, .e-css.e-btn:focus {
  background-color: #e6e6e6;
  border-color: #8c8c8c;
  color: #333;
  outline: #fff 0 solid;
  outline-offset: 0;
  box-shadow: none;
}

.e-control.e-btn:active, .e-css.e-btn:active {
  background-color: #e6e6e6;
  border-color: #adadad;
  color: #333;
  outline: #fff 0 solid;
  outline-offset: 0;
  box-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
}

.e-control.e-btn.e-active, .e-css.e-btn.e-active {
  background-color: #e6e6e6;
  border-color: #adadad;
  box-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
  color: #333;
}

.e-control.e-btn:disabled, .e-css.e-btn:disabled {
  background-color: rgba(255, 255, 255, 0.65);
  border-color: rgba(204, 204, 204, 0.65);
  box-shadow: none;
  color: rgba(51, 51, 51, 0.65);
}

.e-control.e-btn .e-ripple-element, .e-css.e-btn .e-ripple-element {
  background-color: transparent;
}

.e-control.e-btn.e-round, .e-css.e-btn.e-round {
  background-color: transparent;
  border-color: #ccc;
  color: #333;
}

.e-control.e-btn.e-round:hover, .e-css.e-btn.e-round:hover {
  background-color: #e6e6e6;
  border-color: #adadad;
  color: #333;
}

.e-control.e-btn.e-round:focus, .e-css.e-btn.e-round:focus {
  background-color: #e6e6e6;
  border-color: #8c8c8c;
  box-shadow: none;
  color: #333;
  outline: #fff 0 solid;
  outline-offset: 0;
}

.e-control.e-btn.e-round:active, .e-css.e-btn.e-round:active {
  background-color: #e6e6e6;
  border-color: #adadad;
  box-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
  color: #333;
  outline: #fff 0 solid;
  outline-offset: 0;
}

.e-control.e-btn.e-round:disabled, .e-css.e-btn.e-round:disabled {
  background-color: rgba(255, 255, 255, 0.65);
  border-color: rgba(204, 204, 204, 0.65);
  box-shadow: none;
  color: rgba(51, 51, 51, 0.65);
}

.e-control.e-btn.e-round.e-primary:focus, .e-css.e-btn.e-round.e-primary:focus {
  outline: #fff 0 solid;
}

.e-control.e-btn.e-round.e-success:focus, .e-css.e-btn.e-round.e-success:focus {
  outline: #fff 0 solid;
}

.e-control.e-btn.e-round.e-info:focus, .e-css.e-btn.e-round.e-info:focus {
  outline: #fff 0 solid;
}

.e-control.e-btn.e-round.e-warning:focus, .e-css.e-btn.e-round.e-warning:focus {
  outline: #fff 0 solid;
}

.e-control.e-btn.e-round.e-danger:focus, .e-css.e-btn.e-round.e-danger:focus {
  outline: #fff 0 solid;
}

.e-control.e-btn.e-primary, .e-css.e-btn.e-primary {
  background-color: #317ab9;
  border-color: #265f91;
  color: #fff;
}

.e-control.e-btn.e-primary:hover, .e-css.e-btn.e-primary:hover {
  background-color: #21527d;
  border-color: #163854;
  box-shadow: none;
  color: #fff;
}

.e-control.e-btn.e-primary:focus, .e-css.e-btn.e-primary:focus {
  background-color: #21527d;
  border-color: #0c1d2c;
  color: #fff;
  outline: #fff 0 solid;
  box-shadow: none;
}

.e-control.e-btn.e-primary:active, .e-css.e-btn.e-primary:active {
  background-color: #21527d;
  border-color: #163854;
  color: #fff;
  outline: #fff 0 solid;
  box-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
}

.e-control.e-btn.e-primary.e-active, .e-css.e-btn.e-primary.e-active {
  background-color: #21527d;
  border-color: #163854;
  box-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
  color: #fff;
}

.e-control.e-btn.e-primary:disabled, .e-css.e-btn.e-primary:disabled {
  background-color: rgba(49, 122, 185, 0.65);
  border-color: rgba(204, 204, 204, 0.65);
  box-shadow: none;
  color: rgba(255, 255, 255, 0.65);
}

.e-control.e-btn.e-primary .e-ripple-element, .e-css.e-btn.e-primary .e-ripple-element {
  background-color: transparent;
}

.e-control.e-btn.e-success, .e-css.e-btn.e-success {
  background-color: #218739;
  border-color: #175e28;
  color: #fff;
}

.e-control.e-btn.e-success:hover, .e-css.e-btn.e-success:hover {
  background-color: #124a1f;
  border-color: #08210e;
  box-shadow: none;
  color: #fff;
}

.e-control.e-btn.e-success:focus, .e-css.e-btn.e-success:focus {
  background-color: #124a1f;
  border-color: black;
  color: #fff;
  box-shadow: none;
}

.e-control.e-btn.e-success:active, .e-control.e-btn.e-success.e-active, .e-css.e-btn.e-success:active, .e-css.e-btn.e-success.e-active {
  background-color: #124a1f;
  border-color: #08210e;
  color: #fff;
  box-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
}

.e-control.e-btn.e-success:disabled, .e-css.e-btn.e-success:disabled {
  background-color: rgba(92, 184, 92, 0.65);
  border-color: rgba(204, 204, 204, 0.65);
  box-shadow: none;
  color: rgba(255, 255, 255, 0.65);
}

.e-control.e-btn.e-success .e-ripple-element, .e-css.e-btn.e-success .e-ripple-element {
  background-color: transparent;
}

.e-control.e-btn.e-info, .e-css.e-btn.e-info {
  background-color: #1b809e;
  border-color: #145d72;
  color: #fff;
}

.e-control.e-btn.e-info:hover, .e-css.e-btn.e-info:hover {
  background-color: #104b5d;
  border-color: #082831;
  box-shadow: none;
  color: #fff;
}

.e-control.e-btn.e-info:focus, .e-css.e-btn.e-info:focus {
  background-color: #104b5d;
  border-color: #010406;
  color: #fff;
  box-shadow: none;
}

.e-control.e-btn.e-info:active, .e-control.e-btn.e-info.e-active, .e-css.e-btn.e-info:active, .e-css.e-btn.e-info.e-active {
  background-color: #104b5d;
  border-color: #082831;
  color: #fff;
  box-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
}

.e-control.e-btn.e-info:disabled, .e-css.e-btn.e-info:disabled {
  background-color: rgba(91, 192, 222, 0.65);
  border-color: rgba(204, 204, 204, 0.65);
  box-shadow: none;
  color: rgba(255, 255, 255, 0.65);
}

.e-control.e-btn.e-info .e-ripple-element, .e-css.e-btn.e-info .e-ripple-element {
  background-color: transparent;
}

.e-control.e-btn.e-warning, .e-css.e-btn.e-warning {
  background-color: #aa6708;
  border-color: #794906;
  color: #fff;
}

.e-control.e-btn.e-warning:hover, .e-css.e-btn.e-warning:hover {
  background-color: #613b05;
  border-color: #301d02;
  box-shadow: none;
  color: #fff;
}

.e-control.e-btn.e-warning:focus, .e-css.e-btn.e-warning:focus {
  background-color: #613b05;
  border-color: black;
  color: #fff;
  box-shadow: none;
}

.e-control.e-btn.e-warning:active, .e-control.e-btn.e-warning.e-active, .e-css.e-btn.e-warning:active, .e-css.e-btn.e-warning.e-active {
  background-color: #613b05;
  border-color: #301d02;
  color: #fff;
  box-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
}

.e-control.e-btn.e-warning:disabled, .e-css.e-btn.e-warning:disabled {
  background-color: rgba(240, 173, 78, 0.65);
  border-color: rgba(204, 204, 204, 0.65);
  box-shadow: none;
  color: rgba(255, 255, 255, 0.65);
}

.e-control.e-btn.e-warning .e-ripple-element, .e-css.e-btn.e-warning .e-ripple-element {
  background-color: transparent;
}

.e-control.e-btn.e-danger, .e-css.e-btn.e-danger {
  background-color: #dc3243;
  border-color: #bb202f;
  color: #fff;
}

.e-control.e-btn.e-danger:hover, .e-css.e-btn.e-danger:hover {
  background-color: #a51c2a;
  border-color: #7a151f;
  box-shadow: none;
  color: #fff;
}

.e-control.e-btn.e-danger:focus, .e-css.e-btn.e-danger:focus {
  background-color: #a51c2a;
  border-color: #4e0d14;
  color: #fff;
  box-shadow: none;
}

.e-control.e-btn.e-danger:active, .e-css.e-btn.e-danger:active {
  background-color: #a51c2a;
  border-color: #7a151f;
  color: #fff;
  box-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
}

.e-control.e-btn.e-danger.e-active, .e-css.e-btn.e-danger.e-active {
  background-color: #a51c2a;
  border-color: #7a151f;
  box-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
  color: #fff;
}

.e-control.e-btn.e-danger:disabled, .e-css.e-btn.e-danger:disabled {
  background-color: rgba(217, 83, 79, 0.65);
  border-color: rgba(204, 204, 204, 0.65);
  box-shadow: none;
  color: rgba(255, 255, 255, 0.65);
}

.e-control.e-btn.e-danger .e-ripple-element, .e-css.e-btn.e-danger .e-ripple-element {
  background-color: transparent;
}

.e-control.e-btn.e-flat, .e-css.e-btn.e-flat {
  background-color: #fff;
  border-color: #ccc;
  box-shadow: none;
  color: #333;
}

.e-control.e-btn.e-flat:hover, .e-css.e-btn.e-flat:hover {
  background-color: #e6e6e6;
  border-color: #adadad;
  box-shadow: none;
  color: #333;
}

.e-control.e-btn.e-flat:focus, .e-css.e-btn.e-flat:focus {
  background-color: #e6e6e6;
  border-color: #8c8c8c;
  color: #333;
  box-shadow: none;
}

.e-control.e-btn.e-flat:active, .e-control.e-btn.e-flat.e-active, .e-css.e-btn.e-flat:active, .e-css.e-btn.e-flat.e-active {
  background-color: #e6e6e6;
  border-color: #adadad;
  color: #333;
  box-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
}

.e-control.e-btn.e-flat:disabled, .e-css.e-btn.e-flat:disabled {
  background-color: #fff;
  border-color: rgba(204, 204, 204, 0.65);
  box-shadow: none;
  color: rgba(51, 51, 51, 0.65);
}

.e-control.e-btn.e-flat .e-ripple-element, .e-css.e-btn.e-flat .e-ripple-element {
  background-color: transparent;
}

.e-control.e-btn.e-flat.e-primary, .e-css.e-btn.e-flat.e-primary {
  background-color: #317ab9;
  border-color: #265f91;
  color: #fff;
}

.e-control.e-btn.e-flat.e-primary:hover, .e-css.e-btn.e-flat.e-primary:hover {
  background-color: #21527d;
  border-color: #163854;
  color: #fff;
}

.e-control.e-btn.e-flat.e-primary:focus, .e-css.e-btn.e-flat.e-primary:focus {
  background-color: #21527d;
  border-color: #0c1d2c;
  color: #fff;
}

.e-control.e-btn.e-flat.e-primary:active, .e-control.e-btn.e-flat.e-primary.e-active, .e-css.e-btn.e-flat.e-primary:active, .e-css.e-btn.e-flat.e-primary.e-active {
  background-color: #21527d;
  border-color: #163854;
  color: #fff;
}

.e-control.e-btn.e-flat.e-primary:disabled, .e-css.e-btn.e-flat.e-primary:disabled {
  background-color: rgba(49, 122, 185, 0.65);
  border-color: rgba(204, 204, 204, 0.65);
  box-shadow: none;
  color: rgba(255, 255, 255, 0.65);
}

.e-control.e-btn.e-flat.e-primary .e-ripple-element, .e-css.e-btn.e-flat.e-primary .e-ripple-element {
  background-color: transparent;
}

.e-control.e-btn.e-flat.e-success, .e-css.e-btn.e-flat.e-success {
  background-color: #218739;
  border-color: #175e28;
  color: #fff;
}

.e-control.e-btn.e-flat.e-success:hover, .e-css.e-btn.e-flat.e-success:hover {
  background-color: #124a1f;
  border-color: #08210e;
  box-shadow: none;
  color: #fff;
}

.e-control.e-btn.e-flat.e-success:focus, .e-css.e-btn.e-flat.e-success:focus {
  background-color: #124a1f;
  border-color: black;
  color: #fff;
  box-shadow: none;
}

.e-control.e-btn.e-flat.e-success:active, .e-control.e-btn.e-flat.e-success.e-active, .e-css.e-btn.e-flat.e-success:active, .e-css.e-btn.e-flat.e-success.e-active {
  background-color: #124a1f;
  border-color: #08210e;
  color: #fff;
  box-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
}

.e-control.e-btn.e-flat.e-success:disabled, .e-css.e-btn.e-flat.e-success:disabled {
  background-color: rgba(92, 184, 92, 0.65);
  border-color: rgba(204, 204, 204, 0.65);
  color: rgba(255, 255, 255, 0.65);
}

.e-control.e-btn.e-flat.e-success .e-ripple-element, .e-css.e-btn.e-flat.e-success .e-ripple-element {
  background-color: transparent;
}

.e-control.e-btn.e-flat.e-info, .e-css.e-btn.e-flat.e-info {
  background-color: #1b809e;
  border-color: #145d72;
  color: #fff;
}

.e-control.e-btn.e-flat.e-info:hover, .e-css.e-btn.e-flat.e-info:hover {
  background-color: #104b5d;
  border-color: #082831;
  box-shadow: none;
  color: #fff;
}

.e-control.e-btn.e-flat.e-info:focus, .e-css.e-btn.e-flat.e-info:focus {
  background-color: #104b5d;
  border-color: #010406;
  color: #fff;
  box-shadow: none;
}

.e-control.e-btn.e-flat.e-info:active, .e-control.e-btn.e-flat.e-info.e-active, .e-css.e-btn.e-flat.e-info:active, .e-css.e-btn.e-flat.e-info.e-active {
  background-color: #104b5d;
  border-color: #082831;
  color: #fff;
  box-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
}

.e-control.e-btn.e-flat.e-info:disabled, .e-css.e-btn.e-flat.e-info:disabled {
  background-color: rgba(91, 192, 222, 0.65);
  border-color: rgba(204, 204, 204, 0.65);
  color: rgba(255, 255, 255, 0.65);
}

.e-control.e-btn.e-flat.e-info .e-ripple-element, .e-css.e-btn.e-flat.e-info .e-ripple-element {
  background-color: transparent;
}

.e-control.e-btn.e-flat.e-warning, .e-css.e-btn.e-flat.e-warning {
  background-color: #aa6708;
  border-color: #794906;
  color: #fff;
}

.e-control.e-btn.e-flat.e-warning:hover, .e-css.e-btn.e-flat.e-warning:hover {
  background-color: #613b05;
  border-color: #301d02;
  box-shadow: none;
  color: #fff;
}

.e-control.e-btn.e-flat.e-warning:focus, .e-css.e-btn.e-flat.e-warning:focus {
  background-color: #613b05;
  border-color: black;
  color: #fff;
  box-shadow: none;
}

.e-control.e-btn.e-flat.e-warning:active, .e-control.e-btn.e-flat.e-warning.e-active, .e-css.e-btn.e-flat.e-warning:active, .e-css.e-btn.e-flat.e-warning.e-active {
  background-color: #613b05;
  border-color: #301d02;
  color: #fff;
  box-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
}

.e-control.e-btn.e-flat.e-warning:disabled, .e-css.e-btn.e-flat.e-warning:disabled {
  background-color: rgba(240, 173, 78, 0.65);
  border-color: rgba(204, 204, 204, 0.65);
  color: rgba(255, 255, 255, 0.65);
}

.e-control.e-btn.e-flat.e-warning .e-ripple-element, .e-css.e-btn.e-flat.e-warning .e-ripple-element {
  background-color: transparent;
}

.e-control.e-btn.e-flat.e-danger, .e-css.e-btn.e-flat.e-danger {
  background-color: #dc3243;
  border-color: #bb202f;
  color: #fff;
}

.e-control.e-btn.e-flat.e-danger:hover, .e-css.e-btn.e-flat.e-danger:hover {
  background-color: #a51c2a;
  border-color: #7a151f;
  box-shadow: none;
  color: #fff;
}

.e-control.e-btn.e-flat.e-danger:focus, .e-css.e-btn.e-flat.e-danger:focus {
  background-color: #a51c2a;
  border-color: #4e0d14;
  color: #fff;
  box-shadow: none;
}

.e-control.e-btn.e-flat.e-danger:active, .e-control.e-btn.e-flat.e-danger.e-active, .e-css.e-btn.e-flat.e-danger:active, .e-css.e-btn.e-flat.e-danger.e-active {
  background-color: #a51c2a;
  border-color: #7a151f;
  color: #fff;
  box-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
}

.e-control.e-btn.e-flat.e-danger:disabled, .e-css.e-btn.e-flat.e-danger:disabled {
  background-color: rgba(217, 83, 79, 0.65);
  border-color: rgba(204, 204, 204, 0.65);
  color: rgba(255, 255, 255, 0.65);
}

.e-control.e-btn.e-flat.e-danger .e-ripple-element, .e-css.e-btn.e-flat.e-danger .e-ripple-element {
  background-color: transparent;
}

.e-control.e-btn.e-outline, .e-css.e-btn.e-outline {
  background-color: transparent;
  border-color: #ccc;
  box-shadow: none;
  color: #333;
}

.e-control.e-btn.e-outline:hover, .e-css.e-btn.e-outline:hover {
  background-color: #e6e6e6;
  border-color: #adadad;
  box-shadow: none;
  color: #333;
}

.e-control.e-btn.e-outline:focus, .e-css.e-btn.e-outline:focus {
  background-color: #e6e6e6;
  border-color: #8c8c8c;
  color: #333;
  box-shadow: none;
}

.e-control.e-btn.e-outline:active, .e-control.e-btn.e-outline.e-active, .e-css.e-btn.e-outline:active, .e-css.e-btn.e-outline.e-active {
  background-color: #e6e6e6;
  border-color: #adadad;
  box-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
  color: #333;
}

.e-control.e-btn.e-outline:disabled, .e-css.e-btn.e-outline:disabled {
  background-color: transparent;
  border-color: rgba(204, 204, 204, 0.65);
  box-shadow: none;
  color: rgba(51, 51, 51, 0.65);
}

.e-control.e-btn.e-outline.e-primary, .e-css.e-btn.e-outline.e-primary {
  background-color: transparent;
  border-color: #317ab9;
  color: #317ab9;
}

.e-control.e-btn.e-outline.e-primary:hover, .e-css.e-btn.e-outline.e-primary:hover {
  background-color: #21527d;
  border-color: #265f91;
  color: #fff;
}

.e-control.e-btn.e-outline.e-primary:focus, .e-css.e-btn.e-outline.e-primary:focus {
  background-color: #21527d;
  border-color: #0c1d2c;
  color: #fff;
}

.e-control.e-btn.e-outline.e-primary:active, .e-control.e-btn.e-outline.e-primary.e-active, .e-css.e-btn.e-outline.e-primary:active, .e-css.e-btn.e-outline.e-primary.e-active {
  background-color: #21527d;
  border-color: #265f91;
  box-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
  color: #fff;
}

.e-control.e-btn.e-outline.e-primary:disabled, .e-css.e-btn.e-outline.e-primary:disabled {
  background-color: transparent;
  border-color: rgba(204, 204, 204, 0.65);
  box-shadow: none;
  color: rgba(51, 51, 51, 0.65);
}

.e-control.e-btn.e-outline.e-success, .e-css.e-btn.e-outline.e-success {
  background-color: transparent;
  border-color: #218739;
  color: #218739;
}

.e-control.e-btn.e-outline.e-success:hover, .e-css.e-btn.e-outline.e-success:hover {
  background-color: #124a1f;
  border-color: #08210e;
  color: #fff;
}

.e-control.e-btn.e-outline.e-success:focus, .e-css.e-btn.e-outline.e-success:focus {
  background-color: #124a1f;
  border-color: black;
  color: #fff;
}

.e-control.e-btn.e-outline.e-success:active, .e-control.e-btn.e-outline.e-success.e-active, .e-css.e-btn.e-outline.e-success:active, .e-css.e-btn.e-outline.e-success.e-active {
  background-color: #124a1f;
  border-color: #08210e;
  box-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
  color: #fff;
}

.e-control.e-btn.e-outline.e-success:disabled, .e-css.e-btn.e-outline.e-success:disabled {
  background-color: transparent;
  border-color: rgba(33, 135, 57, 0.65);
  box-shadow: none;
  color: rgba(33, 135, 57, 0.65);
}

.e-control.e-btn.e-outline.e-info, .e-css.e-btn.e-outline.e-info {
  background-color: transparent;
  border-color: #1b809e;
  color: #1b809e;
}

.e-control.e-btn.e-outline.e-info:hover, .e-css.e-btn.e-outline.e-info:hover {
  background-color: #104b5d;
  border-color: #082831;
  color: #fff;
}

.e-control.e-btn.e-outline.e-info:focus, .e-css.e-btn.e-outline.e-info:focus {
  background-color: #104b5d;
  border-color: #010406;
  color: #fff;
}

.e-control.e-btn.e-outline.e-info:active, .e-control.e-btn.e-outline.e-info.e-active, .e-css.e-btn.e-outline.e-info:active, .e-css.e-btn.e-outline.e-info.e-active {
  background-color: #104b5d;
  border-color: #082831;
  box-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
  color: #fff;
}

.e-control.e-btn.e-outline.e-info:disabled, .e-css.e-btn.e-outline.e-info:disabled {
  background-color: transparent;
  border-color: rgba(27, 128, 158, 0.65);
  box-shadow: none;
  color: rgba(27, 128, 158, 0.65);
}

.e-control.e-btn.e-outline.e-warning, .e-css.e-btn.e-outline.e-warning {
  background-color: transparent;
  border-color: #aa6708;
  color: #aa6708;
}

.e-control.e-btn.e-outline.e-warning:hover, .e-css.e-btn.e-outline.e-warning:hover {
  background-color: #613b05;
  border-color: #301d02;
  color: #fff;
}

.e-control.e-btn.e-outline.e-warning:focus, .e-css.e-btn.e-outline.e-warning:focus {
  background-color: #613b05;
  border-color: black;
  color: #fff;
}

.e-control.e-btn.e-outline.e-warning:active, .e-control.e-btn.e-outline.e-warning.e-active, .e-css.e-btn.e-outline.e-warning:active, .e-css.e-btn.e-outline.e-warning.e-active {
  background-color: #613b05;
  border-color: #301d02;
  box-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
  color: #fff;
}

.e-control.e-btn.e-outline.e-warning:disabled, .e-css.e-btn.e-outline.e-warning:disabled {
  background-color: transparent;
  border-color: rgba(170, 103, 8, 0.65);
  box-shadow: none;
  color: rgba(170, 103, 8, 0.65);
}

.e-control.e-btn.e-outline.e-danger, .e-css.e-btn.e-outline.e-danger {
  background-color: transparent;
  border-color: #dc3243;
  color: #dc3243;
}

.e-control.e-btn.e-outline.e-danger:hover, .e-css.e-btn.e-outline.e-danger:hover {
  background-color: #a51c2a;
  border-color: #7a151f;
  color: #fff;
}

.e-control.e-btn.e-outline.e-danger:focus, .e-css.e-btn.e-outline.e-danger:focus {
  background-color: #a51c2a;
  border-color: #4e0d14;
  color: #fff;
}

.e-control.e-btn.e-outline.e-danger:active, .e-control.e-btn.e-outline.e-danger.e-active, .e-css.e-btn.e-outline.e-danger:active, .e-css.e-btn.e-outline.e-danger.e-active {
  background-color: #a51c2a;
  border-color: #7a151f;
  box-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
  color: #fff;
}

.e-control.e-btn.e-outline.e-danger:disabled, .e-css.e-btn.e-outline.e-danger:disabled {
  background-color: transparent;
  border-color: rgba(220, 50, 67, 0.65);
  box-shadow: none;
  color: rgba(220, 50, 67, 0.65);
}

.e-control.e-btn.e-link, .e-css.e-btn.e-link {
  background-color: transparent;
  border-color: transparent;
  border-radius: 0;
  box-shadow: none;
  color: #0d47a1;
}

.e-control.e-btn.e-link:hover, .e-css.e-btn.e-link:hover {
  border-radius: 0;
  color: #0a3576;
  text-decoration: underline;
}

.e-control.e-btn.e-link:focus, .e-css.e-btn.e-link:focus {
  border-radius: 0;
  text-decoration: underline;
  color: #0a3576;
}

.e-control.e-btn.e-link:disabled, .e-css.e-btn.e-link:disabled {
  color: rgba(51, 51, 51, 0.65);
  background-color: transparent;
  box-shadow: none;
  text-decoration: none;
}

.e-control .e-js [class^='e-'], .e-control .e-js [class*=' e-'] {
  box-sizing: content-box;
}
