/*! tooltip layout */
.e-tooltip-wrap {
  max-width: 350px;
  min-width: 30px;
  padding: 0;
  position: absolute;
  visibility: visible;
  /*! tooltip arrow */
  /*! tooltip sticky mode close icon */
  /*! tooltip content area */
}

.e-tooltip-wrap .e-arrow-tip {
  overflow: hidden;
  position: absolute;
}

.e-tooltip-wrap .e-arrow-tip.e-tip-bottom {
  height: 8px;
  left: 50%;
  top: 100%;
  width: 16px;
}

.e-tooltip-wrap .e-arrow-tip.e-tip-top {
  height: 8px;
  left: 50%;
  top: -9px;
  width: 16px;
}

.e-tooltip-wrap .e-arrow-tip.e-tip-left {
  height: 16px;
  left: -9px;
  top: 48%;
  width: 8px;
}

.e-tooltip-wrap .e-arrow-tip.e-tip-right {
  height: 16px;
  left: 100%;
  top: 50%;
  width: 8px;
}

.e-tooltip-wrap .e-tooltip-close {
  float: right;
  position: absolute;
  right: -9px;
  top: -9px;
  z-index: inherit;
}

.e-tooltip-wrap .e-tip-content {
  background-color: inherit;
  height: 100%;
  line-height: 16px;
  overflow-wrap: break-word;
  overflow-x: hidden;
  padding: 12px 12px;
  position: relative;
  white-space: normal;
  width: 100%;
  word-break: break-word;
  z-index: 1;
}

/*! Bigger Style */
.e-bigger .e-tooltip-wrap .e-tip-content,
.e-tooltip-wrap.e-bigger .e-tip-content {
  line-height: 20px;
  padding: 15px 15px;
}

/*! Tooltip theme */
.e-tooltip-wrap {
  border-radius: 0;
  filter: drop-shadow(0 6px 10px rgba(0, 0, 0, 0.3));
  opacity: 1;
  /*! tooltip arrow */
  /*! tooltip sticky mode close icon */
  /*! tooltip content area */
}

.e-tooltip-wrap.e-popup {
  background-color: #f4f4f4;
  border: 1px solid #f4f4f4;
}

.e-tooltip-wrap .e-arrow-tip-outer {
  height: 0;
  left: 0;
  position: absolute;
  top: 0;
  width: 0;
}

.e-tooltip-wrap .e-arrow-tip-outer.e-tip-bottom {
  border-left: 8px solid transparent;
  border-right: 8px solid transparent;
  border-top: 8px solid #f4f4f4;
}

.e-tooltip-wrap .e-arrow-tip-outer.e-tip-top {
  border-bottom: 8px solid #f4f4f4;
  border-left: 8px solid transparent;
  border-right: 8px solid transparent;
}

.e-tooltip-wrap .e-arrow-tip-outer.e-tip-left {
  border-bottom: 8px solid transparent;
  border-right: 8px solid #f4f4f4;
  border-top: 8px solid transparent;
}

.e-tooltip-wrap .e-arrow-tip-outer.e-tip-right {
  border-bottom: 8px solid transparent;
  border-left: 8px solid #f4f4f4;
  border-top: 8px solid transparent;
}

.e-tooltip-wrap .e-arrow-tip-inner {
  height: 0;
  position: absolute;
  width: 0;
  z-index: 10;
}

.e-tooltip-wrap .e-arrow-tip-inner.e-tip-right, .e-tooltip-wrap .e-arrow-tip-inner.e-tip-left, .e-tooltip-wrap .e-arrow-tip-inner.e-tip-bottom, .e-tooltip-wrap .e-arrow-tip-inner.e-tip-top {
  color: #f4f4f4;
  font-family: 'e-icons';
  font-size: 16px;
  font-style: normal;
  font-variant: normal;
  font-weight: normal;
  line-height: 1;
  text-transform: none;
}

.e-tooltip-wrap .e-tooltip-close {
  background-color: #414040;
  border-color: #9a9a9a;
  border-radius: 8px;
  color: #000;
}

.e-tooltip-wrap .e-tooltip-close:hover {
  background-color: #414040;
  color: #dadada;
}

.e-tooltip-wrap .e-tip-content {
  color: #000;
  font-family: "Segoe UI", "GeezaPro", "DejaVu Serif", sans-serif, "-apple-system", "BlinkMacSystemFont";
  font-size: 11px;
}

/*! bigger style */
.e-bigger .e-tooltip-wrap .e-tip-content,
.e-tooltip-wrap.e-bigger .e-tip-content {
  font-size: 12px;
}
