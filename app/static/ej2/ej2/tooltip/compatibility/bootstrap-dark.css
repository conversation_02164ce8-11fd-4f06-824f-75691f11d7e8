/*! tooltip layout */
.e-control.e-tooltip-wrap {
  max-width: 350px;
  min-width: 30px;
  padding: 0;
  position: absolute;
  visibility: visible;
  /*! tooltip arrow */
  /*! tooltip sticky mode close icon */
  /*! tooltip content area */
}

.e-control.e-tooltip-wrap .e-arrow-tip {
  overflow: hidden;
  position: absolute;
}

.e-control.e-tooltip-wrap .e-arrow-tip.e-tip-bottom {
  height: 8px;
  left: 50%;
  top: 100%;
  width: 16px;
}

.e-control.e-tooltip-wrap .e-arrow-tip.e-tip-top {
  height: 8px;
  left: 50%;
  top: -9px;
  width: 16px;
}

.e-control.e-tooltip-wrap .e-arrow-tip.e-tip-left {
  height: 16px;
  left: -9px;
  top: 48%;
  width: 8px;
}

.e-control.e-tooltip-wrap .e-arrow-tip.e-tip-right {
  height: 16px;
  left: 100%;
  top: 50%;
  width: 8px;
}

.e-control.e-tooltip-wrap .e-tooltip-close {
  float: right;
  position: absolute;
  right: -9px;
  top: -9px;
  z-index: inherit;
}

.e-control.e-tooltip-wrap .e-tip-content {
  background-color: inherit;
  height: 100%;
  line-height: 17px;
  overflow-wrap: break-word;
  overflow-x: hidden;
  padding: 3px 8px;
  position: relative;
  white-space: normal;
  width: 100%;
  word-break: break-word;
  z-index: 1;
}

/*! Bigger Style */
.e-bigger .e-control.e-tooltip-wrap .e-tip-content,
.e-control.e-control.e-tooltip-wrap.e-bigger .e-tip-content {
  line-height: 20px;
  padding: 5px 10px;
}

/*! Tooltip theme */
.e-control.e-tooltip-wrap {
  border-radius: 4px;
  filter: none;
  opacity: 1;
  /*! tooltip arrow */
  /*! tooltip sticky mode close icon */
  /*! tooltip content area */
}

.e-control.e-tooltip-wrap.e-popup {
  background-color: #f0f0f0;
  border: 1px solid #505050;
}

.e-control.e-tooltip-wrap .e-arrow-tip-outer {
  height: 0;
  left: 0;
  position: absolute;
  top: 0;
  width: 0;
}

.e-control.e-tooltip-wrap .e-arrow-tip-outer.e-tip-bottom {
  border-left: 8px solid transparent;
  border-right: 8px solid transparent;
  border-top: 8px solid #505050;
}

.e-control.e-tooltip-wrap .e-arrow-tip-outer.e-tip-top {
  border-bottom: 8px solid #505050;
  border-left: 8px solid transparent;
  border-right: 8px solid transparent;
}

.e-control.e-tooltip-wrap .e-arrow-tip-outer.e-tip-left {
  border-bottom: 8px solid transparent;
  border-right: 8px solid #505050;
  border-top: 8px solid transparent;
}

.e-control.e-tooltip-wrap .e-arrow-tip-outer.e-tip-right {
  border-bottom: 8px solid transparent;
  border-left: 8px solid #505050;
  border-top: 8px solid transparent;
}

.e-control.e-tooltip-wrap .e-arrow-tip-inner {
  height: 0;
  position: absolute;
  width: 0;
  z-index: 10;
}

.e-control.e-tooltip-wrap .e-arrow-tip-inner.e-tip-right, .e-control.e-tooltip-wrap .e-arrow-tip-inner.e-tip-left, .e-control.e-tooltip-wrap .e-arrow-tip-inner.e-tip-bottom, .e-control.e-tooltip-wrap .e-arrow-tip-inner.e-tip-top {
  color: #f0f0f0;
  font-family: 'e-icons';
  font-size: 16px;
  font-style: normal;
  font-variant: normal;
  font-weight: normal;
  line-height: 1;
  text-transform: none;
}

.e-control.e-tooltip-wrap .e-tooltip-close {
  background-color: #6e6e6e;
  border-color: #505050;
  border-radius: 8px;
  color: #1a1a1a;
}

.e-control.e-tooltip-wrap .e-tooltip-close:hover {
  background-color: #9c9c9c;
  color: #1a1a1a;
}

.e-control.e-tooltip-wrap .e-tip-content {
  color: #1a1a1a;
  font-family: "Helvetica Neue", "Helvetica", "Arial", sans-serif, "-apple-system", "BlinkMacSystemFont";
  font-size: 12px;
}

/*! bigger style */
.e-bigger .e-control.e-tooltip-wrap .e-tip-content,
.e-control.e-control.e-tooltip-wrap.e-bigger .e-tip-content {
  font-size: 13px;
}

.e-control .e-js [class^='e-'], .e-control .e-js [class*=' e-'] {
  box-sizing: content-box;
}
