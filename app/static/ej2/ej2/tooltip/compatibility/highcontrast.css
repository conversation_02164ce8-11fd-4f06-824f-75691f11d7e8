.e-tooltip-close::before {
  color: #000;
  content: '\e953';
  font-size: 8px;
  left: calc(50% - 4px);
  position: absolute;
  top: calc(50% - 4px);
}

.e-arrow-tip-inner.e-tip-right::before {
  content: '\e848';
}

.e-arrow-tip-inner.e-tip-top::before {
  content: '\e918';
}

.e-arrow-tip-inner.e-tip-bottom::before {
  content: '\e919';
}

.e-arrow-tip-inner.e-tip-left::before {
  content: '\e84b';
}

/*! tooltip layout */
.e-control.e-tooltip-wrap {
  max-width: 350px;
  min-width: 30px;
  padding: 0;
  position: absolute;
  visibility: visible;
  /*! tooltip arrow */
  /*! tooltip sticky mode close icon */
  /*! tooltip content area */
}

.e-control.e-tooltip-wrap .e-arrow-tip {
  overflow: hidden;
  position: absolute;
}

.e-control.e-tooltip-wrap .e-arrow-tip.e-tip-bottom {
  height: 8px;
  left: 50%;
  top: 100%;
  width: 16px;
}

.e-control.e-tooltip-wrap .e-arrow-tip.e-tip-top {
  height: 8px;
  left: 50%;
  top: -9px;
  width: 16px;
}

.e-control.e-tooltip-wrap .e-arrow-tip.e-tip-left {
  height: 16px;
  left: -9px;
  top: 48%;
  width: 8px;
}

.e-control.e-tooltip-wrap .e-arrow-tip.e-tip-right {
  height: 16px;
  left: 100%;
  top: 50%;
  width: 8px;
}

.e-control.e-tooltip-wrap .e-tooltip-close {
  float: right;
  position: absolute;
  right: -9px;
  top: -9px;
  z-index: inherit;
}

.e-control.e-tooltip-wrap .e-tip-content {
  background-color: inherit;
  height: 100%;
  line-height: 16px;
  overflow-wrap: break-word;
  overflow-x: hidden;
  padding: 12px 12px;
  position: relative;
  white-space: normal;
  width: 100%;
  word-break: break-word;
  z-index: 1;
}

/*! Bigger Style */
.e-bigger .e-control.e-tooltip-wrap .e-tip-content,
.e-control.e-control.e-tooltip-wrap.e-bigger .e-tip-content {
  line-height: 20px;
  padding: 15px 15px;
}

/*! Tooltip theme */
.e-control.e-tooltip-wrap {
  border-radius: 0;
  filter: none;
  opacity: 1;
  /*! tooltip arrow */
  /*! tooltip sticky mode close icon */
  /*! tooltip content area */
}

.e-control.e-tooltip-wrap.e-popup {
  background-color: #fff;
  border: 1px solid #969696;
}

.e-control.e-tooltip-wrap .e-arrow-tip-outer {
  height: 0;
  left: 0;
  position: absolute;
  top: 0;
  width: 0;
}

.e-control.e-tooltip-wrap .e-arrow-tip-outer.e-tip-bottom {
  border-left: 7px solid transparent;
  border-right: 7px solid transparent;
  border-top: 7px solid #969696;
}

.e-control.e-tooltip-wrap .e-arrow-tip-outer.e-tip-top {
  border-bottom: 7px solid #969696;
  border-left: 7px solid transparent;
  border-right: 7px solid transparent;
}

.e-control.e-tooltip-wrap .e-arrow-tip-outer.e-tip-left {
  border-bottom: 7px solid transparent;
  border-right: 7px solid #969696;
  border-top: 7px solid transparent;
}

.e-control.e-tooltip-wrap .e-arrow-tip-outer.e-tip-right {
  border-bottom: 7px solid transparent;
  border-left: 7px solid #969696;
  border-top: 7px solid transparent;
}

.e-control.e-tooltip-wrap .e-arrow-tip-inner {
  height: 0;
  position: absolute;
  width: 0;
  z-index: 10;
}

.e-control.e-tooltip-wrap .e-arrow-tip-inner.e-tip-right, .e-control.e-tooltip-wrap .e-arrow-tip-inner.e-tip-left, .e-control.e-tooltip-wrap .e-arrow-tip-inner.e-tip-bottom, .e-control.e-tooltip-wrap .e-arrow-tip-inner.e-tip-top {
  color: #fff;
  font-family: 'e-icons';
  font-size: 15px;
  font-style: normal;
  font-variant: normal;
  font-weight: normal;
  line-height: 1;
  text-transform: none;
}

.e-control.e-tooltip-wrap .e-tooltip-close {
  background-color: #fff;
  border-color: #000;
  border-radius: 8px;
  color: #000;
  border-style: solid;
  border-width: 1px;
  height: 16px;
  width: 16px;
}

.e-control.e-tooltip-wrap .e-tooltip-close:hover {
  background-color: #fff;
  color: #000;
}

.e-control.e-tooltip-wrap .e-tip-content {
  color: #000;
  font-family: "Segoe UI", "GeezaPro", "DejaVu Serif", sans-serif, "-apple-system", "BlinkMacSystemFont";
  font-size: 11px;
}

/*! bigger style */
.e-bigger .e-control.e-tooltip-wrap .e-tip-content,
.e-control.e-control.e-tooltip-wrap.e-bigger .e-tip-content {
  font-size: 12px;
}

.e-control .e-js [class^='e-'], .e-control .e-js [class*=' e-'] {
  box-sizing: content-box;
}
