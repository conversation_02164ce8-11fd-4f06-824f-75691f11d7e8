.e-chip-list .e-chip-text {
  margin-top: -2px;
}

.e-chip-list .e-chip-delete.e-dlt-btn::before {
  content: '\e7fc';
}

.e-chip-list.e-multi-selection .e-chip::before {
  content: '\e7ff';
}

.e-chip-list {
  display: -ms-flexbox;
  display: flex;
  padding: 4px;
}

.e-chip-list.e-chip,
.e-chip-list .e-chip {
  -webkit-tap-highlight-color: transparent;
  -ms-flex-align: center;
      align-items: center;
  border: 1px solid;
  border-radius: 0;
  box-shadow: none;
  box-sizing: border-box;
  cursor: pointer;
  display: -ms-inline-flexbox;
  display: inline-flex;
  font-family: "Segoe UI", "GeezaPro", "DejaVu Serif", sans-serif, "-apple-system", "BlinkMacSystemFont";
  font-size: 13px;
  font-weight: 400;
  height: 28px;
  -ms-flex-pack: center;
      justify-content: center;
  line-height: 1.5em;
  margin: 4px;
  outline: none;
  overflow: hidden;
  padding: 0 10px;
  position: relative;
  transition: box-shadow 300ms cubic-bezier(0.4, 0, 0.2, 1);
  -webkit-user-select: none;
     -moz-user-select: none;
      -ms-user-select: none;
          user-select: none;
}

.e-chip-list.e-chip .e-chip-avatar,
.e-chip-list .e-chip .e-chip-avatar {
  -ms-flex-align: center;
      align-items: center;
  background-size: cover;
  border-radius: 50%;
  display: -ms-flexbox;
  display: flex;
  font-size: 13px;
  height: 28px;
  -ms-flex-pack: center;
      justify-content: center;
  line-height: 1;
  margin: 0 8px 0 -10px;
  overflow: hidden;
  width: 28px;
}

.e-chip-list.e-chip .e-chip-avatar-wrap, .e-chip-list.e-chip.e-chip-avatar-wrap,
.e-chip-list .e-chip .e-chip-avatar-wrap,
.e-chip-list .e-chip.e-chip-avatar-wrap {
  border-radius: 14px 0 0 14px;
}

.e-chip-list.e-chip .e-chip-icon,
.e-chip-list .e-chip .e-chip-icon {
  -ms-flex-align: center;
      align-items: center;
  background-size: cover;
  border-radius: 0%;
  display: -ms-flexbox;
  display: flex;
  font-size: 14px;
  height: 20px;
  -ms-flex-pack: center;
      justify-content: center;
  line-height: 1;
  margin: 0 8px 0 -6px;
  overflow: hidden;
  width: 20px;
}

.e-chip-list.e-chip .e-chip-text,
.e-chip-list .e-chip .e-chip-text {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.e-chip-list.e-chip .e-chip-delete,
.e-chip-list .e-chip .e-chip-delete {
  -ms-flex-align: center;
      align-items: center;
  background-size: cover;
  border-radius: 0%;
  display: -ms-flexbox;
  display: flex;
  font-size: 10px;
  height: 14px;
  -ms-flex-pack: center;
      justify-content: center;
  line-height: 1;
  margin: 0 -2px 0 8px;
  overflow: hidden;
  width: 14px;
}

.e-chip-list.e-chip .e-chip-delete.e-dlt-btn::before,
.e-chip-list .e-chip .e-chip-delete.e-dlt-btn::before {
  font-family: 'e-icons';
}

.e-chip-list:not(.e-chip) {
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
}

.e-chip-list.e-multi-selection .e-chip::before {
  -ms-flex-align: center;
      align-items: center;
  display: -ms-flexbox;
  display: flex;
  font-family: 'e-icons';
  height: 20px;
  -ms-flex-pack: center;
      justify-content: center;
  line-height: 1;
  margin: 0 8px 0 -6px;
  margin-top: 0;
  overflow: hidden;
  transition: width 300ms cubic-bezier(0.4, 0, 0.2, 1);
  width: 20px;
}

.e-chip-list.e-multi-selection .e-chip:not(.e-chip-icon-wrap):not(.e-chip-avatar-wrap)::before {
  width: 0;
}

.e-chip-list.e-multi-selection .e-chip.e-chip-icon-wrap::before, .e-chip-list.e-multi-selection .e-chip.e-chip-avatar-wrap::before {
  display: none;
}

.e-chip-list.e-multi-selection .e-chip.e-chip-avatar-wrap::before {
  height: 28px;
  margin: 0 8px 0 -10px;
  margin-top: 0;
  width: 28px;
}

.e-chip-list.e-multi-selection .e-chip.e-active .e-chip-icon,
.e-chip-list.e-multi-selection .e-chip.e-active .e-chip-avatar {
  display: none;
}

.e-chip-list.e-multi-selection .e-chip.e-active.e-chip-icon-wrap::before, .e-chip-list.e-multi-selection .e-chip.e-active.e-chip-avatar-wrap::before {
  display: -ms-flexbox;
  display: flex;
}

.e-chip-list.e-multi-selection .e-chip.e-active:not(.e-chip-icon-wrap):not(.e-chip-avatar-wrap)::before {
  width: 20px;
}

.e-chip-list.e-rtl.e-chip .e-chip-avatar,
.e-chip-list.e-rtl .e-chip .e-chip-avatar {
  margin: 0 -10px 0 8px;
}

.e-chip-list.e-rtl.e-chip .e-chip-icon,
.e-chip-list.e-rtl .e-chip .e-chip-icon {
  margin: 0 -6px 0 8px;
}

.e-chip-list.e-rtl.e-chip .e-chip-delete,
.e-chip-list.e-rtl .e-chip .e-chip-delete {
  margin: 0 8px 0 -2px;
}

.e-chip-list.e-rtl.e-chip .e-chip-avatar-wrap, .e-chip-list.e-rtl.e-chip.e-chip-avatar-wrap,
.e-chip-list.e-rtl .e-chip .e-chip-avatar-wrap,
.e-chip-list.e-rtl .e-chip.e-chip-avatar-wrap {
  border-radius: 0 14px 14px 0;
}

.e-chip-list.e-rtl.e-multi-selection .e-chip::before {
  margin: 0 -6px 0 8px;
  margin-top: 0;
}

.e-chip-list.e-rtl.e-multi-selection .e-chip.e-chip-avatar-wrap::before {
  margin: 0 -10px 0 8px;
  margin-top: 0;
}

.e-bigger .e-chip-list.e-chip,
.e-bigger .e-chip-list .e-chip,
.e-bigger.e-chip-list.e-chip,
.e-bigger.e-chip-list .e-chip {
  border-radius: 0;
  font-size: 14px;
  height: 32px;
  padding: 0 12px;
}

.e-bigger .e-chip-list .e-chip-avatar,
.e-bigger.e-chip-list .e-chip-avatar {
  font-size: 15px;
  height: 32px;
  margin: 0 8px 0 -12px;
  width: 32px;
}

.e-bigger .e-chip-list .e-chip-avatar-wrap, .e-bigger .e-chip-list.e-chip-avatar-wrap,
.e-bigger.e-chip-list .e-chip-avatar-wrap,
.e-bigger.e-chip-list.e-chip-avatar-wrap {
  border-radius: 16px 0 0 16px;
}

.e-bigger .e-chip-list .e-chip-icon,
.e-bigger.e-chip-list .e-chip-icon {
  font-size: 16px;
  height: 24px;
  margin: 0 8px 0 -8px;
  width: 24px;
}

.e-bigger .e-chip-list .e-chip-delete,
.e-bigger.e-chip-list .e-chip-delete {
  font-size: 10px;
  height: 18px;
  margin: 0 -4px 0 8px;
  width: 18px;
}

.e-bigger .e-chip-list.e-multi-selection .e-chip::before,
.e-bigger.e-chip-list.e-multi-selection .e-chip::before {
  height: 24px;
  margin: 0 8px 0 -8px;
  margin-top: 0;
  width: 24px;
}

.e-bigger .e-chip-list.e-multi-selection .e-chip.e-chip-avatar-wrap::before,
.e-bigger.e-chip-list.e-multi-selection .e-chip.e-chip-avatar-wrap::before {
  height: 32px;
  margin: 0 8px 0 -12px;
  margin-top: 0;
  width: 32px;
}

.e-bigger .e-chip-list.e-multi-selection .e-chip.e-active:not(.e-chip-icon-wrap):not(.e-chip-avatar-wrap)::before,
.e-bigger.e-chip-list.e-multi-selection .e-chip.e-active:not(.e-chip-icon-wrap):not(.e-chip-avatar-wrap)::before {
  width: 24px;
}

.e-bigger .e-chip-list.e-rtl.e-chip .e-chip-avatar,
.e-bigger .e-chip-list.e-rtl .e-chip .e-chip-avatar,
.e-bigger.e-chip-list.e-rtl.e-chip .e-chip-avatar,
.e-bigger.e-chip-list.e-rtl .e-chip .e-chip-avatar {
  margin: 0 -12px 0 8px;
}

.e-bigger .e-chip-list.e-rtl.e-chip .e-chip-icon,
.e-bigger .e-chip-list.e-rtl .e-chip .e-chip-icon,
.e-bigger.e-chip-list.e-rtl.e-chip .e-chip-icon,
.e-bigger.e-chip-list.e-rtl .e-chip .e-chip-icon {
  margin: 0 -8px 0 8px;
}

.e-bigger .e-chip-list.e-rtl.e-chip .e-chip-delete,
.e-bigger .e-chip-list.e-rtl .e-chip .e-chip-delete,
.e-bigger.e-chip-list.e-rtl.e-chip .e-chip-delete,
.e-bigger.e-chip-list.e-rtl .e-chip .e-chip-delete {
  margin: 0 8px 0 -4px;
}

.e-bigger .e-chip-list.e-rtl.e-chip .e-chip-avatar-wrap, .e-bigger .e-chip-list.e-rtl.e-chip.e-chip-avatar-wrap,
.e-bigger .e-chip-list.e-rtl .e-chip .e-chip-avatar-wrap,
.e-bigger .e-chip-list.e-rtl .e-chip.e-chip-avatar-wrap,
.e-bigger.e-chip-list.e-rtl.e-chip .e-chip-avatar-wrap,
.e-bigger.e-chip-list.e-rtl.e-chip.e-chip-avatar-wrap,
.e-bigger.e-chip-list.e-rtl .e-chip .e-chip-avatar-wrap,
.e-bigger.e-chip-list.e-rtl .e-chip.e-chip-avatar-wrap {
  border-radius: 0 16px 16px 0;
}

.e-bigger .e-chip-list.e-rtl.e-multi-selection .e-chip::before,
.e-bigger.e-chip-list.e-rtl.e-multi-selection .e-chip::before {
  margin: 0 -8px 0 8px;
  margin-top: 0;
}

.e-bigger .e-chip-list.e-rtl.e-multi-selection .e-chip.e-chip-avatar-wrap::before,
.e-bigger.e-chip-list.e-rtl.e-multi-selection .e-chip.e-chip-avatar-wrap::before {
  margin: 0 -12px 0 8px;
  margin-top: 0;
}

.e-chip-list.e-chip,
.e-chip-list .e-chip {
  background-color: #414040;
  border-color: transparent;
  color: #fff;
}

.e-chip-list.e-chip .e-chip-icon,
.e-chip-list.e-chip .e-chip-delete,
.e-chip-list .e-chip .e-chip-icon,
.e-chip-list .e-chip .e-chip-delete {
  color: #fff;
}

.e-chip-list.e-chip .e-chip-delete.e-dlt-btn,
.e-chip-list .e-chip .e-chip-delete.e-dlt-btn {
  color: rgba(255, 255, 255, 0.8);
}

.e-chip-list.e-chip .e-chip-avatar,
.e-chip-list .e-chip .e-chip-avatar {
  background-color: #605e5e;
  color: #fff;
}

.e-chip-list.e-chip:not(.e-active) .e-chip-delete.e-dlt-btn:hover,
.e-chip-list .e-chip:not(.e-active) .e-chip-delete.e-dlt-btn:hover {
  color: #fff;
}

.e-chip-list.e-chip:not(.e-active) .e-chip-delete.e-dlt-btn:active,
.e-chip-list .e-chip:not(.e-active) .e-chip-delete.e-dlt-btn:active {
  color: #fff;
}

.e-chip-list.e-chip:hover,
.e-chip-list .e-chip:hover {
  background-color: #514f4f;
  border-color: transparent;
  color: #fff;
}

.e-chip-list.e-chip:hover .e-chip-icon,
.e-chip-list.e-chip:hover .e-chip-delete,
.e-chip-list .e-chip:hover .e-chip-icon,
.e-chip-list .e-chip:hover .e-chip-delete {
  color: #fff;
}

.e-chip-list.e-chip:hover .e-chip-avatar,
.e-chip-list .e-chip:hover .e-chip-avatar {
  background-color: #706d6d;
  color: #fff;
}

.e-chip-list.e-chip.e-focused,
.e-chip-list .e-chip.e-focused {
  background-color: #414040;
  border-color: #c8c8c8;
  color: #fff;
  box-shadow: none;
}

.e-chip-list.e-chip.e-focused .e-chip-icon,
.e-chip-list.e-chip.e-focused .e-chip-delete,
.e-chip-list .e-chip.e-focused .e-chip-icon,
.e-chip-list .e-chip.e-focused .e-chip-delete {
  color: #fff;
}

.e-chip-list.e-chip.e-focused .e-chip-avatar,
.e-chip-list .e-chip.e-focused .e-chip-avatar {
  background-color: #706d6d;
  color: #fff;
}

.e-chip-list.e-chip.e-active,
.e-chip-list .e-chip.e-active {
  background-color: #6f6c6c;
  border-color: transparent;
  color: #fff;
  box-shadow: none;
}

.e-chip-list.e-chip.e-active .e-chip-icon,
.e-chip-list.e-chip.e-active .e-chip-delete,
.e-chip-list .e-chip.e-active .e-chip-icon,
.e-chip-list .e-chip.e-active .e-chip-delete {
  color: #fff;
}

.e-chip-list.e-chip.e-active .e-chip-avatar,
.e-chip-list .e-chip.e-active .e-chip-avatar {
  background-color: #8e8b8b;
  color: #fff;
}

.e-chip-list.e-chip.e-focused.e-active,
.e-chip-list .e-chip.e-focused.e-active {
  background-color: #6f6c6c;
  border-color: #c8c8c8;
  color: #fff;
  box-shadow: none;
}

.e-chip-list.e-chip.e-focused.e-active .e-chip-icon,
.e-chip-list.e-chip.e-focused.e-active .e-chip-delete,
.e-chip-list .e-chip.e-focused.e-active .e-chip-icon,
.e-chip-list .e-chip.e-focused.e-active .e-chip-delete {
  color: #fff;
}

.e-chip-list.e-chip.e-focused.e-active .e-chip-avatar,
.e-chip-list .e-chip.e-focused.e-active .e-chip-avatar {
  background-color: #8e8b8b;
  color: #fff;
}

.e-chip-list.e-chip:active,
.e-chip-list .e-chip:active {
  background-color: #6f6c6c;
  border-color: transparent;
  color: #fff;
  box-shadow: none;
}

.e-chip-list.e-chip:active .e-chip-icon,
.e-chip-list.e-chip:active .e-chip-delete,
.e-chip-list .e-chip:active .e-chip-icon,
.e-chip-list .e-chip:active .e-chip-delete {
  color: #fff;
}

.e-chip-list.e-chip:active .e-chip-avatar,
.e-chip-list .e-chip:active .e-chip-avatar {
  background-color: #8e8b8b;
  color: #fff;
}

.e-chip-list.e-chip.e-disabled,
.e-chip-list .e-chip.e-disabled {
  background-color: #333232;
  border-color: transparent;
  color: #9a9a9a;
  opacity: 1;
  pointer-events: none;
}

.e-chip-list.e-chip.e-disabled .e-chip-icon,
.e-chip-list.e-chip.e-disabled .e-chip-delete,
.e-chip-list .e-chip.e-disabled .e-chip-icon,
.e-chip-list .e-chip.e-disabled .e-chip-delete {
  color: #9a9a9a;
}

.e-chip-list.e-chip.e-disabled .e-chip-avatar,
.e-chip-list .e-chip.e-disabled .e-chip-avatar {
  background-color: #525050;
  color: #9a9a9a;
}

.e-chip-list.e-chip.e-outline,
.e-chip-list .e-chip.e-outline {
  background-color: transparent;
  border-color: #9a9a9a;
  color: #fff;
  border-width: 1px;
}

.e-chip-list.e-chip.e-outline .e-chip-icon,
.e-chip-list.e-chip.e-outline .e-chip-delete,
.e-chip-list .e-chip.e-outline .e-chip-icon,
.e-chip-list .e-chip.e-outline .e-chip-delete {
  color: #fff;
}

.e-chip-list.e-chip.e-outline .e-chip-delete.e-dlt-btn,
.e-chip-list .e-chip.e-outline .e-chip-delete.e-dlt-btn {
  color: rgba(255, 255, 255, 0.8);
}

.e-chip-list.e-chip.e-outline .e-chip-avatar,
.e-chip-list .e-chip.e-outline .e-chip-avatar {
  background-color: #605e5e;
  color: #fff;
}

.e-chip-list.e-chip.e-outline:not(.e-active) .e-chip-delete.e-dlt-btn:hover,
.e-chip-list .e-chip.e-outline:not(.e-active) .e-chip-delete.e-dlt-btn:hover {
  color: #fff;
}

.e-chip-list.e-chip.e-outline:not(.e-active) .e-chip-delete.e-dlt-btn:active,
.e-chip-list .e-chip.e-outline:not(.e-active) .e-chip-delete.e-dlt-btn:active {
  color: #fff;
}

.e-chip-list.e-chip.e-outline:hover,
.e-chip-list .e-chip.e-outline:hover {
  background-color: #514f4f;
  border-color: #9a9a9a;
  color: #fff;
}

.e-chip-list.e-chip.e-outline:hover .e-chip-icon,
.e-chip-list.e-chip.e-outline:hover .e-chip-delete,
.e-chip-list .e-chip.e-outline:hover .e-chip-icon,
.e-chip-list .e-chip.e-outline:hover .e-chip-delete {
  color: #fff;
}

.e-chip-list.e-chip.e-outline:hover .e-chip-avatar,
.e-chip-list .e-chip.e-outline:hover .e-chip-avatar {
  background-color: #605e5e;
  color: #fff;
}

.e-chip-list.e-chip.e-outline.e-focused,
.e-chip-list .e-chip.e-outline.e-focused {
  background-color: #414040;
  border-color: #c8c8c8;
  color: #fff;
}

.e-chip-list.e-chip.e-outline.e-focused .e-chip-icon,
.e-chip-list.e-chip.e-outline.e-focused .e-chip-delete,
.e-chip-list .e-chip.e-outline.e-focused .e-chip-icon,
.e-chip-list .e-chip.e-outline.e-focused .e-chip-delete {
  color: #fff;
}

.e-chip-list.e-chip.e-outline.e-focused .e-chip-avatar,
.e-chip-list .e-chip.e-outline.e-focused .e-chip-avatar {
  background-color: #605e5e;
  color: #fff;
}

.e-chip-list.e-chip.e-outline.e-active,
.e-chip-list .e-chip.e-outline.e-active {
  background-color: #6f6c6c;
  border-color: #9a9a9a;
  color: #fff;
}

.e-chip-list.e-chip.e-outline.e-active .e-chip-icon,
.e-chip-list.e-chip.e-outline.e-active .e-chip-delete,
.e-chip-list .e-chip.e-outline.e-active .e-chip-icon,
.e-chip-list .e-chip.e-outline.e-active .e-chip-delete {
  color: #fff;
}

.e-chip-list.e-chip.e-outline.e-active .e-chip-avatar,
.e-chip-list .e-chip.e-outline.e-active .e-chip-avatar {
  background-color: #8e8b8b;
  color: #fff;
}

.e-chip-list.e-chip.e-outline.e-focused.e-active,
.e-chip-list .e-chip.e-outline.e-focused.e-active {
  background-color: #6f6c6c;
  border-color: #c8c8c8;
  color: #fff;
}

.e-chip-list.e-chip.e-outline.e-focused.e-active .e-chip-icon,
.e-chip-list.e-chip.e-outline.e-focused.e-active .e-chip-delete,
.e-chip-list .e-chip.e-outline.e-focused.e-active .e-chip-icon,
.e-chip-list .e-chip.e-outline.e-focused.e-active .e-chip-delete {
  color: #fff;
}

.e-chip-list.e-chip.e-outline.e-focused.e-active .e-chip-avatar,
.e-chip-list .e-chip.e-outline.e-focused.e-active .e-chip-avatar {
  background-color: #8e8b8b;
  color: #fff;
}

.e-chip-list.e-chip.e-outline:active,
.e-chip-list .e-chip.e-outline:active {
  background-color: #6f6c6c;
  border-color: #9a9a9a;
  color: #fff;
}

.e-chip-list.e-chip.e-outline:active .e-chip-icon,
.e-chip-list.e-chip.e-outline:active .e-chip-delete,
.e-chip-list .e-chip.e-outline:active .e-chip-icon,
.e-chip-list .e-chip.e-outline:active .e-chip-delete {
  color: #fff;
}

.e-chip-list.e-chip.e-outline:active .e-chip-avatar,
.e-chip-list .e-chip.e-outline:active .e-chip-avatar {
  background-color: #8e8b8b;
  color: #fff;
}

.e-chip-list.e-chip.e-outline.e-disabled,
.e-chip-list .e-chip.e-outline.e-disabled {
  background-color: transparent;
  border-color: #9a9a9a;
  color: #9a9a9a;
}

.e-chip-list.e-chip.e-outline.e-disabled .e-chip-icon,
.e-chip-list.e-chip.e-outline.e-disabled .e-chip-delete,
.e-chip-list .e-chip.e-outline.e-disabled .e-chip-icon,
.e-chip-list .e-chip.e-outline.e-disabled .e-chip-delete {
  color: #9a9a9a;
}

.e-chip-list.e-chip.e-outline.e-disabled .e-chip-avatar,
.e-chip-list .e-chip.e-outline.e-disabled .e-chip-avatar {
  background-color: rgba(31, 31, 31, 0);
  color: #9a9a9a;
}

.e-chip-list.e-selection .e-chip.e-active {
  background-color: #0074cc;
  border-color: transparent;
  color: #fff;
}

.e-chip-list.e-selection .e-chip.e-active .e-chip-icon,
.e-chip-list.e-selection .e-chip.e-active .e-chip-delete {
  color: #fff;
}

.e-chip-list.e-selection .e-chip.e-active .e-chip-avatar {
  background-color: #0a95ff;
  color: #fff;
}

.e-chip-list.e-selection .e-chip.e-active.e-focused {
  background-color: #0074cc;
  border-color: #c8c8c8;
  color: #fff;
}

.e-chip-list.e-selection .e-chip.e-active.e-focused .e-chip-icon,
.e-chip-list.e-selection .e-chip.e-active.e-focused .e-chip-delete {
  color: #fff;
}

.e-chip-list.e-selection .e-chip.e-active.e-focused .e-chip-avatar {
  background-color: #0a95ff;
  color: #fff;
}

.e-chip-list.e-selection .e-chip.e-active.e-disabled {
  background-color: #333232;
  border-color: transparent;
  color: #9a9a9a;
}

.e-chip-list.e-selection .e-chip.e-active.e-disabled .e-chip-icon,
.e-chip-list.e-selection .e-chip.e-active.e-disabled .e-chip-delete {
  color: #9a9a9a;
}

.e-chip-list.e-selection .e-chip.e-active.e-disabled .e-chip-avatar {
  background-color: #525050;
  color: #9a9a9a;
}

.e-chip-list.e-selection .e-chip.e-active.e-outline {
  background-color: #0074cc;
  border-color: #38a9ff;
  color: #fff;
}

.e-chip-list.e-selection .e-chip.e-active.e-outline .e-chip-icon,
.e-chip-list.e-selection .e-chip.e-active.e-outline .e-chip-delete {
  color: #fff;
}

.e-chip-list.e-selection .e-chip.e-active.e-outline .e-chip-avatar {
  background-color: #0a95ff;
  color: #fff;
}

.e-chip-list.e-selection .e-chip.e-active.e-outline.e-focused {
  background-color: #0074cc;
  border-color: #c8c8c8;
  color: #fff;
}

.e-chip-list.e-selection .e-chip.e-active.e-outline.e-focused .e-chip-icon,
.e-chip-list.e-selection .e-chip.e-active.e-outline.e-focused .e-chip-delete {
  color: #fff;
}

.e-chip-list.e-selection .e-chip.e-active.e-outline.e-focused .e-chip-avatar {
  background-color: #0a95ff;
  color: #fff;
}

.e-chip-list.e-selection .e-chip.e-active.e-outline.e-disabled {
  background-color: transparent;
  border-color: #9a9a9a;
  color: #9a9a9a;
}

.e-chip-list.e-selection .e-chip.e-active.e-outline.e-disabled .e-chip-icon,
.e-chip-list.e-selection .e-chip.e-active.e-outline.e-disabled .e-chip-delete {
  color: #9a9a9a;
}

.e-chip-list.e-selection .e-chip.e-active.e-outline.e-disabled .e-chip-avatar {
  background-color: rgba(31, 31, 31, 0);
  color: #9a9a9a;
}

.e-chip-list.e-selection .e-chip:active {
  background-color: #514f4f;
  border-color: transparent;
  color: #fff;
}

.e-chip-list.e-selection .e-chip:active .e-chip-icon,
.e-chip-list.e-selection .e-chip:active .e-chip-delete {
  color: #fff;
}

.e-chip-list.e-selection .e-chip:active .e-chip-avatar {
  background-color: #706d6d;
  color: #fff;
}

.e-chip-list.e-selection .e-chip:active.e-outline {
  background-color: #6f6c6c;
  border-color: #9a9a9a;
  color: #fff;
}

.e-chip-list.e-selection .e-chip:active.e-outline .e-chip-icon,
.e-chip-list.e-selection .e-chip:active.e-outline .e-chip-delete {
  color: #fff;
}

.e-chip-list.e-selection .e-chip:active.e-outline .e-chip-avatar {
  background-color: #8e8b8b;
  color: #fff;
}

.e-chip-list.e-chip.e-primary,
.e-chip-list .e-chip.e-primary {
  background-color: #0074cc;
  border-color: transparent;
  color: #fff;
}

.e-chip-list.e-chip.e-primary .e-chip-icon,
.e-chip-list.e-chip.e-primary .e-chip-delete,
.e-chip-list .e-chip.e-primary .e-chip-icon,
.e-chip-list .e-chip.e-primary .e-chip-delete {
  color: #fff;
}

.e-chip-list.e-chip.e-primary .e-chip-delete.e-dlt-btn,
.e-chip-list .e-chip.e-primary .e-chip-delete.e-dlt-btn {
  color: rgba(255, 255, 255, 0.8);
}

.e-chip-list.e-chip.e-primary .e-chip-avatar,
.e-chip-list .e-chip.e-primary .e-chip-avatar {
  background-color: #0a95ff;
  color: #fff;
}

.e-chip-list.e-chip.e-primary:not(.e-active) .e-chip-delete.e-dlt-btn:hover,
.e-chip-list .e-chip.e-primary:not(.e-active) .e-chip-delete.e-dlt-btn:hover {
  color: #fff;
}

.e-chip-list.e-chip.e-primary:not(.e-active) .e-chip-delete.e-dlt-btn:active,
.e-chip-list .e-chip.e-primary:not(.e-active) .e-chip-delete.e-dlt-btn:active {
  color: #fff;
}

.e-chip-list.e-chip.e-primary:hover,
.e-chip-list .e-chip.e-primary:hover {
  background-color: #0063ad;
  border-color: transparent;
  color: #fff;
}

.e-chip-list.e-chip.e-primary:hover .e-chip-icon,
.e-chip-list.e-chip.e-primary:hover .e-chip-delete,
.e-chip-list .e-chip.e-primary:hover .e-chip-icon,
.e-chip-list .e-chip.e-primary:hover .e-chip-delete {
  color: #fff;
}

.e-chip-list.e-chip.e-primary:hover .e-chip-avatar,
.e-chip-list .e-chip.e-primary:hover .e-chip-avatar {
  background-color: #0085eb;
  color: #fff;
}

.e-chip-list.e-chip.e-primary.e-focused,
.e-chip-list .e-chip.e-primary.e-focused {
  background-color: #0074cc;
  border-color: #c8c8c8;
  color: #fff;
}

.e-chip-list.e-chip.e-primary.e-focused .e-chip-icon,
.e-chip-list.e-chip.e-primary.e-focused .e-chip-delete,
.e-chip-list .e-chip.e-primary.e-focused .e-chip-icon,
.e-chip-list .e-chip.e-primary.e-focused .e-chip-delete {
  color: #fff;
}

.e-chip-list.e-chip.e-primary.e-focused .e-chip-avatar,
.e-chip-list .e-chip.e-primary.e-focused .e-chip-avatar {
  background-color: #0a95ff;
  color: #fff;
}

.e-chip-list.e-chip.e-primary.e-active,
.e-chip-list .e-chip.e-primary.e-active {
  background-color: #005799;
  border-color: transparent;
  color: #fff;
}

.e-chip-list.e-chip.e-primary.e-active .e-chip-icon,
.e-chip-list.e-chip.e-primary.e-active .e-chip-delete,
.e-chip-list .e-chip.e-primary.e-active .e-chip-icon,
.e-chip-list .e-chip.e-primary.e-active .e-chip-delete {
  color: #fff;
}

.e-chip-list.e-chip.e-primary.e-active .e-chip-avatar,
.e-chip-list .e-chip.e-primary.e-active .e-chip-avatar {
  background-color: #007ad6;
  color: #fff;
}

.e-chip-list.e-chip.e-primary.e-focused.e-active,
.e-chip-list .e-chip.e-primary.e-focused.e-active {
  background-color: #005799;
  border-color: #c8c8c8;
  color: #fff;
}

.e-chip-list.e-chip.e-primary.e-focused.e-active .e-chip-icon,
.e-chip-list.e-chip.e-primary.e-focused.e-active .e-chip-delete,
.e-chip-list .e-chip.e-primary.e-focused.e-active .e-chip-icon,
.e-chip-list .e-chip.e-primary.e-focused.e-active .e-chip-delete {
  color: #fff;
}

.e-chip-list.e-chip.e-primary.e-focused.e-active .e-chip-avatar,
.e-chip-list .e-chip.e-primary.e-focused.e-active .e-chip-avatar {
  background-color: #007ad6;
  color: #fff;
}

.e-chip-list.e-chip.e-primary:active,
.e-chip-list .e-chip.e-primary:active {
  background-color: #005799;
  border-color: transparent;
  color: #fff;
}

.e-chip-list.e-chip.e-primary:active .e-chip-icon,
.e-chip-list.e-chip.e-primary:active .e-chip-delete,
.e-chip-list .e-chip.e-primary:active .e-chip-icon,
.e-chip-list .e-chip.e-primary:active .e-chip-delete {
  color: #fff;
}

.e-chip-list.e-chip.e-primary:active .e-chip-avatar,
.e-chip-list .e-chip.e-primary:active .e-chip-avatar {
  background-color: #007ad6;
  color: #fff;
}

.e-chip-list.e-chip.e-primary.e-disabled,
.e-chip-list .e-chip.e-primary.e-disabled {
  background-color: #333232;
  border-color: transparent;
  color: #9a9a9a;
}

.e-chip-list.e-chip.e-primary.e-disabled .e-chip-icon,
.e-chip-list.e-chip.e-primary.e-disabled .e-chip-delete,
.e-chip-list .e-chip.e-primary.e-disabled .e-chip-icon,
.e-chip-list .e-chip.e-primary.e-disabled .e-chip-delete {
  color: #9a9a9a;
}

.e-chip-list.e-chip.e-primary.e-disabled .e-chip-avatar,
.e-chip-list .e-chip.e-primary.e-disabled .e-chip-avatar {
  background-color: #525050;
  color: #9a9a9a;
}

.e-chip-list.e-chip.e-primary.e-outline,
.e-chip-list .e-chip.e-primary.e-outline {
  background-color: transparent;
  border-color: #38a9ff;
  color: #38a9ff;
}

.e-chip-list.e-chip.e-primary.e-outline .e-chip-icon,
.e-chip-list.e-chip.e-primary.e-outline .e-chip-delete,
.e-chip-list .e-chip.e-primary.e-outline .e-chip-icon,
.e-chip-list .e-chip.e-primary.e-outline .e-chip-delete {
  color: #38a9ff;
}

.e-chip-list.e-chip.e-primary.e-outline .e-chip-delete.e-dlt-btn,
.e-chip-list .e-chip.e-primary.e-outline .e-chip-delete.e-dlt-btn {
  color: rgba(56, 169, 255, 0.8);
}

.e-chip-list.e-chip.e-primary.e-outline .e-chip-avatar,
.e-chip-list .e-chip.e-primary.e-outline .e-chip-avatar {
  background-color: #38a9ff;
  color: #fff;
}

.e-chip-list.e-chip.e-primary.e-outline:not(.e-active) .e-chip-delete.e-dlt-btn:hover,
.e-chip-list .e-chip.e-primary.e-outline:not(.e-active) .e-chip-delete.e-dlt-btn:hover {
  color: #fff;
}

.e-chip-list.e-chip.e-primary.e-outline:not(.e-active) .e-chip-delete.e-dlt-btn:active,
.e-chip-list .e-chip.e-primary.e-outline:not(.e-active) .e-chip-delete.e-dlt-btn:active {
  color: #fff;
}

.e-chip-list.e-chip.e-primary.e-outline:hover,
.e-chip-list .e-chip.e-primary.e-outline:hover {
  background-color: #0063ad;
  border-color: transparent;
  color: #fff;
}

.e-chip-list.e-chip.e-primary.e-outline:hover .e-chip-icon,
.e-chip-list.e-chip.e-primary.e-outline:hover .e-chip-delete,
.e-chip-list .e-chip.e-primary.e-outline:hover .e-chip-icon,
.e-chip-list .e-chip.e-primary.e-outline:hover .e-chip-delete {
  color: #fff;
}

.e-chip-list.e-chip.e-primary.e-outline:hover .e-chip-avatar,
.e-chip-list .e-chip.e-primary.e-outline:hover .e-chip-avatar {
  background-color: #38a9ff;
  color: #fff;
}

.e-chip-list.e-chip.e-primary.e-outline.e-focused,
.e-chip-list .e-chip.e-primary.e-outline.e-focused {
  background-color: #0074cc;
  border-color: #c8c8c8;
  color: #fff;
}

.e-chip-list.e-chip.e-primary.e-outline.e-focused .e-chip-icon,
.e-chip-list.e-chip.e-primary.e-outline.e-focused .e-chip-delete,
.e-chip-list .e-chip.e-primary.e-outline.e-focused .e-chip-icon,
.e-chip-list .e-chip.e-primary.e-outline.e-focused .e-chip-delete {
  color: #fff;
}

.e-chip-list.e-chip.e-primary.e-outline.e-focused .e-chip-avatar,
.e-chip-list .e-chip.e-primary.e-outline.e-focused .e-chip-avatar {
  background-color: #0a95ff;
  color: #fff;
}

.e-chip-list.e-chip.e-primary.e-outline.e-active,
.e-chip-list .e-chip.e-primary.e-outline.e-active {
  background-color: #0074cc;
  border-color: transparent;
  color: #fff;
}

.e-chip-list.e-chip.e-primary.e-outline.e-active .e-chip-icon,
.e-chip-list.e-chip.e-primary.e-outline.e-active .e-chip-delete,
.e-chip-list .e-chip.e-primary.e-outline.e-active .e-chip-icon,
.e-chip-list .e-chip.e-primary.e-outline.e-active .e-chip-delete {
  color: #fff;
}

.e-chip-list.e-chip.e-primary.e-outline.e-active .e-chip-avatar,
.e-chip-list .e-chip.e-primary.e-outline.e-active .e-chip-avatar {
  background-color: #0a95ff;
  color: #fff;
}

.e-chip-list.e-chip.e-primary.e-outline.e-focused.e-active,
.e-chip-list .e-chip.e-primary.e-outline.e-focused.e-active {
  background-color: #0074cc;
  border-color: #c8c8c8;
  color: #fff;
}

.e-chip-list.e-chip.e-primary.e-outline.e-focused.e-active .e-chip-icon,
.e-chip-list.e-chip.e-primary.e-outline.e-focused.e-active .e-chip-delete,
.e-chip-list .e-chip.e-primary.e-outline.e-focused.e-active .e-chip-icon,
.e-chip-list .e-chip.e-primary.e-outline.e-focused.e-active .e-chip-delete {
  color: #fff;
}

.e-chip-list.e-chip.e-primary.e-outline.e-focused.e-active .e-chip-avatar,
.e-chip-list .e-chip.e-primary.e-outline.e-focused.e-active .e-chip-avatar {
  background-color: #0a95ff;
  color: #fff;
}

.e-chip-list.e-chip.e-primary.e-outline:active,
.e-chip-list .e-chip.e-primary.e-outline:active {
  background-color: #005799;
  border-color: transparent;
  color: #fff;
}

.e-chip-list.e-chip.e-primary.e-outline:active .e-chip-icon,
.e-chip-list.e-chip.e-primary.e-outline:active .e-chip-delete,
.e-chip-list .e-chip.e-primary.e-outline:active .e-chip-icon,
.e-chip-list .e-chip.e-primary.e-outline:active .e-chip-delete {
  color: #fff;
}

.e-chip-list.e-chip.e-primary.e-outline:active .e-chip-avatar,
.e-chip-list .e-chip.e-primary.e-outline:active .e-chip-avatar {
  background-color: #007ad6;
  color: #fff;
}

.e-chip-list.e-chip.e-primary.e-outline.e-disabled,
.e-chip-list .e-chip.e-primary.e-outline.e-disabled {
  background-color: transparent;
  border-color: #9a9a9a;
  color: #9a9a9a;
}

.e-chip-list.e-chip.e-primary.e-outline.e-disabled .e-chip-icon,
.e-chip-list.e-chip.e-primary.e-outline.e-disabled .e-chip-delete,
.e-chip-list .e-chip.e-primary.e-outline.e-disabled .e-chip-icon,
.e-chip-list .e-chip.e-primary.e-outline.e-disabled .e-chip-delete {
  color: #9a9a9a;
}

.e-chip-list.e-chip.e-primary.e-outline.e-disabled .e-chip-avatar,
.e-chip-list .e-chip.e-primary.e-outline.e-disabled .e-chip-avatar {
  background-color: rgba(31, 31, 31, 0);
  color: #9a9a9a;
}

.e-chip-list.e-chip.e-success,
.e-chip-list .e-chip.e-success {
  background-color: #37844d;
  border-color: transparent;
  color: #fff;
}

.e-chip-list.e-chip.e-success .e-chip-icon,
.e-chip-list.e-chip.e-success .e-chip-delete,
.e-chip-list .e-chip.e-success .e-chip-icon,
.e-chip-list .e-chip.e-success .e-chip-delete {
  color: #fff;
}

.e-chip-list.e-chip.e-success .e-chip-delete.e-dlt-btn,
.e-chip-list .e-chip.e-success .e-chip-delete.e-dlt-btn {
  color: rgba(255, 255, 255, 0.8);
}

.e-chip-list.e-chip.e-success .e-chip-avatar,
.e-chip-list .e-chip.e-success .e-chip-avatar {
  background-color: #49af66;
  color: #fff;
}

.e-chip-list.e-chip.e-success:not(.e-active) .e-chip-delete.e-dlt-btn:hover,
.e-chip-list .e-chip.e-success:not(.e-active) .e-chip-delete.e-dlt-btn:hover {
  color: #fff;
}

.e-chip-list.e-chip.e-success:not(.e-active) .e-chip-delete.e-dlt-btn:active,
.e-chip-list .e-chip.e-success:not(.e-active) .e-chip-delete.e-dlt-btn:active {
  color: #fff;
}

.e-chip-list.e-chip.e-success:hover,
.e-chip-list .e-chip.e-success:hover {
  background-color: #2e6e40;
  border-color: transparent;
  color: #fff;
}

.e-chip-list.e-chip.e-success:hover .e-chip-icon,
.e-chip-list.e-chip.e-success:hover .e-chip-delete,
.e-chip-list .e-chip.e-success:hover .e-chip-icon,
.e-chip-list .e-chip.e-success:hover .e-chip-delete {
  color: #fff;
}

.e-chip-list.e-chip.e-success:hover .e-chip-avatar,
.e-chip-list .e-chip.e-success:hover .e-chip-avatar {
  background-color: #409a5a;
  color: #fff;
}

.e-chip-list.e-chip.e-success.e-focused,
.e-chip-list .e-chip.e-success.e-focused {
  background-color: #37844d;
  border-color: #201f1f;
  color: #fff;
}

.e-chip-list.e-chip.e-success.e-focused .e-chip-icon,
.e-chip-list.e-chip.e-success.e-focused .e-chip-delete,
.e-chip-list .e-chip.e-success.e-focused .e-chip-icon,
.e-chip-list .e-chip.e-success.e-focused .e-chip-delete {
  color: #fff;
}

.e-chip-list.e-chip.e-success.e-focused .e-chip-avatar,
.e-chip-list .e-chip.e-success.e-focused .e-chip-avatar {
  background-color: #49af66;
  color: #fff;
}

.e-chip-list.e-chip.e-success.e-active,
.e-chip-list .e-chip.e-success.e-active {
  background-color: #255934;
  border-color: transparent;
  color: #fff;
}

.e-chip-list.e-chip.e-success.e-active .e-chip-icon,
.e-chip-list.e-chip.e-success.e-active .e-chip-delete,
.e-chip-list .e-chip.e-success.e-active .e-chip-icon,
.e-chip-list .e-chip.e-success.e-active .e-chip-delete {
  color: #fff;
}

.e-chip-list.e-chip.e-success.e-active .e-chip-avatar,
.e-chip-list .e-chip.e-success.e-active .e-chip-avatar {
  background-color: #37844d;
  color: #fff;
}

.e-chip-list.e-chip.e-success.e-focused.e-active,
.e-chip-list .e-chip.e-success.e-focused.e-active {
  background-color: #255934;
  border-color: #201f1f;
  color: #fff;
}

.e-chip-list.e-chip.e-success.e-focused.e-active .e-chip-icon,
.e-chip-list.e-chip.e-success.e-focused.e-active .e-chip-delete,
.e-chip-list .e-chip.e-success.e-focused.e-active .e-chip-icon,
.e-chip-list .e-chip.e-success.e-focused.e-active .e-chip-delete {
  color: #fff;
}

.e-chip-list.e-chip.e-success.e-focused.e-active .e-chip-avatar,
.e-chip-list .e-chip.e-success.e-focused.e-active .e-chip-avatar {
  background-color: #37844d;
  color: #fff;
}

.e-chip-list.e-chip.e-success:active,
.e-chip-list .e-chip.e-success:active {
  background-color: #255934;
  border-color: transparent;
  color: #fff;
}

.e-chip-list.e-chip.e-success:active .e-chip-icon,
.e-chip-list.e-chip.e-success:active .e-chip-delete,
.e-chip-list .e-chip.e-success:active .e-chip-icon,
.e-chip-list .e-chip.e-success:active .e-chip-delete {
  color: #fff;
}

.e-chip-list.e-chip.e-success:active .e-chip-avatar,
.e-chip-list .e-chip.e-success:active .e-chip-avatar {
  background-color: #37844d;
  color: #fff;
}

.e-chip-list.e-chip.e-success.e-disabled,
.e-chip-list .e-chip.e-success.e-disabled {
  background-color: #333232;
  border-color: transparent;
  color: #9a9a9a;
}

.e-chip-list.e-chip.e-success.e-disabled .e-chip-icon,
.e-chip-list.e-chip.e-success.e-disabled .e-chip-delete,
.e-chip-list .e-chip.e-success.e-disabled .e-chip-icon,
.e-chip-list .e-chip.e-success.e-disabled .e-chip-delete {
  color: #9a9a9a;
}

.e-chip-list.e-chip.e-success.e-disabled .e-chip-avatar,
.e-chip-list .e-chip.e-success.e-disabled .e-chip-avatar {
  background-color: #525050;
  color: #9a9a9a;
}

.e-chip-list.e-chip.e-success.e-outline,
.e-chip-list .e-chip.e-success.e-outline {
  background-color: transparent;
  border-color: #8eff8d;
  color: #8eff8d;
}

.e-chip-list.e-chip.e-success.e-outline .e-chip-icon,
.e-chip-list.e-chip.e-success.e-outline .e-chip-delete,
.e-chip-list .e-chip.e-success.e-outline .e-chip-icon,
.e-chip-list .e-chip.e-success.e-outline .e-chip-delete {
  color: #8eff8d;
}

.e-chip-list.e-chip.e-success.e-outline .e-chip-delete.e-dlt-btn,
.e-chip-list .e-chip.e-success.e-outline .e-chip-delete.e-dlt-btn {
  color: rgba(142, 255, 141, 0.8);
}

.e-chip-list.e-chip.e-success.e-outline .e-chip-avatar,
.e-chip-list .e-chip.e-success.e-outline .e-chip-avatar {
  background-color: #37844d;
  color: #fff;
}

.e-chip-list.e-chip.e-success.e-outline:not(.e-active) .e-chip-delete.e-dlt-btn:hover,
.e-chip-list .e-chip.e-success.e-outline:not(.e-active) .e-chip-delete.e-dlt-btn:hover {
  color: #fff;
}

.e-chip-list.e-chip.e-success.e-outline:not(.e-active) .e-chip-delete.e-dlt-btn:active,
.e-chip-list .e-chip.e-success.e-outline:not(.e-active) .e-chip-delete.e-dlt-btn:active {
  color: #fff;
}

.e-chip-list.e-chip.e-success.e-outline:hover,
.e-chip-list .e-chip.e-success.e-outline:hover {
  background-color: #2e6e40;
  border-color: #37844d;
  color: #fff;
}

.e-chip-list.e-chip.e-success.e-outline:hover .e-chip-icon,
.e-chip-list.e-chip.e-success.e-outline:hover .e-chip-delete,
.e-chip-list .e-chip.e-success.e-outline:hover .e-chip-icon,
.e-chip-list .e-chip.e-success.e-outline:hover .e-chip-delete {
  color: #fff;
}

.e-chip-list.e-chip.e-success.e-outline:hover .e-chip-avatar,
.e-chip-list .e-chip.e-success.e-outline:hover .e-chip-avatar {
  background-color: #37844d;
  color: #fff;
}

.e-chip-list.e-chip.e-success.e-outline.e-focused,
.e-chip-list .e-chip.e-success.e-outline.e-focused {
  background-color: #37844d;
  border-color: #201f1f;
  color: #fff;
}

.e-chip-list.e-chip.e-success.e-outline.e-focused .e-chip-icon,
.e-chip-list.e-chip.e-success.e-outline.e-focused .e-chip-delete,
.e-chip-list .e-chip.e-success.e-outline.e-focused .e-chip-icon,
.e-chip-list .e-chip.e-success.e-outline.e-focused .e-chip-delete {
  color: #fff;
}

.e-chip-list.e-chip.e-success.e-outline.e-focused .e-chip-avatar,
.e-chip-list .e-chip.e-success.e-outline.e-focused .e-chip-avatar {
  background-color: #49af66;
  color: #fff;
}

.e-chip-list.e-chip.e-success.e-outline.e-active,
.e-chip-list .e-chip.e-success.e-outline.e-active {
  background-color: #37844d;
  border-color: transparent;
  color: #fff;
}

.e-chip-list.e-chip.e-success.e-outline.e-active .e-chip-icon,
.e-chip-list.e-chip.e-success.e-outline.e-active .e-chip-delete,
.e-chip-list .e-chip.e-success.e-outline.e-active .e-chip-icon,
.e-chip-list .e-chip.e-success.e-outline.e-active .e-chip-delete {
  color: #fff;
}

.e-chip-list.e-chip.e-success.e-outline.e-active .e-chip-avatar,
.e-chip-list .e-chip.e-success.e-outline.e-active .e-chip-avatar {
  background-color: #49af66;
  color: #fff;
}

.e-chip-list.e-chip.e-success.e-outline.e-focused.e-active,
.e-chip-list .e-chip.e-success.e-outline.e-focused.e-active {
  background-color: #37844d;
  border-color: #201f1f;
  color: #fff;
}

.e-chip-list.e-chip.e-success.e-outline.e-focused.e-active .e-chip-icon,
.e-chip-list.e-chip.e-success.e-outline.e-focused.e-active .e-chip-delete,
.e-chip-list .e-chip.e-success.e-outline.e-focused.e-active .e-chip-icon,
.e-chip-list .e-chip.e-success.e-outline.e-focused.e-active .e-chip-delete {
  color: #fff;
}

.e-chip-list.e-chip.e-success.e-outline.e-focused.e-active .e-chip-avatar,
.e-chip-list .e-chip.e-success.e-outline.e-focused.e-active .e-chip-avatar {
  background-color: #49af66;
  color: #fff;
}

.e-chip-list.e-chip.e-success.e-outline:active,
.e-chip-list .e-chip.e-success.e-outline:active {
  background-color: #255934;
  border-color: transparent;
  color: #fff;
}

.e-chip-list.e-chip.e-success.e-outline:active .e-chip-icon,
.e-chip-list.e-chip.e-success.e-outline:active .e-chip-delete,
.e-chip-list .e-chip.e-success.e-outline:active .e-chip-icon,
.e-chip-list .e-chip.e-success.e-outline:active .e-chip-delete {
  color: #fff;
}

.e-chip-list.e-chip.e-success.e-outline:active .e-chip-avatar,
.e-chip-list .e-chip.e-success.e-outline:active .e-chip-avatar {
  background-color: #37844d;
  color: #fff;
}

.e-chip-list.e-chip.e-success.e-outline.e-disabled,
.e-chip-list .e-chip.e-success.e-outline.e-disabled {
  background-color: transparent;
  border-color: #9a9a9a;
  color: #9a9a9a;
}

.e-chip-list.e-chip.e-success.e-outline.e-disabled .e-chip-icon,
.e-chip-list.e-chip.e-success.e-outline.e-disabled .e-chip-delete,
.e-chip-list .e-chip.e-success.e-outline.e-disabled .e-chip-icon,
.e-chip-list .e-chip.e-success.e-outline.e-disabled .e-chip-delete {
  color: #9a9a9a;
}

.e-chip-list.e-chip.e-success.e-outline.e-disabled .e-chip-avatar,
.e-chip-list .e-chip.e-success.e-outline.e-disabled .e-chip-avatar {
  background-color: rgba(31, 31, 31, 0);
  color: #9a9a9a;
}

.e-chip-list.e-chip.e-info,
.e-chip-list .e-chip.e-info {
  background-color: #1e79cb;
  border-color: transparent;
  color: #fff;
}

.e-chip-list.e-chip.e-info .e-chip-icon,
.e-chip-list.e-chip.e-info .e-chip-delete,
.e-chip-list .e-chip.e-info .e-chip-icon,
.e-chip-list .e-chip.e-info .e-chip-delete {
  color: #fff;
}

.e-chip-list.e-chip.e-info .e-chip-delete.e-dlt-btn,
.e-chip-list .e-chip.e-info .e-chip-delete.e-dlt-btn {
  color: rgba(255, 255, 255, 0.8);
}

.e-chip-list.e-chip.e-info .e-chip-avatar,
.e-chip-list .e-chip.e-info .e-chip-avatar {
  background-color: #4397e3;
  color: #fff;
}

.e-chip-list.e-chip.e-info:not(.e-active) .e-chip-delete.e-dlt-btn:hover,
.e-chip-list .e-chip.e-info:not(.e-active) .e-chip-delete.e-dlt-btn:hover {
  color: #fff;
}

.e-chip-list.e-chip.e-info:not(.e-active) .e-chip-delete.e-dlt-btn:active,
.e-chip-list .e-chip.e-info:not(.e-active) .e-chip-delete.e-dlt-btn:active {
  color: #fff;
}

.e-chip-list.e-chip.e-info:hover,
.e-chip-list .e-chip.e-info:hover {
  background-color: #1a69b0;
  border-color: transparent;
  color: #fff;
}

.e-chip-list.e-chip.e-info:hover .e-chip-icon,
.e-chip-list.e-chip.e-info:hover .e-chip-delete,
.e-chip-list .e-chip.e-info:hover .e-chip-icon,
.e-chip-list .e-chip.e-info:hover .e-chip-delete {
  color: #fff;
}

.e-chip-list.e-chip.e-info:hover .e-chip-avatar,
.e-chip-list .e-chip.e-info:hover .e-chip-avatar {
  background-color: #2889df;
  color: #fff;
}

.e-chip-list.e-chip.e-info.e-focused,
.e-chip-list .e-chip.e-info.e-focused {
  background-color: #1e79cb;
  border-color: #201f1f;
  color: #fff;
}

.e-chip-list.e-chip.e-info.e-focused .e-chip-icon,
.e-chip-list.e-chip.e-info.e-focused .e-chip-delete,
.e-chip-list .e-chip.e-info.e-focused .e-chip-icon,
.e-chip-list .e-chip.e-info.e-focused .e-chip-delete {
  color: #fff;
}

.e-chip-list.e-chip.e-info.e-focused .e-chip-avatar,
.e-chip-list .e-chip.e-info.e-focused .e-chip-avatar {
  background-color: #4397e3;
  color: #fff;
}

.e-chip-list.e-chip.e-info.e-active,
.e-chip-list .e-chip.e-info.e-active {
  background-color: #165996;
  border-color: transparent;
  color: #fff;
}

.e-chip-list.e-chip.e-info.e-active .e-chip-icon,
.e-chip-list.e-chip.e-info.e-active .e-chip-delete,
.e-chip-list .e-chip.e-info.e-active .e-chip-icon,
.e-chip-list .e-chip.e-info.e-active .e-chip-delete {
  color: #fff;
}

.e-chip-list.e-chip.e-info.e-active .e-chip-avatar,
.e-chip-list .e-chip.e-info.e-active .e-chip-avatar {
  background-color: #1e79cb;
  color: #fff;
}

.e-chip-list.e-chip.e-info.e-focused.e-active,
.e-chip-list .e-chip.e-info.e-focused.e-active {
  background-color: #165996;
  border-color: #201f1f;
  color: #fff;
}

.e-chip-list.e-chip.e-info.e-focused.e-active .e-chip-icon,
.e-chip-list.e-chip.e-info.e-focused.e-active .e-chip-delete,
.e-chip-list .e-chip.e-info.e-focused.e-active .e-chip-icon,
.e-chip-list .e-chip.e-info.e-focused.e-active .e-chip-delete {
  color: #fff;
}

.e-chip-list.e-chip.e-info.e-focused.e-active .e-chip-avatar,
.e-chip-list .e-chip.e-info.e-focused.e-active .e-chip-avatar {
  background-color: #1e79cb;
  color: #fff;
}

.e-chip-list.e-chip.e-info:active,
.e-chip-list .e-chip.e-info:active {
  background-color: #165996;
  border-color: transparent;
  color: #fff;
}

.e-chip-list.e-chip.e-info:active .e-chip-icon,
.e-chip-list.e-chip.e-info:active .e-chip-delete,
.e-chip-list .e-chip.e-info:active .e-chip-icon,
.e-chip-list .e-chip.e-info:active .e-chip-delete {
  color: #fff;
}

.e-chip-list.e-chip.e-info:active .e-chip-avatar,
.e-chip-list .e-chip.e-info:active .e-chip-avatar {
  background-color: #1e79cb;
  color: #fff;
}

.e-chip-list.e-chip.e-info.e-disabled,
.e-chip-list .e-chip.e-info.e-disabled {
  background-color: #333232;
  border-color: transparent;
  color: #9a9a9a;
}

.e-chip-list.e-chip.e-info.e-disabled .e-chip-icon,
.e-chip-list.e-chip.e-info.e-disabled .e-chip-delete,
.e-chip-list .e-chip.e-info.e-disabled .e-chip-icon,
.e-chip-list .e-chip.e-info.e-disabled .e-chip-delete {
  color: #9a9a9a;
}

.e-chip-list.e-chip.e-info.e-disabled .e-chip-avatar,
.e-chip-list .e-chip.e-info.e-disabled .e-chip-avatar {
  background-color: #525050;
  color: #9a9a9a;
}

.e-chip-list.e-chip.e-info.e-outline,
.e-chip-list .e-chip.e-info.e-outline {
  background-color: transparent;
  border-color: #62cfff;
  color: #62cfff;
}

.e-chip-list.e-chip.e-info.e-outline .e-chip-icon,
.e-chip-list.e-chip.e-info.e-outline .e-chip-delete,
.e-chip-list .e-chip.e-info.e-outline .e-chip-icon,
.e-chip-list .e-chip.e-info.e-outline .e-chip-delete {
  color: #62cfff;
}

.e-chip-list.e-chip.e-info.e-outline .e-chip-delete.e-dlt-btn,
.e-chip-list .e-chip.e-info.e-outline .e-chip-delete.e-dlt-btn {
  color: rgba(98, 207, 255, 0.8);
}

.e-chip-list.e-chip.e-info.e-outline .e-chip-avatar,
.e-chip-list .e-chip.e-info.e-outline .e-chip-avatar {
  background-color: #1e79cb;
  color: #fff;
}

.e-chip-list.e-chip.e-info.e-outline:not(.e-active) .e-chip-delete.e-dlt-btn:hover,
.e-chip-list .e-chip.e-info.e-outline:not(.e-active) .e-chip-delete.e-dlt-btn:hover {
  color: #fff;
}

.e-chip-list.e-chip.e-info.e-outline:not(.e-active) .e-chip-delete.e-dlt-btn:active,
.e-chip-list .e-chip.e-info.e-outline:not(.e-active) .e-chip-delete.e-dlt-btn:active {
  color: #fff;
}

.e-chip-list.e-chip.e-info.e-outline:hover,
.e-chip-list .e-chip.e-info.e-outline:hover {
  background-color: #1a69b0;
  border-color: #1e79cb;
  color: #fff;
}

.e-chip-list.e-chip.e-info.e-outline:hover .e-chip-icon,
.e-chip-list.e-chip.e-info.e-outline:hover .e-chip-delete,
.e-chip-list .e-chip.e-info.e-outline:hover .e-chip-icon,
.e-chip-list .e-chip.e-info.e-outline:hover .e-chip-delete {
  color: #fff;
}

.e-chip-list.e-chip.e-info.e-outline:hover .e-chip-avatar,
.e-chip-list .e-chip.e-info.e-outline:hover .e-chip-avatar {
  background-color: #1e79cb;
  color: #fff;
}

.e-chip-list.e-chip.e-info.e-outline.e-focused,
.e-chip-list .e-chip.e-info.e-outline.e-focused {
  background-color: #1e79cb;
  border-color: #201f1f;
  color: #fff;
}

.e-chip-list.e-chip.e-info.e-outline.e-focused .e-chip-icon,
.e-chip-list.e-chip.e-info.e-outline.e-focused .e-chip-delete,
.e-chip-list .e-chip.e-info.e-outline.e-focused .e-chip-icon,
.e-chip-list .e-chip.e-info.e-outline.e-focused .e-chip-delete {
  color: #fff;
}

.e-chip-list.e-chip.e-info.e-outline.e-focused .e-chip-avatar,
.e-chip-list .e-chip.e-info.e-outline.e-focused .e-chip-avatar {
  background-color: #4397e3;
  color: #fff;
}

.e-chip-list.e-chip.e-info.e-outline.e-active,
.e-chip-list .e-chip.e-info.e-outline.e-active {
  background-color: #1e79cb;
  border-color: transparent;
  color: #fff;
}

.e-chip-list.e-chip.e-info.e-outline.e-active .e-chip-icon,
.e-chip-list.e-chip.e-info.e-outline.e-active .e-chip-delete,
.e-chip-list .e-chip.e-info.e-outline.e-active .e-chip-icon,
.e-chip-list .e-chip.e-info.e-outline.e-active .e-chip-delete {
  color: #fff;
}

.e-chip-list.e-chip.e-info.e-outline.e-active .e-chip-avatar,
.e-chip-list .e-chip.e-info.e-outline.e-active .e-chip-avatar {
  background-color: #4397e3;
  color: #fff;
}

.e-chip-list.e-chip.e-info.e-outline.e-focused.e-active,
.e-chip-list .e-chip.e-info.e-outline.e-focused.e-active {
  background-color: #1e79cb;
  border-color: #201f1f;
  color: #fff;
}

.e-chip-list.e-chip.e-info.e-outline.e-focused.e-active .e-chip-icon,
.e-chip-list.e-chip.e-info.e-outline.e-focused.e-active .e-chip-delete,
.e-chip-list .e-chip.e-info.e-outline.e-focused.e-active .e-chip-icon,
.e-chip-list .e-chip.e-info.e-outline.e-focused.e-active .e-chip-delete {
  color: #fff;
}

.e-chip-list.e-chip.e-info.e-outline.e-focused.e-active .e-chip-avatar,
.e-chip-list .e-chip.e-info.e-outline.e-focused.e-active .e-chip-avatar {
  background-color: #4397e3;
  color: #fff;
}

.e-chip-list.e-chip.e-info.e-outline:active,
.e-chip-list .e-chip.e-info.e-outline:active {
  background-color: #165996;
  border-color: transparent;
  color: #fff;
}

.e-chip-list.e-chip.e-info.e-outline:active .e-chip-icon,
.e-chip-list.e-chip.e-info.e-outline:active .e-chip-delete,
.e-chip-list .e-chip.e-info.e-outline:active .e-chip-icon,
.e-chip-list .e-chip.e-info.e-outline:active .e-chip-delete {
  color: #fff;
}

.e-chip-list.e-chip.e-info.e-outline:active .e-chip-avatar,
.e-chip-list .e-chip.e-info.e-outline:active .e-chip-avatar {
  background-color: #1e79cb;
  color: #fff;
}

.e-chip-list.e-chip.e-info.e-outline.e-disabled,
.e-chip-list .e-chip.e-info.e-outline.e-disabled {
  background-color: transparent;
  border-color: #9a9a9a;
  color: #9a9a9a;
}

.e-chip-list.e-chip.e-info.e-outline.e-disabled .e-chip-icon,
.e-chip-list.e-chip.e-info.e-outline.e-disabled .e-chip-delete,
.e-chip-list .e-chip.e-info.e-outline.e-disabled .e-chip-icon,
.e-chip-list .e-chip.e-info.e-outline.e-disabled .e-chip-delete {
  color: #9a9a9a;
}

.e-chip-list.e-chip.e-info.e-outline.e-disabled .e-chip-avatar,
.e-chip-list .e-chip.e-info.e-outline.e-disabled .e-chip-avatar {
  background-color: rgba(31, 31, 31, 0);
  color: #9a9a9a;
}

.e-chip-list.e-chip.e-warning,
.e-chip-list .e-chip.e-warning {
  background-color: #bf7500;
  border-color: transparent;
  color: #fff;
}

.e-chip-list.e-chip.e-warning .e-chip-icon,
.e-chip-list.e-chip.e-warning .e-chip-delete,
.e-chip-list .e-chip.e-warning .e-chip-icon,
.e-chip-list .e-chip.e-warning .e-chip-delete {
  color: #fff;
}

.e-chip-list.e-chip.e-warning .e-chip-delete.e-dlt-btn,
.e-chip-list .e-chip.e-warning .e-chip-delete.e-dlt-btn {
  color: rgba(255, 255, 255, 0.8);
}

.e-chip-list.e-chip.e-warning .e-chip-avatar,
.e-chip-list .e-chip.e-warning .e-chip-avatar {
  background-color: #fc9a00;
  color: #fff;
}

.e-chip-list.e-chip.e-warning:not(.e-active) .e-chip-delete.e-dlt-btn:hover,
.e-chip-list .e-chip.e-warning:not(.e-active) .e-chip-delete.e-dlt-btn:hover {
  color: #fff;
}

.e-chip-list.e-chip.e-warning:not(.e-active) .e-chip-delete.e-dlt-btn:active,
.e-chip-list .e-chip.e-warning:not(.e-active) .e-chip-delete.e-dlt-btn:active {
  color: #fff;
}

.e-chip-list.e-chip.e-warning:hover,
.e-chip-list .e-chip.e-warning:hover {
  background-color: #a06200;
  border-color: transparent;
  color: #fff;
}

.e-chip-list.e-chip.e-warning:hover .e-chip-icon,
.e-chip-list.e-chip.e-warning:hover .e-chip-delete,
.e-chip-list .e-chip.e-warning:hover .e-chip-icon,
.e-chip-list .e-chip.e-warning:hover .e-chip-delete {
  color: #fff;
}

.e-chip-list.e-chip.e-warning:hover .e-chip-avatar,
.e-chip-list .e-chip.e-warning:hover .e-chip-avatar {
  background-color: #de8800;
  color: #fff;
}

.e-chip-list.e-chip.e-warning.e-focused,
.e-chip-list .e-chip.e-warning.e-focused {
  background-color: #bf7500;
  border-color: #201f1f;
  color: #fff;
}

.e-chip-list.e-chip.e-warning.e-focused .e-chip-icon,
.e-chip-list.e-chip.e-warning.e-focused .e-chip-delete,
.e-chip-list .e-chip.e-warning.e-focused .e-chip-icon,
.e-chip-list .e-chip.e-warning.e-focused .e-chip-delete {
  color: #fff;
}

.e-chip-list.e-chip.e-warning.e-focused .e-chip-avatar,
.e-chip-list .e-chip.e-warning.e-focused .e-chip-avatar {
  background-color: #fc9a00;
  color: #fff;
}

.e-chip-list.e-chip.e-warning.e-active,
.e-chip-list .e-chip.e-warning.e-active {
  background-color: #825000;
  border-color: transparent;
  color: #fff;
}

.e-chip-list.e-chip.e-warning.e-active .e-chip-icon,
.e-chip-list.e-chip.e-warning.e-active .e-chip-delete,
.e-chip-list .e-chip.e-warning.e-active .e-chip-icon,
.e-chip-list .e-chip.e-warning.e-active .e-chip-delete {
  color: #fff;
}

.e-chip-list.e-chip.e-warning.e-active .e-chip-avatar,
.e-chip-list .e-chip.e-warning.e-active .e-chip-avatar {
  background-color: #bf7500;
  color: #fff;
}

.e-chip-list.e-chip.e-warning.e-focused.e-active,
.e-chip-list .e-chip.e-warning.e-focused.e-active {
  background-color: #825000;
  border-color: #201f1f;
  color: #fff;
}

.e-chip-list.e-chip.e-warning.e-focused.e-active .e-chip-icon,
.e-chip-list.e-chip.e-warning.e-focused.e-active .e-chip-delete,
.e-chip-list .e-chip.e-warning.e-focused.e-active .e-chip-icon,
.e-chip-list .e-chip.e-warning.e-focused.e-active .e-chip-delete {
  color: #fff;
}

.e-chip-list.e-chip.e-warning.e-focused.e-active .e-chip-avatar,
.e-chip-list .e-chip.e-warning.e-focused.e-active .e-chip-avatar {
  background-color: #bf7500;
  color: #fff;
}

.e-chip-list.e-chip.e-warning:active,
.e-chip-list .e-chip.e-warning:active {
  background-color: #825000;
  border-color: transparent;
  color: #fff;
}

.e-chip-list.e-chip.e-warning:active .e-chip-icon,
.e-chip-list.e-chip.e-warning:active .e-chip-delete,
.e-chip-list .e-chip.e-warning:active .e-chip-icon,
.e-chip-list .e-chip.e-warning:active .e-chip-delete {
  color: #fff;
}

.e-chip-list.e-chip.e-warning:active .e-chip-avatar,
.e-chip-list .e-chip.e-warning:active .e-chip-avatar {
  background-color: #bf7500;
  color: #fff;
}

.e-chip-list.e-chip.e-warning.e-disabled,
.e-chip-list .e-chip.e-warning.e-disabled {
  background-color: #333232;
  border-color: transparent;
  color: #9a9a9a;
}

.e-chip-list.e-chip.e-warning.e-disabled .e-chip-icon,
.e-chip-list.e-chip.e-warning.e-disabled .e-chip-delete,
.e-chip-list .e-chip.e-warning.e-disabled .e-chip-icon,
.e-chip-list .e-chip.e-warning.e-disabled .e-chip-delete {
  color: #9a9a9a;
}

.e-chip-list.e-chip.e-warning.e-disabled .e-chip-avatar,
.e-chip-list .e-chip.e-warning.e-disabled .e-chip-avatar {
  background-color: #525050;
  color: #9a9a9a;
}

.e-chip-list.e-chip.e-warning.e-outline,
.e-chip-list .e-chip.e-warning.e-outline {
  background-color: transparent;
  border-color: #ff9d48;
  color: #ff9d48;
}

.e-chip-list.e-chip.e-warning.e-outline .e-chip-icon,
.e-chip-list.e-chip.e-warning.e-outline .e-chip-delete,
.e-chip-list .e-chip.e-warning.e-outline .e-chip-icon,
.e-chip-list .e-chip.e-warning.e-outline .e-chip-delete {
  color: #ff9d48;
}

.e-chip-list.e-chip.e-warning.e-outline .e-chip-delete.e-dlt-btn,
.e-chip-list .e-chip.e-warning.e-outline .e-chip-delete.e-dlt-btn {
  color: rgba(255, 157, 72, 0.8);
}

.e-chip-list.e-chip.e-warning.e-outline .e-chip-avatar,
.e-chip-list .e-chip.e-warning.e-outline .e-chip-avatar {
  background-color: #bf7500;
  color: #fff;
}

.e-chip-list.e-chip.e-warning.e-outline:not(.e-active) .e-chip-delete.e-dlt-btn:hover,
.e-chip-list .e-chip.e-warning.e-outline:not(.e-active) .e-chip-delete.e-dlt-btn:hover {
  color: #fff;
}

.e-chip-list.e-chip.e-warning.e-outline:not(.e-active) .e-chip-delete.e-dlt-btn:active,
.e-chip-list .e-chip.e-warning.e-outline:not(.e-active) .e-chip-delete.e-dlt-btn:active {
  color: #fff;
}

.e-chip-list.e-chip.e-warning.e-outline:hover,
.e-chip-list .e-chip.e-warning.e-outline:hover {
  background-color: #a06200;
  border-color: #bf7500;
  color: #fff;
}

.e-chip-list.e-chip.e-warning.e-outline:hover .e-chip-icon,
.e-chip-list.e-chip.e-warning.e-outline:hover .e-chip-delete,
.e-chip-list .e-chip.e-warning.e-outline:hover .e-chip-icon,
.e-chip-list .e-chip.e-warning.e-outline:hover .e-chip-delete {
  color: #fff;
}

.e-chip-list.e-chip.e-warning.e-outline:hover .e-chip-avatar,
.e-chip-list .e-chip.e-warning.e-outline:hover .e-chip-avatar {
  background-color: #bf7500;
  color: #fff;
}

.e-chip-list.e-chip.e-warning.e-outline.e-focused,
.e-chip-list .e-chip.e-warning.e-outline.e-focused {
  background-color: #bf7500;
  border-color: #201f1f;
  color: #fff;
}

.e-chip-list.e-chip.e-warning.e-outline.e-focused .e-chip-icon,
.e-chip-list.e-chip.e-warning.e-outline.e-focused .e-chip-delete,
.e-chip-list .e-chip.e-warning.e-outline.e-focused .e-chip-icon,
.e-chip-list .e-chip.e-warning.e-outline.e-focused .e-chip-delete {
  color: #fff;
}

.e-chip-list.e-chip.e-warning.e-outline.e-focused .e-chip-avatar,
.e-chip-list .e-chip.e-warning.e-outline.e-focused .e-chip-avatar {
  background-color: #fc9a00;
  color: #fff;
}

.e-chip-list.e-chip.e-warning.e-outline.e-active,
.e-chip-list .e-chip.e-warning.e-outline.e-active {
  background-color: #bf7500;
  border-color: transparent;
  color: #fff;
}

.e-chip-list.e-chip.e-warning.e-outline.e-active .e-chip-icon,
.e-chip-list.e-chip.e-warning.e-outline.e-active .e-chip-delete,
.e-chip-list .e-chip.e-warning.e-outline.e-active .e-chip-icon,
.e-chip-list .e-chip.e-warning.e-outline.e-active .e-chip-delete {
  color: #fff;
}

.e-chip-list.e-chip.e-warning.e-outline.e-active .e-chip-avatar,
.e-chip-list .e-chip.e-warning.e-outline.e-active .e-chip-avatar {
  background-color: #fc9a00;
  color: #fff;
}

.e-chip-list.e-chip.e-warning.e-outline.e-focused.e-active,
.e-chip-list .e-chip.e-warning.e-outline.e-focused.e-active {
  background-color: #bf7500;
  border-color: #201f1f;
  color: #fff;
}

.e-chip-list.e-chip.e-warning.e-outline.e-focused.e-active .e-chip-icon,
.e-chip-list.e-chip.e-warning.e-outline.e-focused.e-active .e-chip-delete,
.e-chip-list .e-chip.e-warning.e-outline.e-focused.e-active .e-chip-icon,
.e-chip-list .e-chip.e-warning.e-outline.e-focused.e-active .e-chip-delete {
  color: #fff;
}

.e-chip-list.e-chip.e-warning.e-outline.e-focused.e-active .e-chip-avatar,
.e-chip-list .e-chip.e-warning.e-outline.e-focused.e-active .e-chip-avatar {
  background-color: #fc9a00;
  color: #fff;
}

.e-chip-list.e-chip.e-warning.e-outline:active,
.e-chip-list .e-chip.e-warning.e-outline:active {
  background-color: #825000;
  border-color: transparent;
  color: #fff;
}

.e-chip-list.e-chip.e-warning.e-outline:active .e-chip-icon,
.e-chip-list.e-chip.e-warning.e-outline:active .e-chip-delete,
.e-chip-list .e-chip.e-warning.e-outline:active .e-chip-icon,
.e-chip-list .e-chip.e-warning.e-outline:active .e-chip-delete {
  color: #fff;
}

.e-chip-list.e-chip.e-warning.e-outline:active .e-chip-avatar,
.e-chip-list .e-chip.e-warning.e-outline:active .e-chip-avatar {
  background-color: #bf7500;
  color: #fff;
}

.e-chip-list.e-chip.e-warning.e-outline.e-disabled,
.e-chip-list .e-chip.e-warning.e-outline.e-disabled {
  background-color: transparent;
  border-color: #9a9a9a;
  color: #9a9a9a;
}

.e-chip-list.e-chip.e-warning.e-outline.e-disabled .e-chip-icon,
.e-chip-list.e-chip.e-warning.e-outline.e-disabled .e-chip-delete,
.e-chip-list .e-chip.e-warning.e-outline.e-disabled .e-chip-icon,
.e-chip-list .e-chip.e-warning.e-outline.e-disabled .e-chip-delete {
  color: #9a9a9a;
}

.e-chip-list.e-chip.e-warning.e-outline.e-disabled .e-chip-avatar,
.e-chip-list .e-chip.e-warning.e-outline.e-disabled .e-chip-avatar {
  background-color: rgba(31, 31, 31, 0);
  color: #9a9a9a;
}

.e-chip-list.e-chip.e-danger,
.e-chip-list .e-chip.e-danger {
  background-color: #cd2a19;
  border-color: transparent;
  color: #fff;
}

.e-chip-list.e-chip.e-danger .e-chip-icon,
.e-chip-list.e-chip.e-danger .e-chip-delete,
.e-chip-list .e-chip.e-danger .e-chip-icon,
.e-chip-list .e-chip.e-danger .e-chip-delete {
  color: #fff;
}

.e-chip-list.e-chip.e-danger .e-chip-delete.e-dlt-btn,
.e-chip-list .e-chip.e-danger .e-chip-delete.e-dlt-btn {
  color: rgba(255, 255, 255, 0.8);
}

.e-chip-list.e-chip.e-danger .e-chip-avatar,
.e-chip-list .e-chip.e-danger .e-chip-avatar {
  background-color: #e74c3c;
  color: #fff;
}

.e-chip-list.e-chip.e-danger:not(.e-active) .e-chip-delete.e-dlt-btn:hover,
.e-chip-list .e-chip.e-danger:not(.e-active) .e-chip-delete.e-dlt-btn:hover {
  color: #fff;
}

.e-chip-list.e-chip.e-danger:not(.e-active) .e-chip-delete.e-dlt-btn:active,
.e-chip-list .e-chip.e-danger:not(.e-active) .e-chip-delete.e-dlt-btn:active {
  color: #fff;
}

.e-chip-list.e-chip.e-danger:hover,
.e-chip-list .e-chip.e-danger:hover {
  background-color: #b22416;
  border-color: transparent;
  color: #fff;
}

.e-chip-list.e-chip.e-danger:hover .e-chip-icon,
.e-chip-list.e-chip.e-danger:hover .e-chip-delete,
.e-chip-list .e-chip.e-danger:hover .e-chip-icon,
.e-chip-list .e-chip.e-danger:hover .e-chip-delete {
  color: #fff;
}

.e-chip-list.e-chip.e-danger:hover .e-chip-avatar,
.e-chip-list .e-chip.e-danger:hover .e-chip-avatar {
  background-color: #e43321;
  color: #fff;
}

.e-chip-list.e-chip.e-danger.e-focused,
.e-chip-list .e-chip.e-danger.e-focused {
  background-color: #cd2a19;
  border-color: #201f1f;
  color: #fff;
}

.e-chip-list.e-chip.e-danger.e-focused .e-chip-icon,
.e-chip-list.e-chip.e-danger.e-focused .e-chip-delete,
.e-chip-list .e-chip.e-danger.e-focused .e-chip-icon,
.e-chip-list .e-chip.e-danger.e-focused .e-chip-delete {
  color: #fff;
}

.e-chip-list.e-chip.e-danger.e-focused .e-chip-avatar,
.e-chip-list .e-chip.e-danger.e-focused .e-chip-avatar {
  background-color: #e74c3c;
  color: #fff;
}

.e-chip-list.e-chip.e-danger.e-active,
.e-chip-list .e-chip.e-danger.e-active {
  background-color: #961f12;
  border-color: transparent;
  color: #fff;
}

.e-chip-list.e-chip.e-danger.e-active .e-chip-icon,
.e-chip-list.e-chip.e-danger.e-active .e-chip-delete,
.e-chip-list .e-chip.e-danger.e-active .e-chip-icon,
.e-chip-list .e-chip.e-danger.e-active .e-chip-delete {
  color: #fff;
}

.e-chip-list.e-chip.e-danger.e-active .e-chip-avatar,
.e-chip-list .e-chip.e-danger.e-active .e-chip-avatar {
  background-color: #cd2a19;
  color: #fff;
}

.e-chip-list.e-chip.e-danger.e-focused.e-active,
.e-chip-list .e-chip.e-danger.e-focused.e-active {
  background-color: #961f12;
  border-color: #201f1f;
  color: #fff;
}

.e-chip-list.e-chip.e-danger.e-focused.e-active .e-chip-icon,
.e-chip-list.e-chip.e-danger.e-focused.e-active .e-chip-delete,
.e-chip-list .e-chip.e-danger.e-focused.e-active .e-chip-icon,
.e-chip-list .e-chip.e-danger.e-focused.e-active .e-chip-delete {
  color: #fff;
}

.e-chip-list.e-chip.e-danger.e-focused.e-active .e-chip-avatar,
.e-chip-list .e-chip.e-danger.e-focused.e-active .e-chip-avatar {
  background-color: #cd2a19;
  color: #fff;
}

.e-chip-list.e-chip.e-danger:active,
.e-chip-list .e-chip.e-danger:active {
  background-color: #961f12;
  border-color: transparent;
  color: #fff;
}

.e-chip-list.e-chip.e-danger:active .e-chip-icon,
.e-chip-list.e-chip.e-danger:active .e-chip-delete,
.e-chip-list .e-chip.e-danger:active .e-chip-icon,
.e-chip-list .e-chip.e-danger:active .e-chip-delete {
  color: #fff;
}

.e-chip-list.e-chip.e-danger:active .e-chip-avatar,
.e-chip-list .e-chip.e-danger:active .e-chip-avatar {
  background-color: #cd2a19;
  color: #fff;
}

.e-chip-list.e-chip.e-danger.e-disabled,
.e-chip-list .e-chip.e-danger.e-disabled {
  background-color: #333232;
  border-color: transparent;
  color: #9a9a9a;
}

.e-chip-list.e-chip.e-danger.e-disabled .e-chip-icon,
.e-chip-list.e-chip.e-danger.e-disabled .e-chip-delete,
.e-chip-list .e-chip.e-danger.e-disabled .e-chip-icon,
.e-chip-list .e-chip.e-danger.e-disabled .e-chip-delete {
  color: #9a9a9a;
}

.e-chip-list.e-chip.e-danger.e-disabled .e-chip-avatar,
.e-chip-list .e-chip.e-danger.e-disabled .e-chip-avatar {
  background-color: #525050;
  color: #9a9a9a;
}

.e-chip-list.e-chip.e-danger.e-outline,
.e-chip-list .e-chip.e-danger.e-outline {
  background-color: transparent;
  border-color: #ff5f5f;
  color: #ff5f5f;
}

.e-chip-list.e-chip.e-danger.e-outline .e-chip-icon,
.e-chip-list.e-chip.e-danger.e-outline .e-chip-delete,
.e-chip-list .e-chip.e-danger.e-outline .e-chip-icon,
.e-chip-list .e-chip.e-danger.e-outline .e-chip-delete {
  color: #ff5f5f;
}

.e-chip-list.e-chip.e-danger.e-outline .e-chip-delete.e-dlt-btn,
.e-chip-list .e-chip.e-danger.e-outline .e-chip-delete.e-dlt-btn {
  color: rgba(255, 95, 95, 0.8);
}

.e-chip-list.e-chip.e-danger.e-outline .e-chip-avatar,
.e-chip-list .e-chip.e-danger.e-outline .e-chip-avatar {
  background-color: #cd2a19;
  color: #fff;
}

.e-chip-list.e-chip.e-danger.e-outline:not(.e-active) .e-chip-delete.e-dlt-btn:hover,
.e-chip-list .e-chip.e-danger.e-outline:not(.e-active) .e-chip-delete.e-dlt-btn:hover {
  color: #fff;
}

.e-chip-list.e-chip.e-danger.e-outline:not(.e-active) .e-chip-delete.e-dlt-btn:active,
.e-chip-list .e-chip.e-danger.e-outline:not(.e-active) .e-chip-delete.e-dlt-btn:active {
  color: #fff;
}

.e-chip-list.e-chip.e-danger.e-outline:hover,
.e-chip-list .e-chip.e-danger.e-outline:hover {
  background-color: #b22416;
  border-color: #cd2a19;
  color: #fff;
}

.e-chip-list.e-chip.e-danger.e-outline:hover .e-chip-icon,
.e-chip-list.e-chip.e-danger.e-outline:hover .e-chip-delete,
.e-chip-list .e-chip.e-danger.e-outline:hover .e-chip-icon,
.e-chip-list .e-chip.e-danger.e-outline:hover .e-chip-delete {
  color: #fff;
}

.e-chip-list.e-chip.e-danger.e-outline:hover .e-chip-avatar,
.e-chip-list .e-chip.e-danger.e-outline:hover .e-chip-avatar {
  background-color: #cd2a19;
  color: #fff;
}

.e-chip-list.e-chip.e-danger.e-outline.e-focused,
.e-chip-list .e-chip.e-danger.e-outline.e-focused {
  background-color: #cd2a19;
  border-color: #201f1f;
  color: #fff;
}

.e-chip-list.e-chip.e-danger.e-outline.e-focused .e-chip-icon,
.e-chip-list.e-chip.e-danger.e-outline.e-focused .e-chip-delete,
.e-chip-list .e-chip.e-danger.e-outline.e-focused .e-chip-icon,
.e-chip-list .e-chip.e-danger.e-outline.e-focused .e-chip-delete {
  color: #fff;
}

.e-chip-list.e-chip.e-danger.e-outline.e-focused .e-chip-avatar,
.e-chip-list .e-chip.e-danger.e-outline.e-focused .e-chip-avatar {
  background-color: #e74c3c;
  color: #fff;
}

.e-chip-list.e-chip.e-danger.e-outline.e-active,
.e-chip-list .e-chip.e-danger.e-outline.e-active {
  background-color: #cd2a19;
  border-color: transparent;
  color: #fff;
}

.e-chip-list.e-chip.e-danger.e-outline.e-active .e-chip-icon,
.e-chip-list.e-chip.e-danger.e-outline.e-active .e-chip-delete,
.e-chip-list .e-chip.e-danger.e-outline.e-active .e-chip-icon,
.e-chip-list .e-chip.e-danger.e-outline.e-active .e-chip-delete {
  color: #fff;
}

.e-chip-list.e-chip.e-danger.e-outline.e-active .e-chip-avatar,
.e-chip-list .e-chip.e-danger.e-outline.e-active .e-chip-avatar {
  background-color: #e74c3c;
  color: #fff;
}

.e-chip-list.e-chip.e-danger.e-outline.e-focused.e-active,
.e-chip-list .e-chip.e-danger.e-outline.e-focused.e-active {
  background-color: #cd2a19;
  border-color: #201f1f;
  color: #fff;
}

.e-chip-list.e-chip.e-danger.e-outline.e-focused.e-active .e-chip-icon,
.e-chip-list.e-chip.e-danger.e-outline.e-focused.e-active .e-chip-delete,
.e-chip-list .e-chip.e-danger.e-outline.e-focused.e-active .e-chip-icon,
.e-chip-list .e-chip.e-danger.e-outline.e-focused.e-active .e-chip-delete {
  color: #fff;
}

.e-chip-list.e-chip.e-danger.e-outline.e-focused.e-active .e-chip-avatar,
.e-chip-list .e-chip.e-danger.e-outline.e-focused.e-active .e-chip-avatar {
  background-color: #e74c3c;
  color: #fff;
}

.e-chip-list.e-chip.e-danger.e-outline:active,
.e-chip-list .e-chip.e-danger.e-outline:active {
  background-color: #961f12;
  border-color: transparent;
  color: #fff;
}

.e-chip-list.e-chip.e-danger.e-outline:active .e-chip-icon,
.e-chip-list.e-chip.e-danger.e-outline:active .e-chip-delete,
.e-chip-list .e-chip.e-danger.e-outline:active .e-chip-icon,
.e-chip-list .e-chip.e-danger.e-outline:active .e-chip-delete {
  color: #fff;
}

.e-chip-list.e-chip.e-danger.e-outline:active .e-chip-avatar,
.e-chip-list .e-chip.e-danger.e-outline:active .e-chip-avatar {
  background-color: #cd2a19;
  color: #fff;
}

.e-chip-list.e-chip.e-danger.e-outline.e-disabled,
.e-chip-list .e-chip.e-danger.e-outline.e-disabled {
  background-color: transparent;
  border-color: #9a9a9a;
  color: #9a9a9a;
}

.e-chip-list.e-chip.e-danger.e-outline.e-disabled .e-chip-icon,
.e-chip-list.e-chip.e-danger.e-outline.e-disabled .e-chip-delete,
.e-chip-list .e-chip.e-danger.e-outline.e-disabled .e-chip-icon,
.e-chip-list .e-chip.e-danger.e-outline.e-disabled .e-chip-delete {
  color: #9a9a9a;
}

.e-chip-list.e-chip.e-danger.e-outline.e-disabled .e-chip-avatar,
.e-chip-list .e-chip.e-danger.e-outline.e-disabled .e-chip-avatar {
  background-color: rgba(31, 31, 31, 0);
  color: #9a9a9a;
}
