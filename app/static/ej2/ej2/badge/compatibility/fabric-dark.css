.e-control.e-badge {
  background: #fafafa;
  border-color: transparent;
  border-radius: 0.25em;
  box-shadow: 0 0 0 2px transparent;
  box-sizing: border-box;
  color: rgba(0, 0, 0, 0.87);
  display: inline-block;
  font-family: "Segoe UI", "GeezaPro", "DejaVu Serif", sans-serif, "-apple-system", "BlinkMacSystemFont";
  font-size: 0.75em;
  font-weight: 500;
  line-height: 1;
  overflow: hidden;
  padding: 0.25em 0.4em 0.3em 0.4em;
  text-align: center;
  text-decoration: none;
  text-indent: 0;
  vertical-align: middle;
}

.e-control.e-badge:hover {
  text-decoration: none;
}

.e-control.e-badge.e-badge-pill {
  border-radius: 5em;
}

.e-control.e-badge.e-badge-notification {
  border-radius: 1em;
  font-size: 12px;
  height: 18px;
  left: 100%;
  line-height: 1.1;
  min-width: 24px;
  padding: 0.25em 0.4em 0.3em 0.4em;
  position: absolute;
  top: -10px;
  width: auto;
}

.e-control.e-badge.e-badge-notification.e-badge-ghost {
  line-height: 0.9;
}

.e-control.e-badge.e-badge-circle {
  border-radius: 50%;
  height: 1.834em;
  line-height: 1.8em;
  min-width: 0;
  padding: 0;
  width: 1.834em;
}

.e-control.e-badge.e-badge-circle.e-badge-ghost {
  line-height: 1.8em;
}

.e-control.e-badge.e-badge-overlap {
  position: absolute;
  top: -10px;
  transform: translateX(-50%);
}

.e-control.e-badge.e-badge-dot {
  border-radius: 100%;
  box-shadow: 0 0 0 1px #fff;
  height: 6px;
  left: 100%;
  line-height: 1;
  margin: 0;
  min-width: 0;
  overflow: visible;
  padding: 0;
  position: absolute;
  top: -3px;
  width: 6px;
}

.e-control.e-badge.e-badge-bottom.e-badge-dot {
  bottom: 3px;
  position: absolute;
  top: auto;
}

.e-control.e-badge.e-badge-bottom.e-badge-notification {
  bottom: -3px;
  position: absolute;
  top: auto;
}

button .e-badge {
  line-height: .9;
  position: relative;
  top: -2px;
}

button .e-badge.e-badge-circle {
  height: 2em;
  line-height: 2em;
  width: 2em;
}

button .e-badge.e-badge-circle.e-badge-ghost {
  line-height: 1.9em;
}

.e-control.e-badge.e-badge-primary:not(.e-badge-ghost):not([href]), .e-control.e-badge.e-badge-primary[href]:not(.e-badge-ghost) {
  background-color: #0074cc;
  color: #fff;
}

.e-control.e-badge.e-badge-secondary:not(.e-badge-ghost):not([href]), .e-control.e-badge.e-badge-secondary[href]:not(.e-badge-ghost) {
  background-color: #414040;
  color: #dadada;
}

.e-control.e-badge.e-badge-success:not(.e-badge-ghost):not([href]), .e-control.e-badge.e-badge-success[href]:not(.e-badge-ghost) {
  background-color: #37844d;
  color: #000;
}

.e-control.e-badge.e-badge-danger:not(.e-badge-ghost):not([href]), .e-control.e-badge.e-badge-danger[href]:not(.e-badge-ghost) {
  background-color: #cd2a19;
  color: #000;
}

.e-control.e-badge.e-badge-warning:not(.e-badge-ghost):not([href]), .e-control.e-badge.e-badge-warning[href]:not(.e-badge-ghost) {
  background-color: #bf7500;
  color: #000;
}

.e-control.e-badge.e-badge-info:not(.e-badge-ghost):not([href]), .e-control.e-badge.e-badge-info[href]:not(.e-badge-ghost) {
  background-color: #1e79cb;
  color: #000;
}

.e-control.e-badge.e-badge-light:not(.e-badge-ghost):not([href]), .e-control.e-badge.e-badge-light[href]:not(.e-badge-ghost) {
  background-color: #201f1f;
  color: #dadada;
}

.e-control.e-badge.e-badge-dark:not(.e-badge-ghost):not([href]), .e-control.e-badge.e-badge-dark[href]:not(.e-badge-ghost) {
  background-color: #f4f4f4;
  color: #000;
}

.e-control.e-badge.e-badge-primary[href]:not(.e-badge-ghost):hover {
  background-color: #005799;
}

.e-control.e-badge.e-badge-secondary[href]:not(.e-badge-ghost):hover {
  background-color: #272727;
}

.e-control.e-badge.e-badge-success[href]:not(.e-badge-ghost):hover {
  background-color: #286038;
}

.e-control.e-badge.e-badge-danger[href]:not(.e-badge-ghost):hover {
  background-color: #a02113;
}

.e-control.e-badge.e-badge-warning[href]:not(.e-badge-ghost):hover {
  background-color: #8c5600;
}

.e-control.e-badge.e-badge-info[href]:not(.e-badge-ghost):hover {
  background-color: #175f9f;
}

.e-control.e-badge.e-badge-light[href]:not(.e-badge-ghost):hover {
  background-color: #060606;
}

.e-control.e-badge.e-badge-dark[href]:not(.e-badge-ghost):hover {
  background-color: #dbdbdb;
}

.e-control.e-badge.e-badge-primary[href].e-badge-ghost:hover {
  border-color: #004980;
  color: #004980;
}

.e-control.e-badge.e-badge-secondary[href].e-badge-ghost:hover {
  border-color: #1a1a1a;
  color: #1a1a1a;
}

.e-control.e-badge.e-badge-success[href].e-badge-ghost:hover {
  border-color: #214e2e;
  color: #214e2e;
}

.e-control.e-badge.e-badge-danger[href].e-badge-ghost:hover {
  border-color: #891c11;
  color: #891c11;
}

.e-control.e-badge.e-badge-warning[href].e-badge-ghost:hover {
  border-color: #734600;
  color: #734600;
}

.e-control.e-badge.e-badge-info[href].e-badge-ghost:hover {
  border-color: #145188;
  color: #145188;
}

.e-control.e-badge.e-badge-light[href].e-badge-ghost:hover {
  border-color: black;
  color: black;
}

.e-control.e-badge.e-badge-dark[href].e-badge-ghost:hover {
  border-color: #cecece;
  color: #cecece;
}

.e-control.e-badge.e-badge-ghost.e-badge-primary {
  background-color: transparent;
  border: 1px solid #0074cc;
  color: #0074cc;
}

.e-control.e-badge.e-badge-ghost.e-badge-secondary {
  background-color: transparent;
  border: 1px solid #414040;
  color: #414040;
}

.e-control.e-badge.e-badge-ghost.e-badge-success {
  background-color: transparent;
  border: 1px solid #37844d;
  color: #37844d;
}

.e-control.e-badge.e-badge-ghost.e-badge-danger {
  background-color: transparent;
  border: 1px solid #cd2a19;
  color: #cd2a19;
}

.e-control.e-badge.e-badge-ghost.e-badge-warning {
  background-color: transparent;
  border: 1px solid #bf7500;
  color: #bf7500;
}

.e-control.e-badge.e-badge-ghost.e-badge-info {
  background-color: transparent;
  border: 1px solid #1e79cb;
  color: #1e79cb;
}

.e-control.e-badge.e-badge-ghost.e-badge-light {
  background-color: transparent;
  border: 1px solid #201f1f;
  color: #201f1f;
}

.e-control.e-badge.e-badge-ghost.e-badge-dark {
  background-color: transparent;
  border: 1px solid #f4f4f4;
  color: #f4f4f4;
}

.e-control .e-js [class^='e-'], .e-control .e-js [class*=' e-'] {
  box-sizing: content-box;
}
