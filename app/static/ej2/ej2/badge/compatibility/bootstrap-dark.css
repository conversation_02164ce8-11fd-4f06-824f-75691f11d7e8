.e-control.e-badge {
  background: #fafafa;
  border-color: transparent;
  border-radius: 0.25em;
  box-shadow: 0 0 0 2px transparent;
  box-sizing: border-box;
  color: rgba(0, 0, 0, 0.87);
  display: inline-block;
  font-family: "Helvetica Neue", "Helvetica", "Arial", sans-serif, "-apple-system", "BlinkMacSystemFont";
  font-size: 0.75em;
  font-weight: 500;
  line-height: 1;
  overflow: hidden;
  padding: 0.25em 0.4em 0.25em 0.4em;
  text-align: center;
  text-decoration: none;
  text-indent: 0;
  vertical-align: middle;
}

.e-control.e-badge:hover {
  text-decoration: none;
}

.e-control.e-badge.e-badge-pill {
  border-radius: 5em;
}

.e-control.e-badge.e-badge-notification {
  border-radius: 1em;
  font-size: 12px;
  height: 18px;
  left: 100%;
  line-height: 18px;
  min-width: 24px;
  padding: 0 8px 0 8px;
  position: absolute;
  top: -10px;
  width: auto;
}

.e-control.e-badge.e-badge-notification.e-badge-ghost {
  line-height: 16px;
}

.e-control.e-badge.e-badge-circle {
  border-radius: 50%;
  height: 1.834em;
  line-height: 1.834em;
  min-width: 0;
  padding: 0;
  width: 1.834em;
}

.e-control.e-badge.e-badge-circle.e-badge-ghost {
  line-height: 1.8em;
}

.e-control.e-badge.e-badge-overlap {
  position: absolute;
  top: -10px;
  transform: translateX(-50%);
}

.e-control.e-badge.e-badge-dot {
  border-radius: 100%;
  box-shadow: 0 0 0 1px #fff;
  height: 6px;
  left: 100%;
  line-height: 1;
  margin: 0;
  min-width: 0;
  overflow: visible;
  padding: 0;
  position: absolute;
  top: -3px;
  width: 6px;
}

.e-control.e-badge.e-badge-bottom.e-badge-dot {
  bottom: 3px;
  position: absolute;
  top: auto;
}

.e-control.e-badge.e-badge-bottom.e-badge-notification {
  bottom: -3px;
  position: absolute;
  top: auto;
}

button .e-badge {
  line-height: .9;
  position: relative;
  top: -2px;
}

button .e-badge.e-badge-circle {
  height: 2em;
  line-height: 2em;
  width: 2em;
}

button .e-badge.e-badge-circle.e-badge-ghost {
  line-height: 1.9em;
}

.e-control.e-badge.e-badge-primary:not(.e-badge-ghost):not([href]), .e-control.e-badge.e-badge-primary[href]:not(.e-badge-ghost) {
  background-color: #0070f0;
  color: #fff;
}

.e-control.e-badge.e-badge-secondary:not(.e-badge-ghost):not([href]), .e-control.e-badge.e-badge-secondary[href]:not(.e-badge-ghost) {
  background-color: #f0f0f0;
  color: #2a2a2a;
}

.e-control.e-badge.e-badge-success:not(.e-badge-ghost):not([href]), .e-control.e-badge.e-badge-success[href]:not(.e-badge-ghost) {
  background-color: #358238;
  color: #131313;
}

.e-control.e-badge.e-badge-danger:not(.e-badge-ghost):not([href]), .e-control.e-badge.e-badge-danger[href]:not(.e-badge-ghost) {
  background-color: #c12f2f;
  color: #131313;
}

.e-control.e-badge.e-badge-warning:not(.e-badge-ghost):not([href]), .e-control.e-badge.e-badge-warning[href]:not(.e-badge-ghost) {
  background-color: #f9ad37;
  color: #f0f0f0;
}

.e-control.e-badge.e-badge-info:not(.e-badge-ghost):not([href]), .e-control.e-badge.e-badge-info[href]:not(.e-badge-ghost) {
  background-color: #208090;
  color: #131313;
}

.e-control.e-badge.e-badge-light:not(.e-badge-ghost):not([href]), .e-control.e-badge.e-badge-light[href]:not(.e-badge-ghost) {
  background-color: #414141;
  color: #fff;
}

.e-control.e-badge.e-badge-dark:not(.e-badge-ghost):not([href]), .e-control.e-badge.e-badge-dark[href]:not(.e-badge-ghost) {
  background-color: #acacac;
  color: #131313;
}

.e-control.e-badge.e-badge-primary[href]:not(.e-badge-ghost):hover {
  background-color: #0058bd;
}

.e-control.e-badge.e-badge-secondary[href]:not(.e-badge-ghost):hover {
  background-color: #d7d7d7;
}

.e-control.e-badge.e-badge-success[href]:not(.e-badge-ghost):hover {
  background-color: #265e28;
}

.e-control.e-badge.e-badge-danger[href]:not(.e-badge-ghost):hover {
  background-color: #982525;
}

.e-control.e-badge.e-badge-warning[href]:not(.e-badge-ghost):hover {
  background-color: #f69807;
}

.e-control.e-badge.e-badge-info[href]:not(.e-badge-ghost):hover {
  background-color: #175b66;
}

.e-control.e-badge.e-badge-light[href]:not(.e-badge-ghost):hover {
  background-color: #282828;
}

.e-control.e-badge.e-badge-dark[href]:not(.e-badge-ghost):hover {
  background-color: #939393;
}

.e-control.e-badge.e-badge-primary[href].e-badge-ghost:hover {
  border-color: #004ca4;
  color: #004ca4;
}

.e-control.e-badge.e-badge-secondary[href].e-badge-ghost:hover {
  border-color: #cacaca;
  color: #cacaca;
}

.e-control.e-badge.e-badge-success[href].e-badge-ghost:hover {
  border-color: #1f4c21;
  color: #1f4c21;
}

.e-control.e-badge.e-badge-danger[href].e-badge-ghost:hover {
  border-color: #832020;
  color: #832020;
}

.e-control.e-badge.e-badge-warning[href].e-badge-ghost:hover {
  border-color: #dd8907;
  color: #dd8907;
}

.e-control.e-badge.e-badge-info[href].e-badge-ghost:hover {
  border-color: #124851;
  color: #124851;
}

.e-control.e-badge.e-badge-light[href].e-badge-ghost:hover {
  border-color: #1b1b1b;
  color: #1b1b1b;
}

.e-control.e-badge.e-badge-dark[href].e-badge-ghost:hover {
  border-color: #868686;
  color: #868686;
}

.e-control.e-badge.e-badge-ghost.e-badge-primary {
  background-color: transparent;
  border: 1px solid #0070f0;
  color: #0070f0;
}

.e-control.e-badge.e-badge-ghost.e-badge-secondary {
  background-color: transparent;
  border: 1px solid #f0f0f0;
  color: #f0f0f0;
}

.e-control.e-badge.e-badge-ghost.e-badge-success {
  background-color: transparent;
  border: 1px solid #358238;
  color: #358238;
}

.e-control.e-badge.e-badge-ghost.e-badge-danger {
  background-color: transparent;
  border: 1px solid #c12f2f;
  color: #c12f2f;
}

.e-control.e-badge.e-badge-ghost.e-badge-warning {
  background-color: transparent;
  border: 1px solid #f9ad37;
  color: #f9ad37;
}

.e-control.e-badge.e-badge-ghost.e-badge-info {
  background-color: transparent;
  border: 1px solid #208090;
  color: #208090;
}

.e-control.e-badge.e-badge-ghost.e-badge-light {
  background-color: transparent;
  border: 1px solid #414141;
  color: #414141;
}

.e-control.e-badge.e-badge-ghost.e-badge-dark {
  background-color: transparent;
  border: 1px solid #acacac;
  color: #acacac;
}

.e-control .e-js [class^='e-'], .e-control .e-js [class*=' e-'] {
  box-sizing: content-box;
}
