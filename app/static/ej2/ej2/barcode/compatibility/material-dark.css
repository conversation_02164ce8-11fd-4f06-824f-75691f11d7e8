@import url("https://fonts.googleapis.com/css?family=Roboto:400,500");
.e-barcode {
  display: block;
}

.e-datamatrix {
  display: block;
}

.e-qrcode {
  display: block;
}

.e-content-placeholder.e-barcode.e-placeholder-barcode {
  background-size: 100% 100%;
  max-height: 100px;
  max-width: 100px;
}

.e-content-placeholder.e-datamatrixgenerator.e-placeholder-datamatrixgenerator {
  background-size: 100% 100%;
  max-height: 100px;
  max-width: 100px;
}

.e-content-placeholder.e-qrcodegenerator.e-placeholder-qrcodegenerator {
  background-size: 100% 100%;
  max-height: 100px;
  max-width: 100px;
}

.e-lib .e-js [class^='e-'], .e-lib .e-js [class*=' e-'] {
  box-sizing: content-box;
}
