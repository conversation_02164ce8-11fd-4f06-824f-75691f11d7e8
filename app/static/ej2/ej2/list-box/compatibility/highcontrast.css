.e-dropdownbase .e-list-item .e-list-icon {
  padding: 0 10px 0 0;
}

.e-bigger .e-dropdownbase .e-list-item .e-list-icon {
  padding: 0 12px 0 0;
}

.e-dropdownbase .e-list-item:not(.e-active).e-hover {
  border: 2px solid #fff;
}

.e-dropdownbase .e-list-item:not(.e-active):not(.e-hover):not(.e-item-focus) {
  border: 2px solid #000;
}

.e-dropdownbase .e-list-item.e-active {
  border: 2px solid #ffd939;
}

.e-dropdownbase .e-list-item.e-item-focus {
  border: 2px solid #685708;
}

.e-small .e-dropdownbase .e-list-item .e-list-icon {
  padding: 0 8px 0 0;
}

.e-bigger.e-small .e-dropdownbase .e-list-item .e-list-icon {
  padding: 0 12px 0 0;
}

.e-listbox-tool .e-moveup::before {
  content: '\e651';
}

.e-listbox-tool .e-movedown::before {
  content: '\e652';
}

.e-listbox-tool .e-moveto::before {
  content: '\e653';
}

.e-listbox-tool .e-movefrom::before {
  content: '\e654';
}

.e-listbox-tool .e-moveallto::before {
  content: '\e655';
}

.e-listbox-tool .e-moveallfrom::before {
  content: '\e656';
}

.e-control.e-listbox-wrapper {
  -webkit-overflow-scrolling: touch;
  border: 0 solid;
  cursor: pointer;
  display: block;
  overflow: auto;
  position: relative;
  -webkit-user-select: none;
     -moz-user-select: none;
      -ms-user-select: none;
          user-select: none;
  width: 100%;
}

.e-control.e-listbox-wrapper * {
  box-sizing: border-box;
}

.e-control.e-listbox-wrapper:focus {
  outline: none;
}

.e-control.e-listbox-wrapper.e-disabled {
  cursor: default;
  pointer-events: none;
}

.e-control.e-listbox-wrapper:not(.e-list-template) .e-list-item, .e-control.e-listbox-wrapper .e-selectall-parent {
  height: 36px;
  line-height: 30px;
  padding: 0 12px;
  position: relative;
}

.e-control.e-listbox-wrapper .e-list-parent {
  height: 100%;
}

.e-control.e-listbox-wrapper .e-list-item {
  border-bottom: 0 solid;
  outline: none;
}

.e-control.e-listbox-wrapper .e-list-item.e-disabled {
  pointer-events: none;
}

.e-control.e-listbox-wrapper .e-disable {
  opacity: .7;
}

.e-control.e-listbox-wrapper .e-list-parent {
  margin: 0;
  padding: 0;
}

.e-control.e-listbox-wrapper .e-list-header .e-text.header {
  display: none;
}

.e-control.e-listbox-wrapper .e-icon-back {
  margin-top: 2px;
}

.e-control.e-listbox-wrapper .e-list-header .e-headertemplate-text.nested-header {
  display: none;
}

.e-control.e-listbox-wrapper .e-list-header {
  -ms-flex-align: center;
      align-items: center;
  border-bottom: 1px solid;
  display: -ms-flexbox;
  display: flex;
  font-weight: 600;
  height: 36px;
  padding: 0 16px;
}

.e-control.e-listbox-wrapper .e-has-header > .e-view {
  top: 45px;
}

.e-control.e-listbox-wrapper .e-but-back {
  cursor: pointer;
  padding-right: 20px;
}

.e-control.e-listbox-wrapper .e-list-group-item:first-child {
  border: 0;
  border-bottom: 0 solid transparent;
}

.e-control.e-listbox-wrapper .e-list-group-item {
  border-bottom: 0 solid transparent;
  border-top: 1px solid;
  font-weight: 600;
  height: 36px;
  line-height: 30px;
  padding: 0 12px;
}

.e-control.e-listbox-wrapper .e-icon-collapsible {
  cursor: pointer;
  font-size: 12px;
  position: absolute;
  right: 0%;
  top: 50%;
  transform: translateY(-50%);
}

.e-control.e-listbox-wrapper .e-text-content {
  height: 100%;
  position: relative;
  vertical-align: middle;
}

.e-control.e-listbox-wrapper .e-text-content * {
  display: inline-block;
  vertical-align: middle;
}

.e-control.e-listbox-wrapper .e-text-content.e-checkbox .e-list-text {
  width: calc(100% - 40px);
}

.e-control.e-listbox-wrapper .e-text-content.e-checkbox.e-checkbox-left .e-list-icon + .e-list-text {
  width: calc(100% - 90px);
}

.e-control.e-listbox-wrapper .e-text-content.e-checkbox.e-checkbox-right .e-list-icon + .e-list-text {
  width: calc(100% - 80px);
}

.e-control.e-listbox-wrapper .e-list-item.e-checklist.e-has-child .e-text-content.e-checkbox.e-checkbox-right .e-list-icon + .e-list-text {
  width: calc(100% - 92px);
}

.e-control.e-listbox-wrapper .e-checkbox .e-checkbox-left {
  margin: -2px 10px 0 0;
}

.e-control.e-listbox-wrapper .e-checkbox .e-checkbox-right {
  margin: -2px 0 0 10px;
}

.e-control.e-listbox-wrapper .e-list-text {
  cursor: pointer;
  display: inline-block;
  overflow: hidden;
  text-overflow: ellipsis;
  vertical-align: top;
  white-space: nowrap;
  width: 100%;
}

.e-control.e-listbox-wrapper .e-list-icon + .e-list-text {
  width: calc(100% - 60px);
}

.e-control.e-listbox-wrapper .e-icon-wrapper .e-list-text {
  width: calc(100% - 60px);
}

.e-control.e-listbox-wrapper .e-icon-wrapper.e-text-content.e-checkbox .e-list-text {
  width: calc(100% - 60px);
}

.e-control.e-listbox-wrapper .e-list-icon {
  height: 30px;
  margin-right: 16px;
  width: 30px;
}

.e-control.e-listbox-wrapper .e-content {
  overflow: hidden;
  position: relative;
}

.e-control.e-listbox-wrapper .e-list-header .e-text {
  cursor: pointer;
  text-indent: 0;
}

.e-control.e-listbox-wrapper .e-text .e-headertext {
  display: inline-block;
  line-height: 21px;
}

.e-control.e-listbox-wrapper.e-rtl {
  direction: rtl;
}

.e-control.e-listbox-wrapper.e-rtl .e-list-icon {
  margin-left: 16px;
  margin-right: 0;
}

.e-control.e-listbox-wrapper.e-rtl .e-icon-collapsible {
  left: 0%;
  right: initial;
  top: 50%;
  transform: translateY(-50%) rotate(180deg);
}

.e-control.e-listbox-wrapper.e-rtl .e-list-header .e-text {
  cursor: pointer;
}

.e-control.e-listbox-wrapper.e-rtl .e-but-back {
  transform: rotate(180deg);
}

.e-control.e-listbox-wrapper.e-rtl .e-icon-back {
  margin-top: 1px;
}

.e-control.e-listbox-wrapper.e-rtl .e-checkbox .e-checkbox-left {
  margin: -2px 0 0 10px;
}

.e-control.e-listbox-wrapper.e-rtl .e-checkbox .e-checkbox-right {
  margin: -2px 10px 0 0;
}

.e-control.e-listbox-wrapper.e-rtl .e-checkbox-wrapper {
  margin: -2px 0 0 10px;
}

.e-control.e-listbox-wrapper .e-list-item .e-list-badge {
  -ms-flex-align: center;
      align-items: center;
  background-color: #ffd939;
  border: 1px solid #000;
  border-radius: 50%;
  color: #000;
  display: -ms-flexbox;
  display: flex;
  font-size: 12px;
  height: 22px;
  -ms-flex-pack: center;
      justify-content: center;
  position: absolute;
  right: -10px;
  top: -10px;
  width: 22px;
}

.e-control.e-listbox-wrapper .e-checkbox-wrapper {
  margin: -2px 10px 0 0;
  text-indent: 0;
  vertical-align: middle;
}

.e-control.e-listbox-wrapper.e-right .e-checkbox-wrapper {
  position: absolute;
  right: 0;
  top: 30%;
}

.e-control.e-listbox-wrapper .e-input-group {
  padding: 4px 8px;
}

.e-control.e-listbox-wrapper .e-input-focus {
  padding: 4px 4px 4px 8px;
}

.e-control.e-listbox-wrapper .e-hidden-select {
  height: 1px;
  opacity: 0;
  position: absolute;
  width: 100%;
}

.e-control.e-listbox-wrapper .e-placeholder {
  background-color: #ffd939;
  display: block;
  height: 1px;
}

.e-control.e-listbox-wrapper .e-sortableclone {
  z-index: 10000;
}

.e-control.e-listbox-wrapper .e-sortableclone.e-ripple {
  overflow: visible;
}

ejs-listbox {
  display: block;
}

.e-control.e-listboxtool-wrapper {
  cursor: pointer;
  display: -ms-flexbox;
  display: flex;
}

.e-control.e-listboxtool-wrapper * {
  box-sizing: border-box;
}

.e-control.e-listboxtool-wrapper.e-disabled {
  cursor: default;
  pointer-events: none;
}

.e-control.e-listboxtool-wrapper .e-listbox-wrapper {
  -ms-flex: 1;
      flex: 1;
}

.e-control.e-listboxtool-wrapper.e-right .e-listbox-tool {
  margin-left: 15px;
}

.e-control.e-listboxtool-wrapper.e-left .e-listbox-tool {
  margin-right: 15px;
}

.e-control.e-listboxtool-wrapper .e-listbox-tool {
  border: 1px solid #969696;
  overflow: auto;
  padding: 8px;
}

.e-control.e-listboxtool-wrapper .e-listbox-tool .e-btn {
  display: list-item;
  list-style-type: none;
  margin-bottom: 10px;
}

.e-control.e-rtl.e-listboxtool-wrapper.e-right .e-listbox-tool {
  margin-right: 15px;
}

.e-control.e-rtl.e-listboxtool-wrapper.e-left .e-listbox-tool {
  margin-left: 15px;
}

.e-bigger .e-control.e-listbox-wrapper .e-list-item, .e-control.e-listbox-wrapper.e-bigger .e-list-item {
  border-bottom: 2px solid transparent;
  border-left: 2px solid transparent;
  border-right: 2px solid transparent;
  border-top: 2px solid transparent;
}

.e-bigger .e-control.e-listbox-wrapper:not(.e-list-template) .e-list-item, .e-bigger .e-control.e-listbox-wrapper .e-selectall-parent, .e-control.e-listbox-wrapper.e-bigger:not(.e-list-template) .e-list-item, .e-control.e-listbox-wrapper.e-bigger .e-selectall-parent {
  height: 45px;
  line-height: 38px;
  position: relative;
}

.e-bigger .e-control.e-listbox-wrapper .e-text-content, .e-control.e-listbox-wrapper.e-bigger .e-text-content {
  font-size: 15px;
}

.e-bigger .e-control.e-listbox-wrapper .e-list-group-item, .e-control.e-listbox-wrapper.e-bigger .e-list-group-item {
  height: 45px;
  line-height: 45px;
}

.e-bigger .e-control.e-listbox-wrapper .e-list-header, .e-control.e-listbox-wrapper.e-bigger .e-list-header {
  -ms-flex-align: center;
      align-items: center;
  display: -ms-flexbox;
  display: flex;
  font-weight: 600;
  height: 45px;
}

.e-bigger .e-control.e-listbox-wrapper .e-list-header .e-text.header, .e-control.e-listbox-wrapper.e-bigger .e-list-header .e-text.header {
  display: none;
}

.e-bigger .e-control.e-listbox-wrapper .e-list-header .e-headertemplate-text.nested-header, .e-control.e-listbox-wrapper.e-bigger .e-list-header .e-headertemplate-text.nested-header {
  display: none;
}

.e-bigger .e-control.e-listbox-wrapper .e-list-header .e-text, .e-control.e-listbox-wrapper.e-bigger .e-list-header .e-text {
  font-size: 15px;
}

.e-control.e-listbox-wrapper {
  border: 1px solid #969696;
  font-family: "Segoe UI", "GeezaPro", "DejaVu Serif", sans-serif, "-apple-system", "BlinkMacSystemFont";
  font-size: 13px;
}

.e-control.e-listbox-wrapper .e-list-header {
  background-color: #000;
  border-color: #969696;
  color: #ffd939;
  font-size: 14px;
}

.e-control.e-listbox-wrapper .e-icons {
  color: #fff;
}

.e-control.e-listbox-wrapper .e-list-item {
  background-color: #000;
  border-bottom: 2px solid transparent;
  border-left: 2px solid transparent;
  border-right: 2px solid transparent;
  border-top: 2px solid transparent;
  color: #fff;
}

.e-control.e-listbox-wrapper .e-list-item:hover:not(.e-disabled), .e-control.e-listbox-wrapper .e-list-item:hover.e-selected.e-checklist:not(.e-disabled) {
  background-color: #685708;
  border-color: #fff;
  color: #fff;
}

.e-control.e-listbox-wrapper .e-list-item.e-selected {
  background-color: #ffd939;
  color: #000;
}

.e-control.e-listbox-wrapper .e-list-item.e-selected.e-checklist {
  background-color: #000;
  color: #fff;
}

.e-control.e-listbox-wrapper .e-list-item.e-focused, .e-control.e-listbox-wrapper .e-list-item.e-focused.e-selected.e-checklist {
  background-color: #ffd939;
  color: #000;
}

.e-control.e-listbox-wrapper .e-list-item.e-focused .e-checkbox-wrapper .e-frame.e-check, .e-control.e-listbox-wrapper .e-list-item.e-focused .e-css.e-checkbox-wrapper .e-frame.e-check {
  background-color: #000;
  border-color: #000;
  color: #ffd939;
}

.e-control.e-listbox-wrapper .e-list-group-item {
  background-color: #000;
  border-color: #969696;
  color: #ffd939;
  font-size: 14px;
}

.e-control.e-listbox-wrapper .e-selectall-parent {
  background-color: #000;
  border-bottom: 1px solid #fff;
  color: #fff;
}

.e-control.e-listbox-wrapper .e-sortableclone.e-ripple .e-ripple-element {
  background-color: transparent;
}

.e-control.e-listbox-wrapper.e-list-template .e-list-wrapper {
  height: inherit;
  position: relative;
}

.e-control.e-listbox-wrapper.e-list-template .e-list-wrapper:not(.e-list-multi-line) {
  padding: 0.6153em 0.923em;
}

.e-control.e-listbox-wrapper.e-list-template .e-list-wrapper.e-list-multi-line {
  padding: 0.6153em 0.923em;
}

.e-control.e-listbox-wrapper.e-list-template .e-list-wrapper.e-list-multi-line .e-list-item-header {
  color: #fff;
  display: block;
  font-size: 13px;
  font-weight: 500;
  margin: 0;
  overflow: hidden;
  padding: 0.093em 0;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.e-control.e-listbox-wrapper.e-list-template .e-list-wrapper.e-list-multi-line .e-list-content {
  color: #fff;
  display: block;
  font-size: 13px;
  margin: 0;
  padding: 0.093em 0;
  word-wrap: break-word;
}

.e-control.e-listbox-wrapper.e-list-template .e-list-wrapper.e-list-multi-line .e-list-content:not(.e-text-overflow) {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.e-control.e-listbox-wrapper.e-list-template .e-list-wrapper.e-list-avatar .e-avatar {
  height: 3.0769em;
  left: 0.923em;
  position: absolute;
  top: 0.6153em;
  width: 3.0769em;
}

.e-control.e-listbox-wrapper.e-list-template .e-list-wrapper.e-list-avatar:not(.e-list-badge) {
  padding-left: 4.923em;
  padding-right: 0.923em;
}

.e-control.e-listbox-wrapper.e-list-template .e-list-wrapper.e-list-avatar-right:not(.e-list-badge) {
  padding-left: 0.923em;
  padding-right: 4.923em;
}

.e-control.e-listbox-wrapper.e-list-template .e-list-wrapper.e-list-avatar-right:not(.e-list-badge) .e-avatar {
  height: 3.0769em;
  position: absolute;
  right: 0.923em;
  top: 0.6153em;
  width: 3.0769em;
}

.e-control.e-listbox-wrapper.e-list-template .e-list-wrapper.e-list-multi-line.e-list-avatar .e-avatar {
  top: 0.6153em;
}

.e-control.e-listbox-wrapper.e-list-template .e-list-wrapper.e-list-multi-line.e-list-avatar-right:not(.e-list-badge) .e-avatar {
  top: 0.6153em;
}

.e-control.e-listbox-wrapper.e-list-template .e-list-wrapper.e-list-badge .e-badge {
  font-size: 13px;
  height: 1.5384em;
  line-height: 1.3384em;
  padding: 0;
  position: absolute;
  right: 0.923em;
  top: 50%;
  transform: translateY(-50%);
  width: 1.5384em;
}

.e-control.e-listbox-wrapper.e-list-template .e-list-wrapper.e-list-badge.e-list-avatar {
  padding-left: 4.923em;
  padding-right: 3.3846em;
}

.e-control.e-listbox-wrapper.e-list-template .e-list-wrapper.e-list-badge:not(.e-list-avatar) {
  padding-left: 0.923em;
  padding-right: 3.3846em;
}

.e-control.e-listbox-wrapper.e-list-template .e-list-wrapper:not(.e-list-multi-line) .e-list-content {
  display: block;
  margin: 0;
  overflow: hidden;
  padding: 0.867em 0;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.e-control.e-listbox-wrapper.e-list-template .e-list-item.e-list-item:hover .e-list-item-header {
  color: #fff;
}

.e-control.e-listbox-wrapper.e-list-template .e-list-item.e-list-item:hover .e-list-content {
  color: #fff;
}

.e-control.e-listbox-wrapper.e-list-template .e-list-item.e-selected .e-list-item-header {
  color: #000;
}

.e-control.e-listbox-wrapper.e-list-template .e-list-item.e-selected .e-list-content {
  color: #000;
}

.e-control.e-listbox-wrapper.e-rtl.e-list-template .e-list-wrapper.e-list-avatar .e-avatar {
  left: inherit;
  right: 0.923em;
}

.e-control.e-listbox-wrapper.e-rtl.e-list-template .e-list-wrapper.e-list-avatar:not(.e-list-badge) {
  padding-left: 0.923em;
  padding-right: 4.923em;
}

.e-control.e-listbox-wrapper.e-rtl.e-list-template .e-list-wrapper.e-list-avatar-right:not(.e-list-badge) {
  padding-left: 4.923em;
  padding-right: 0.923em;
}

.e-control.e-listbox-wrapper.e-rtl.e-list-template .e-list-wrapper.e-list-avatar-right:not(.e-list-badge) .e-avatar {
  left: 0.923em;
  right: inherit;
}

.e-control.e-listbox-wrapper.e-rtl.e-list-template .e-list-wrapper.e-list-badge .e-badge {
  left: 0.923em;
  right: inherit;
}

.e-control.e-listbox-wrapper.e-rtl.e-list-template .e-list-wrapper.e-list-badge.e-list-avatar {
  padding-left: 3.3846em;
  padding-right: 4.923em;
}

.e-control.e-listbox-wrapper.e-rtl.e-list-template .e-list-wrapper.e-list-badge:not(.e-list-avatar) {
  padding-left: 3.3846em;
  padding-right: 0.923em;
}

.e-control .e-js [class^='e-'], .e-control .e-js [class*=' e-'] {
  box-sizing: content-box;
}
