@import url("https://fonts.googleapis.com/css?family=Roboto:400,500");
/*! component's theme wise override definitions and variables */
@keyframes hscroll-popup-shadow {
  0% {
    border-color: rgba(255, 255, 255, 0.5);
    box-shadow: 0 0 0 0 rgba(255, 255, 255, 0.5);
  }
  100% {
    box-shadow: 0 0 0 200px rgba(255, 255, 255, 0.12);
  }
}

/*! hscroll icons */
.e-hscroll.e-rtl.e-scroll-device .e-nav-right-arrow::before {
  content: '\e904';
}

.e-hscroll.e-rtl.e-scroll-device .e-nav-left-arrow::before {
  content: '\e913';
}

.e-hscroll.e-rtl .e-nav-left-arrow::before {
  content: '\e913';
}

.e-hscroll.e-rtl .e-nav-right-arrow::before {
  content: '\e904';
}

.e-hscroll.e-scroll-device .e-nav-right-arrow::before {
  content: '\e913';
}

.e-hscroll.e-scroll-device .e-nav-left-arrow::before {
  content: '\e904';
}

.e-hscroll .e-nav-left-arrow::before {
  content: '\e904';
  line-height: normal;
}

.e-hscroll .e-nav-right-arrow::before {
  content: '\e913';
  line-height: normal;
}

/*! h-scroll layout */
.e-bigger .e-hscroll,
.e-hscroll.e-bigger {
  min-height: 56px;
}

.e-bigger .e-hscroll:not(.e-scroll-device),
.e-hscroll.e-bigger:not(.e-scroll-device) {
  padding: 0 50px;
}

.e-bigger .e-hscroll.e-scroll-device,
.e-hscroll.e-bigger.e-scroll-device {
  padding-right: 50px;
}

.e-bigger .e-hscroll.e-rtl.e-scroll-device,
.e-hscroll.e-bigger.e-rtl.e-scroll-device {
  padding-left: 50px;
  padding-right: initial;
}

.e-bigger .e-hscroll .e-nav-arrow.e-icons,
.e-hscroll.e-bigger .e-nav-arrow.e-icons {
  font-size: 14px;
}

.e-bigger .e-hscroll.e-rtl .e-scroll-overlay.e-scroll-right-overlay,
.e-hscroll.e-bigger.e-rtl .e-scroll-overlay.e-scroll-right-overlay {
  left: 50px;
}

.e-bigger .e-hscroll .e-scroll-overlay.e-scroll-right-overlay,
.e-hscroll.e-bigger .e-scroll-overlay.e-scroll-right-overlay {
  right: 50px;
}

.e-bigger .e-hscroll .e-scroll-nav,
.e-hscroll.e-bigger .e-scroll-nav {
  min-height: 56px;
  width: 50px;
}

.e-hscroll {
  display: block;
  position: relative;
  width: inherit;
}

.e-hscroll.e-rtl.e-scroll-device {
  padding-left: 50px;
  padding-right: initial;
}

.e-hscroll.e-rtl.e-scroll-device .e-scroll-nav {
  transform: skewX(-16deg) translateX(-6px);
}

.e-hscroll.e-rtl.e-scroll-device .e-scroll-overlay.e-scroll-right-overlay {
  left: 56px;
  right: auto;
  transform: skewX(-16deg) translateX(-6px);
}

.e-hscroll.e-rtl.e-scroll-device .e-scroll-overlay.e-scroll-left-overlay {
  left: auto;
  right: 0;
}

.e-hscroll:not(.e-scroll-device) {
  padding: 0 40px;
}

.e-hscroll.e-scroll-device {
  padding-right: 50px;
}

.e-hscroll.e-scroll-device .e-scroll-nav {
  transform: skewX(-16deg) translateX(6px);
  width: 56px;
}

.e-hscroll.e-scroll-device .e-scroll-nav .e-nav-arrow {
  font-size: 14px;
  transform: skewX(16deg);
}

.e-hscroll.e-scroll-device .e-scroll-overlay {
  opacity: 0.5;
  pointer-events: none;
  position: absolute;
  top: 0;
  z-index: 100;
}

.e-hscroll.e-scroll-device .e-scroll-overlay.e-scroll-right-overlay {
  left: auto;
  right: 56px;
  transform: skewX(-16deg) translateX(6px);
}

.e-hscroll.e-scroll-device .e-scroll-overlay.e-scroll-left-overlay {
  left: 0;
  right: auto;
}

.e-hscroll.e-overlay .e-hscroll-content > * {
  pointer-events: none;
}

.e-hscroll > * {
  height: inherit;
}

.e-hscroll .e-hscroll-content {
  display: inline-block;
  height: inherit;
  position: relative;
  -ms-touch-action: none;
      touch-action: none;
}

.e-hscroll .e-hscroll-content > * {
  pointer-events: auto;
}

.e-hscroll.e-rtl .e-scroll-nav.e-scroll-right-nav {
  left: 0;
  right: auto;
}

.e-hscroll.e-rtl .e-scroll-nav.e-scroll-left-nav {
  left: auto;
  right: 0;
}

.e-hscroll .e-scroll-nav {
  -ms-flex-align: center;
      align-items: center;
  bottom: 0;
  cursor: pointer;
  display: -ms-flexbox;
  display: flex;
  height: 100%;
  min-height: 42px;
  overflow: hidden;
  position: absolute;
  top: 0;
  width: 40px;
}

.e-hscroll .e-scroll-nav.e-scroll-left-nav {
  left: 0;
}

.e-hscroll .e-scroll-nav.e-scroll-right-nav {
  right: 0;
}

.e-hscroll .e-scroll-nav.e-ie-align {
  display: table;
}

.e-hscroll .e-nav-arrow {
  position: relative;
}

.e-hscroll .e-nav-arrow.e-icons {
  display: table-cell;
  text-align: center;
  vertical-align: middle;
  width: 100%;
}

.e-hscroll .e-hscroll-bar .e-hscroll-content .e-overlay {
  pointer-events: none;
}

/*! h-scroll theme */
.e-hscroll .e-icons {
  color: rgba(0, 0, 0, 0.54);
}

.e-hscroll.e-rtl.e-scroll-device .e-scroll-nav.e-scroll-right-nav {
  border-color: rgba(0, 0, 0, 0.12);
  box-shadow: 4px 0 8px 0 rgba(0, 0, 0, 0.06);
}

.e-hscroll.e-scroll-device .e-scroll-nav.e-scroll-right-nav {
  background-color: #fafafa;
  border-color: rgba(0, 0, 0, 0.12);
  border-width: 1px;
  box-shadow: -4px 0 8px 0 rgba(0, 0, 0, 0.06);
}

.e-hscroll.e-scroll-device .e-scroll-nav.e-scroll-right-nav .e-nav-arrow {
  color: #e3165b;
}

.e-hscroll .e-scroll-overlay {
  background-color: transparent;
  background-repeat: repeat-x;
}

.e-hscroll .e-scroll-overlay.e-scroll-left-overlay {
  background-image: linear-gradient(-270deg, #fafafa 0%, rgba(250, 250, 250, 0) 100%);
}

.e-hscroll .e-scroll-overlay.e-scroll-right-overlay {
  background-image: linear-gradient(-270deg, rgba(250, 250, 250, 0) 0%, #fafafa 100%);
}

.e-hscroll.e-rtl .e-scroll-nav {
  background: #fafafa;
}

.e-hscroll.e-rtl .e-scroll-nav.e-scroll-left-nav {
  border-left: 1px solid rgba(0, 0, 0, 0.12);
  border-right: 0;
}

.e-hscroll.e-rtl .e-scroll-nav.e-scroll-right-nav {
  border-left: 0;
  border-right: 1px solid rgba(0, 0, 0, 0.12);
}

.e-hscroll.e-rtl .e-scroll-nav:hover {
  background: rgba(0, 0, 0, 0.12);
  border: "";
  border-color: rgba(0, 0, 0, 0.12);
  color: #000;
}

.e-hscroll:not(.e-scroll-device) .e-scroll-nav:hover {
  background: rgba(0, 0, 0, 0.12);
  border: "";
  color: #000;
}

.e-hscroll:not(.e-scroll-device) .e-scroll-nav:focus {
  background: rgba(0, 0, 0, 0.12);
  border: "";
  border-color: rgba(0, 0, 0, 0.12);
  color: #000;
}

.e-hscroll:not(.e-scroll-device) .e-scroll-nav:active {
  background: #bdbdbd;
  border: "";
  box-shadow: "";
  color: #000;
}

.e-hscroll .e-scroll-nav {
  background: #fafafa;
}

.e-hscroll .e-scroll-nav.e-scroll-left-nav {
  border-right: 1px solid rgba(0, 0, 0, 0.12);
}

.e-hscroll .e-scroll-nav.e-scroll-right-nav {
  border-left: 1px solid rgba(0, 0, 0, 0.12);
}

.e-hscroll .e-scroll-nav::after {
  background-color: transparent;
  border-radius: 50%;
  border-width: 1px;
  box-sizing: border-box;
  content: '';
  height: 1px;
  left: 50%;
  position: absolute;
  top: 50%;
  visibility: hidden;
  width: 1px;
}

.e-hscroll .e-scroll-nav:active::after {
  animation: hscroll-popup-shadow .6s ease-out 0ms;
  visibility: visible;
}
