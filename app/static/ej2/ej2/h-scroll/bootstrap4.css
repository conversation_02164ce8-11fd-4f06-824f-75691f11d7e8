/*! component's theme wise override definitions and variables */
/*! hscroll icons */
.e-hscroll.e-rtl.e-scroll-device .e-nav-right-arrow::before {
  content: '\e70d';
}

.e-hscroll.e-rtl.e-scroll-device .e-nav-left-arrow::before {
  content: '\e76a';
}

.e-hscroll.e-rtl .e-nav-left-arrow::before {
  content: '\e71f';
}

.e-hscroll.e-rtl .e-nav-right-arrow::before {
  content: '\e70b';
}

.e-hscroll.e-scroll-device .e-nav-right-arrow::before {
  content: '\e76a';
}

.e-hscroll.e-scroll-device .e-nav-left-arrow::before {
  content: '\e70d';
}

.e-hscroll .e-nav-left-arrow::before {
  content: '\e71f';
  line-height: normal;
}

.e-hscroll .e-nav-right-arrow::before {
  content: '\e70b';
  line-height: normal;
}

/*! h-scroll layout */
.e-bigger .e-hscroll,
.e-hscroll.e-bigger {
  min-height: 50px;
}

.e-bigger .e-hscroll:not(.e-scroll-device),
.e-hscroll.e-bigger:not(.e-scroll-device) {
  padding: 0 38px;
}

.e-bigger .e-hscroll.e-scroll-device,
.e-hscroll.e-bigger.e-scroll-device {
  padding-right: 38px;
}

.e-bigger .e-hscroll.e-rtl.e-scroll-device,
.e-hscroll.e-bigger.e-rtl.e-scroll-device {
  padding-left: 38px;
  padding-right: initial;
}

.e-bigger .e-hscroll .e-nav-arrow.e-icons,
.e-hscroll.e-bigger .e-nav-arrow.e-icons {
  font-size: 14px;
}

.e-bigger .e-hscroll.e-rtl .e-scroll-overlay.e-scroll-right-overlay,
.e-hscroll.e-bigger.e-rtl .e-scroll-overlay.e-scroll-right-overlay {
  left: 38px;
}

.e-bigger .e-hscroll .e-scroll-overlay.e-scroll-right-overlay,
.e-hscroll.e-bigger .e-scroll-overlay.e-scroll-right-overlay {
  right: 38px;
}

.e-bigger .e-hscroll .e-scroll-nav,
.e-hscroll.e-bigger .e-scroll-nav {
  min-height: 50px;
  width: 38px;
}

.e-hscroll {
  display: block;
  position: relative;
  width: inherit;
}

.e-hscroll.e-rtl.e-scroll-device {
  padding-left: 50px;
  padding-right: initial;
}

.e-hscroll.e-rtl.e-scroll-device .e-scroll-nav {
  transform: skewX(-16deg) translateX(-6px);
}

.e-hscroll.e-rtl.e-scroll-device .e-scroll-overlay.e-scroll-right-overlay {
  left: 52px;
  right: auto;
  transform: skewX(-16deg) translateX(-6px);
}

.e-hscroll.e-rtl.e-scroll-device .e-scroll-overlay.e-scroll-left-overlay {
  left: auto;
  right: 0;
}

.e-hscroll:not(.e-scroll-device) {
  padding: 0 32px;
}

.e-hscroll.e-scroll-device {
  padding-right: 50px;
}

.e-hscroll.e-scroll-device .e-scroll-nav {
  transform: skewX(-16deg) translateX(6px);
  width: 52px;
}

.e-hscroll.e-scroll-device .e-scroll-nav .e-nav-arrow {
  font-size: 14px;
  transform: skewX(16deg);
}

.e-hscroll.e-scroll-device .e-scroll-overlay {
  opacity: 0.5;
  pointer-events: none;
  position: absolute;
  top: 0;
  z-index: 100;
}

.e-hscroll.e-scroll-device .e-scroll-overlay.e-scroll-right-overlay {
  left: auto;
  right: 52px;
  transform: skewX(-16deg) translateX(6px);
}

.e-hscroll.e-scroll-device .e-scroll-overlay.e-scroll-left-overlay {
  left: 0;
  right: auto;
}

.e-hscroll.e-overlay .e-hscroll-content > * {
  pointer-events: none;
}

.e-hscroll > * {
  height: inherit;
}

.e-hscroll .e-hscroll-content {
  display: inline-block;
  height: inherit;
  position: relative;
  -ms-touch-action: none;
      touch-action: none;
}

.e-hscroll .e-hscroll-content > * {
  pointer-events: auto;
}

.e-hscroll.e-rtl .e-scroll-nav.e-scroll-right-nav {
  left: 0;
  right: auto;
}

.e-hscroll.e-rtl .e-scroll-nav.e-scroll-left-nav {
  left: auto;
  right: 0;
}

.e-hscroll .e-scroll-nav {
  -ms-flex-align: center;
      align-items: center;
  bottom: 0;
  cursor: pointer;
  display: -ms-flexbox;
  display: flex;
  height: 100%;
  min-height: 38px;
  overflow: hidden;
  position: absolute;
  top: 0;
  width: 32px;
}

.e-hscroll .e-scroll-nav.e-scroll-left-nav {
  left: 0;
}

.e-hscroll .e-scroll-nav.e-scroll-right-nav {
  right: 0;
}

.e-hscroll .e-scroll-nav.e-ie-align {
  display: table;
}

.e-hscroll .e-nav-arrow {
  position: relative;
}

.e-hscroll .e-nav-arrow.e-icons {
  display: table-cell;
  text-align: center;
  vertical-align: middle;
  width: 100%;
}

.e-hscroll .e-hscroll-bar .e-hscroll-content .e-overlay {
  pointer-events: none;
}

/*! h-scroll theme */
.e-hscroll .e-icons {
  color: #333;
}

.e-hscroll.e-rtl.e-scroll-device .e-scroll-nav.e-scroll-right-nav {
  border-color: #ccc;
  box-shadow: 4px 0 8px 0 rgba(108, 117, 125, 0.06);
}

.e-hscroll.e-scroll-device .e-scroll-nav.e-scroll-right-nav {
  background-color: #f8f8f8;
  border-color: #ccc;
  border-width: 1px;
  box-shadow: -4px 0 8px 0 rgba(108, 117, 125, 0.06);
}

.e-hscroll.e-scroll-device .e-scroll-nav.e-scroll-right-nav .e-nav-arrow {
  color: #317ab9;
}

.e-hscroll .e-scroll-overlay {
  background-color: transparent;
  background-repeat: repeat-x;
}

.e-hscroll .e-scroll-overlay.e-scroll-left-overlay {
  background-image: linear-gradient(-270deg, white 0%, rgba(255, 255, 255, 0) 100%);
}

.e-hscroll .e-scroll-overlay.e-scroll-right-overlay {
  background-image: linear-gradient(-270deg, rgba(255, 255, 255, 0) 0%, white 100%);
}

.e-hscroll.e-rtl .e-scroll-nav {
  background: #f8f8f8;
}

.e-hscroll.e-rtl .e-scroll-nav.e-scroll-left-nav {
  border-left: 1px solid #ccc;
  border-right: 0;
}

.e-hscroll.e-rtl .e-scroll-nav.e-scroll-right-nav {
  border-left: 0;
  border-right: 1px solid #ccc;
}

.e-hscroll.e-rtl .e-scroll-nav:hover {
  background: #5a6268;
  border: 1px #545b62;
  border-color: "";
  color: #fff;
}

.e-hscroll:not(.e-scroll-device) .e-scroll-nav:hover {
  background: #5a6268;
  border: 1px #545b62;
  color: #fff;
}

.e-hscroll:not(.e-scroll-device) .e-scroll-nav:hover .e-icons {
  color: inherit;
}

.e-hscroll:not(.e-scroll-device) .e-scroll-nav:focus {
  background: #5a6268;
  border: 1px #6c757d;
  border-color: "";
  color: #fff;
}

.e-hscroll:not(.e-scroll-device) .e-scroll-nav:focus .e-icons {
  color: inherit;
}

.e-hscroll:not(.e-scroll-device) .e-scroll-nav:active {
  background: #6c757d;
  border: 3px solid #adb5bd;
  box-shadow: none;
  color: #fff;
}

.e-hscroll .e-scroll-nav {
  background: #f8f8f8;
}

.e-hscroll .e-scroll-nav.e-scroll-left-nav {
  border-right: 1px solid #ccc;
}

.e-hscroll .e-scroll-nav.e-scroll-right-nav {
  border-left: 1px solid #ccc;
}

.e-hscroll .e-scroll-nav::after {
  content: '';
}

.e-hscroll .e-scroll-nav:active::after {
  content: '';
}
