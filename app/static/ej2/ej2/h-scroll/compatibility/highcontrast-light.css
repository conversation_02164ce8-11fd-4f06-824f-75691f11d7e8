/*! hscroll icons */
.e-control.e-hscroll.e-rtl.e-scroll-device .e-nav-right-arrow::before {
  content: '\e904';
}

.e-control.e-hscroll.e-rtl.e-scroll-device .e-nav-left-arrow::before {
  content: '\e913';
}

.e-control.e-hscroll.e-rtl .e-nav-left-arrow::before {
  content: '\e830';
}

.e-control.e-hscroll.e-rtl .e-nav-right-arrow::before {
  content: '\e829';
}

.e-control.e-hscroll.e-scroll-device .e-nav-right-arrow::before {
  content: '\e913';
}

.e-control.e-hscroll.e-scroll-device .e-nav-left-arrow::before {
  content: '\e904';
}

.e-control.e-hscroll .e-nav-left-arrow::before {
  content: '\e829';
  line-height: normal;
}

.e-control.e-hscroll .e-nav-right-arrow::before {
  content: '\e830';
  line-height: normal;
}

/*! h-scroll layout */
.e-bigger .e-control.e-hscroll, .e-control.e-hscroll.e-bigger {
  min-height: 50px;
}

.e-bigger .e-control.e-hscroll:not(.e-scroll-device), .e-control.e-hscroll.e-bigger:not(.e-scroll-device) {
  padding: 0 50px;
}

.e-bigger .e-control.e-hscroll.e-scroll-device, .e-control.e-hscroll.e-bigger.e-scroll-device {
  padding-right: 50px;
}

.e-bigger .e-control.e-hscroll.e-rtl.e-scroll-device, .e-control.e-hscroll.e-bigger.e-rtl.e-scroll-device {
  padding-left: 50px;
  padding-right: initial;
}

.e-bigger .e-control.e-hscroll .e-nav-arrow.e-icons, .e-control.e-hscroll.e-bigger .e-nav-arrow.e-icons {
  font-size: 18px;
}

.e-bigger .e-control.e-hscroll.e-rtl .e-scroll-overlay.e-scroll-right-overlay, .e-control.e-hscroll.e-bigger.e-rtl .e-scroll-overlay.e-scroll-right-overlay {
  left: 50px;
}

.e-bigger .e-control.e-hscroll .e-scroll-overlay.e-scroll-right-overlay, .e-control.e-hscroll.e-bigger .e-scroll-overlay.e-scroll-right-overlay {
  right: 50px;
}

.e-bigger .e-control.e-hscroll .e-scroll-nav, .e-control.e-hscroll.e-bigger .e-scroll-nav {
  min-height: 50px;
  width: 50px;
}

.e-control.e-hscroll {
  display: block;
  position: relative;
  width: inherit;
}

.e-control.e-hscroll.e-rtl.e-scroll-device {
  padding-left: 50px;
  padding-right: initial;
}

.e-control.e-hscroll.e-rtl.e-scroll-device .e-scroll-nav {
  transform: skewX(-16deg) translateX(-6px);
}

.e-control.e-hscroll.e-rtl.e-scroll-device .e-scroll-overlay.e-scroll-right-overlay {
  left: 50px;
  right: auto;
  transform: skewX(-16deg) translateX(-6px);
}

.e-control.e-hscroll.e-rtl.e-scroll-device .e-scroll-overlay.e-scroll-left-overlay {
  left: auto;
  right: 0;
}

.e-control.e-hscroll:not(.e-scroll-device) {
  padding: 0 40px;
}

.e-control.e-hscroll.e-scroll-device {
  padding-right: 50px;
}

.e-control.e-hscroll.e-scroll-device .e-scroll-nav {
  transform: skewX(-16deg) translateX(6px);
  width: 50px;
}

.e-control.e-hscroll.e-scroll-device .e-scroll-nav .e-nav-arrow {
  font-size: 14px;
  transform: skewX(16deg);
}

.e-control.e-hscroll.e-scroll-device .e-scroll-overlay {
  opacity: 0.5;
  pointer-events: none;
  position: absolute;
  top: 0;
  z-index: 100;
}

.e-control.e-hscroll.e-scroll-device .e-scroll-overlay.e-scroll-right-overlay {
  left: auto;
  right: 50px;
  transform: skewX(-16deg) translateX(6px);
}

.e-control.e-hscroll.e-scroll-device .e-scroll-overlay.e-scroll-left-overlay {
  left: 0;
  right: auto;
}

.e-control.e-hscroll.e-overlay .e-hscroll-content > * {
  pointer-events: none;
}

.e-control.e-hscroll > * {
  height: inherit;
}

.e-control.e-hscroll .e-hscroll-content {
  display: inline-block;
  height: inherit;
  position: relative;
  -ms-touch-action: none;
      touch-action: none;
}

.e-control.e-hscroll .e-hscroll-content > * {
  pointer-events: auto;
}

.e-control.e-hscroll.e-rtl .e-scroll-nav.e-scroll-right-nav {
  left: 0;
  right: auto;
}

.e-control.e-hscroll.e-rtl .e-scroll-nav.e-scroll-left-nav {
  left: auto;
  right: 0;
}

.e-control.e-hscroll .e-scroll-nav {
  -ms-flex-align: center;
      align-items: center;
  bottom: 0;
  cursor: pointer;
  display: -ms-flexbox;
  display: flex;
  height: 100%;
  min-height: 40px;
  overflow: hidden;
  position: absolute;
  top: 0;
  width: 40px;
}

.e-control.e-hscroll .e-scroll-nav.e-scroll-left-nav {
  left: 0;
}

.e-control.e-hscroll .e-scroll-nav.e-scroll-right-nav {
  right: 0;
}

.e-control.e-hscroll .e-scroll-nav.e-ie-align {
  display: table;
}

.e-control.e-hscroll .e-nav-arrow {
  position: relative;
}

.e-control.e-hscroll .e-nav-arrow.e-icons {
  display: table-cell;
  text-align: center;
  vertical-align: middle;
  width: 100%;
}

.e-control.e-hscroll .e-hscroll-bar .e-hscroll-content .e-overlay {
  pointer-events: none;
}

/*! h-scroll theme */
.e-control.e-hscroll .e-icons {
  color: #400074;
}

.e-control.e-hscroll.e-rtl.e-scroll-device .e-scroll-nav.e-scroll-right-nav {
  border-color: #757575;
  box-shadow: 4px 0 8px 0 rgba(0, 0, 0, 0.06);
}

.e-control.e-hscroll.e-scroll-device .e-scroll-nav.e-scroll-right-nav {
  background-color: #fff;
  border-color: #757575;
  border-width: none;
  box-shadow: -4px 0 8px 0 rgba(0, 0, 0, 0.6);
}

.e-control.e-hscroll.e-scroll-device .e-scroll-nav.e-scroll-right-nav .e-nav-arrow {
  color: #000;
}

.e-control.e-hscroll .e-scroll-overlay {
  background-color: transparent;
  background-repeat: repeat-x;
}

.e-control.e-hscroll .e-scroll-overlay.e-scroll-left-overlay {
  background-image: linear-gradient(to right, rgba(255, 255, 255, 0.5) 0, rgba(255, 255, 255, 0.0001) 100%);
}

.e-control.e-hscroll .e-scroll-overlay.e-scroll-right-overlay {
  background-image: linear-gradient(to right, rgba(255, 255, 255, 0.0001) 0, rgba(255, 255, 255, 0.5) 100%);
}

.e-control.e-hscroll.e-rtl .e-scroll-nav {
  background: #fff;
}

.e-control.e-hscroll.e-rtl .e-scroll-nav.e-scroll-left-nav {
  border-left: 1px solid #000;
  border-right: 0;
}

.e-control.e-hscroll.e-rtl .e-scroll-nav.e-scroll-right-nav {
  border-left: 0;
  border-right: 1px solid #000;
}

.e-control.e-hscroll.e-rtl .e-scroll-nav:hover {
  background: #ecf;
  border: "";
  border-color: "";
  color: #000;
}

.e-control.e-hscroll.e-rtl .e-scroll-nav:hover:active {
  background: #400074;
}

.e-control.e-hscroll:not(.e-scroll-device) .e-scroll-nav:hover {
  background: #ecf;
  border: "";
  color: #000;
  border: 2px solid #000;
}

.e-control.e-hscroll:not(.e-scroll-device) .e-scroll-nav:hover:active {
  border: 0;
}

.e-control.e-hscroll:not(.e-scroll-device) .e-scroll-nav:focus {
  background: #ecf;
  border: "";
  border-color: "";
  color: #000;
  background: inherit;
  border: 2px solid #fff;
}

.e-control.e-hscroll:not(.e-scroll-device) .e-scroll-nav:active {
  background: #400074;
  border: "";
  box-shadow: "";
  color: #000;
}

.e-control.e-hscroll:not(.e-scroll-device) .e-scroll-nav:active .e-icons {
  color: #fff;
}

.e-control.e-hscroll .e-scroll-nav {
  background: #fff;
}

.e-control.e-hscroll .e-scroll-nav.e-scroll-left-nav {
  border-right: 1px solid #000;
}

.e-control.e-hscroll .e-scroll-nav.e-scroll-right-nav {
  border-left: 1px solid #000;
}

.e-control.e-hscroll .e-scroll-nav::after {
  content: '';
}

.e-control.e-hscroll .e-scroll-nav:active::after {
  content: '';
}

.e-control .e-js [class^='e-'], .e-control .e-js [class*=' e-'] {
  box-sizing: content-box;
}
