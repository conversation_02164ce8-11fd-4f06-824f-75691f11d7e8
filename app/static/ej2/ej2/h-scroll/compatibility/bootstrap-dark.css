/*! hscroll icons */
.e-control.e-hscroll.e-rtl.e-scroll-device .e-nav-right-arrow::before {
  content: '\e904';
}

.e-control.e-hscroll.e-rtl.e-scroll-device .e-nav-left-arrow::before {
  content: '\e913';
}

.e-control.e-hscroll.e-rtl .e-nav-left-arrow::before {
  content: '\e848';
}

.e-control.e-hscroll.e-rtl .e-nav-right-arrow::before {
  content: '\e84b';
}

.e-control.e-hscroll.e-scroll-device .e-nav-right-arrow::before {
  content: '\e913';
}

.e-control.e-hscroll.e-scroll-device .e-nav-left-arrow::before {
  content: '\e904';
}

.e-control.e-hscroll .e-nav-left-arrow::before {
  content: '\e84b';
  line-height: normal;
}

.e-control.e-hscroll .e-nav-right-arrow::before {
  content: '\e848';
  line-height: normal;
}

/*! h-scroll layout */
.e-bigger .e-control.e-hscroll, .e-control.e-hscroll.e-bigger {
  min-height: 50px;
}

.e-bigger .e-control.e-hscroll:not(.e-scroll-device), .e-control.e-hscroll.e-bigger:not(.e-scroll-device) {
  padding: 0 50px;
}

.e-bigger .e-control.e-hscroll.e-scroll-device, .e-control.e-hscroll.e-bigger.e-scroll-device {
  padding-right: 50px;
}

.e-bigger .e-control.e-hscroll.e-rtl.e-scroll-device, .e-control.e-hscroll.e-bigger.e-rtl.e-scroll-device {
  padding-left: 50px;
  padding-right: initial;
}

.e-bigger .e-control.e-hscroll .e-nav-arrow.e-icons, .e-control.e-hscroll.e-bigger .e-nav-arrow.e-icons {
  font-size: 14px;
}

.e-bigger .e-control.e-hscroll.e-rtl .e-scroll-overlay.e-scroll-right-overlay, .e-control.e-hscroll.e-bigger.e-rtl .e-scroll-overlay.e-scroll-right-overlay {
  left: 50px;
}

.e-bigger .e-control.e-hscroll .e-scroll-overlay.e-scroll-right-overlay, .e-control.e-hscroll.e-bigger .e-scroll-overlay.e-scroll-right-overlay {
  right: 50px;
}

.e-bigger .e-control.e-hscroll .e-scroll-nav, .e-control.e-hscroll.e-bigger .e-scroll-nav {
  min-height: 50px;
  width: 50px;
}

.e-control.e-hscroll {
  display: block;
  position: relative;
  width: inherit;
}

.e-control.e-hscroll.e-rtl.e-scroll-device {
  padding-left: 50px;
  padding-right: initial;
}

.e-control.e-hscroll.e-rtl.e-scroll-device .e-scroll-nav {
  transform: skewX(-16deg) translateX(-6px);
}

.e-control.e-hscroll.e-rtl.e-scroll-device .e-scroll-overlay.e-scroll-right-overlay {
  left: 50px;
  right: auto;
  transform: skewX(-16deg) translateX(-6px);
}

.e-control.e-hscroll.e-rtl.e-scroll-device .e-scroll-overlay.e-scroll-left-overlay {
  left: auto;
  right: 0;
}

.e-control.e-hscroll:not(.e-scroll-device) {
  padding: 0 40px;
}

.e-control.e-hscroll.e-scroll-device {
  padding-right: 50px;
}

.e-control.e-hscroll.e-scroll-device .e-scroll-nav {
  transform: skewX(-16deg) translateX(6px);
  width: 50px;
}

.e-control.e-hscroll.e-scroll-device .e-scroll-nav .e-nav-arrow {
  font-size: 14px;
  transform: skewX(16deg);
}

.e-control.e-hscroll.e-scroll-device .e-scroll-overlay {
  opacity: 0.5;
  pointer-events: none;
  position: absolute;
  top: 0;
  z-index: 100;
}

.e-control.e-hscroll.e-scroll-device .e-scroll-overlay.e-scroll-right-overlay {
  left: auto;
  right: 50px;
  transform: skewX(-16deg) translateX(6px);
}

.e-control.e-hscroll.e-scroll-device .e-scroll-overlay.e-scroll-left-overlay {
  left: 0;
  right: auto;
}

.e-control.e-hscroll.e-overlay .e-hscroll-content > * {
  pointer-events: none;
}

.e-control.e-hscroll > * {
  height: inherit;
}

.e-control.e-hscroll .e-hscroll-content {
  display: inline-block;
  height: inherit;
  position: relative;
  -ms-touch-action: none;
      touch-action: none;
}

.e-control.e-hscroll .e-hscroll-content > * {
  pointer-events: auto;
}

.e-control.e-hscroll.e-rtl .e-scroll-nav.e-scroll-right-nav {
  left: 0;
  right: auto;
}

.e-control.e-hscroll.e-rtl .e-scroll-nav.e-scroll-left-nav {
  left: auto;
  right: 0;
}

.e-control.e-hscroll .e-scroll-nav {
  -ms-flex-align: center;
      align-items: center;
  bottom: 0;
  cursor: pointer;
  display: -ms-flexbox;
  display: flex;
  height: 100%;
  min-height: 40px;
  overflow: hidden;
  position: absolute;
  top: 0;
  width: 40px;
}

.e-control.e-hscroll .e-scroll-nav.e-scroll-left-nav {
  left: 0;
}

.e-control.e-hscroll .e-scroll-nav.e-scroll-right-nav {
  right: 0;
}

.e-control.e-hscroll .e-scroll-nav.e-ie-align {
  display: table;
}

.e-control.e-hscroll .e-nav-arrow {
  position: relative;
}

.e-control.e-hscroll .e-nav-arrow.e-icons {
  display: table-cell;
  text-align: center;
  vertical-align: middle;
  width: 100%;
}

.e-control.e-hscroll .e-hscroll-bar .e-hscroll-content .e-overlay {
  pointer-events: none;
}

/*! h-scroll theme */
.e-control.e-hscroll .e-icons {
  color: #f0f0f0;
}

.e-control.e-hscroll.e-rtl.e-scroll-device .e-scroll-nav.e-scroll-right-nav {
  border-color: #1a1a1a;
  box-shadow: 4px 0 8px 0 rgba(0, 0, 0, 0.06);
}

.e-control.e-hscroll.e-scroll-device .e-scroll-nav.e-scroll-right-nav {
  background-color: #131313;
  border-color: #1a1a1a;
  border-width: none;
  box-shadow: -4px 0 8px 0 rgba(0, 0, 0, 0.6);
}

.e-control.e-hscroll.e-scroll-device .e-scroll-nav.e-scroll-right-nav .e-nav-arrow {
  color: #fff;
}

.e-control.e-hscroll .e-scroll-overlay {
  background-color: transparent;
  background-repeat: repeat-x;
}

.e-control.e-hscroll .e-scroll-overlay.e-scroll-left-overlay {
  background-image: linear-gradient(to right, rgba(19, 19, 19, 0.5) 0, rgba(19, 19, 19, 0.0001) 100%);
}

.e-control.e-hscroll .e-scroll-overlay.e-scroll-right-overlay {
  background-image: linear-gradient(to right, rgba(19, 19, 19, 0.0001) 0, rgba(19, 19, 19, 0.5) 100%);
}

.e-control.e-hscroll.e-rtl .e-scroll-nav {
  background: #131313;
}

.e-control.e-hscroll.e-rtl .e-scroll-nav.e-scroll-left-nav {
  border-left: 1px solid #505050;
  border-right: 0;
}

.e-control.e-hscroll.e-rtl .e-scroll-nav.e-scroll-right-nav {
  border-left: 0;
  border-right: 1px solid #505050;
}

.e-control.e-hscroll.e-rtl .e-scroll-nav:hover {
  background: #393939;
  border: "";
  border-color: "";
  color: #fff;
}

.e-control.e-hscroll:not(.e-scroll-device) .e-scroll-nav:hover {
  background: #393939;
  border: "";
  color: #fff;
}

.e-control.e-hscroll:not(.e-scroll-device) .e-scroll-nav:focus {
  background: #393939;
  border: "";
  border-color: "";
  color: #fff;
}

.e-control.e-hscroll:not(.e-scroll-device) .e-scroll-nav:active {
  background: #2a2a2a;
  border: "";
  box-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
  color: #fff;
}

.e-control.e-hscroll .e-scroll-nav {
  background: #131313;
}

.e-control.e-hscroll .e-scroll-nav.e-scroll-left-nav {
  border-right: 1px solid #505050;
}

.e-control.e-hscroll .e-scroll-nav.e-scroll-right-nav {
  border-left: 1px solid #505050;
}

.e-control.e-hscroll .e-scroll-nav::after {
  content: '';
}

.e-control.e-hscroll .e-scroll-nav:active::after {
  content: '';
}

.e-control .e-js [class^='e-'], .e-control .e-js [class*=' e-'] {
  box-sizing: content-box;
}
