@import url("https://fonts.googleapis.com/css?family=Roboto:400,500");
/*! PdfViewer's default definitions and variables */
.e-pdfviewer .e-pv-icon::before {
  font-family: 'e-icons';
}

.e-pdfviewer .e-pv-icon-search::before {
  font-family: 'e-icons';
  font-size: 12px;
}

.e-pdfviewer .e-pv-open-document-icon::before {
  content: '\ec04';
}

.e-pdfviewer .e-pv-download-document-icon::before {
  content: '\ec0b';
}

.e-pdfviewer .e-pv-print-document-icon::before {
  content: '\ec20';
}

.e-pdfviewer .e-pv-first-page-navigation-icon::before {
  content: '\ec05';
}

.e-pdfviewer .e-pv-previous-page-navigation-icon::before, .e-pdfviewer .e-pv-prev-search-icon::before {
  content: '\ec06';
}

.e-pdfviewer .e-pv-next-page-navigation-icon::before, .e-pdfviewer .e-pv-next-search-icon::before {
  content: '\ec07';
}

.e-pdfviewer .e-pv-last-page-navigation-icon::before {
  content: '\ec08';
}

.e-pdfviewer .e-pv-zoom-out-icon::before {
  content: '\ec09';
}

.e-pdfviewer .e-pv-zoom-in-icon::before {
  content: '\ec0a';
}

.e-pdfviewer .e-pv-thumbnail-view-icon::before {
  content: '\ec27';
}

.e-pdfviewer .e-pv-thumbnail-view-disable-icon::before {
  color: rgba(255, 255, 255, 0.2);
  content: '\ec27';
}

.e-pdfviewer .e-pv-thumbnail-view-selection-icon::before {
  color: rgba(0, 176, 255, 0.6);
  content: '\ec27';
}

.e-pdfviewer .e-pv-bookmark-icon::before {
  content: '\ec0c';
}

.e-pdfviewer .e-pv-bookmark-disable-icon::before {
  color: rgba(255, 255, 255, 0.2);
  content: '\ec0c';
}

.e-pdfviewer .e-pv-bookmark-selection-icon::before {
  color: rgba(0, 176, 255, 0.6);
  content: '\ec0c';
}

.e-pdfviewer .e-pv-title-close-icon::before, .e-pdfviewer .e-pv-annotation-tools-close-icon::before, .e-pdfviewer .e-pv-annotation-popup-close::before {
  content: '\ec0f';
}

.e-pdfviewer .e-pv-resize-icon::before {
  content: '\e84b';
  font-size: 10px;
}

.e-pdfviewer .e-pv-text-select-tool-icon::before {
  content: '\ec1d';
}

.e-pdfviewer .e-pv-pan-tool-icon::before {
  content: '\ec1a';
}

.e-pdfviewer .e-pv-text-search-icon::before {
  content: '\ec0d';
}

.e-pdfviewer .e-pv-search-icon::before {
  content: '\ec0d';
  font-family: 'e-icons';
}

.e-pdfviewer .e-pv-search-close::before {
  content: '\ec0f';
  font-family: 'e-icons';
}

.e-pdfviewer .e-pv-annotation-icon::before {
  content: '\ec26';
}

.e-pdfviewer .e-pv-annotation-color-icon::before {
  content: '\ec19';
}

.e-pdfviewer .e-pv-annotation-opacity-icon::before {
  content: '\ec1b';
}

.e-pdfviewer .e-pv-annotation-thickness-icon::before {
  content: '\ec25';
}

.e-pdfviewer .e-pv-annotation-delete-icon::before {
  content: '\ec1c';
}

.e-pdfviewer .e-pv-undo-icon::before {
  content: '\ec14';
}

.e-pdfviewer .e-pv-redo-icon::before {
  content: '\ec21';
}

.e-pdfviewer .e-pv-more-icon::before {
  content: '\ec16';
}

.e-pdfviewer .e-pv-backward-icon::before {
  content: '\e977';
}

.e-pdfviewer .e-pv-notification-icon {
  background-image: url("data:image/svg+xml;base64,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");
  background-repeat: no-repeat;
  background-size: 36.7px 31.7px;
  height: 31.7px;
}

.e-pdfviewer .e-pv-notification-icon-rtl {
  background-image: url("data:image/svg+xml;base64,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");
  background-position: right;
  background-repeat: no-repeat;
  background-size: 36.7px 31.7px;
  height: 31.7px;
}

.e-pdfviewer .e-pv-annotation-calibrate-icon::before {
  content: '\e678';
}

.e-pv-download-document-icon.e-menu-icon::before {
  content: '\ec0b';
}

.e-pv-bookmark-icon.e-menu-icon::before {
  content: '\ec0c';
}

.e-pv-highlight-icon::before {
  content: '\ec15';
  font-family: 'e-icons';
}

.e-pv-underline-icon::before {
  content: '\ec17';
  font-family: 'e-icons';
}

.e-pv-strikethrough-icon::before {
  content: '\ec18';
  font-family: 'e-icons';
}

.e-pv-copy-icon::before {
  content: '\e70a';
  font-family: 'e-icons';
}

.e-pv-stamp-icon::before {
  content: '\ec28';
}

.e-pv-shape-line-icon::before {
  content: '\e668';
  font-family: 'e-icons';
}

.e-pv-shape-arrow-icon::before {
  content: '\e669';
  font-family: 'e-icons';
}

.e-pv-shape-rectangle-icon::before {
  content: '\e670';
  font-family: 'e-icons';
}

.e-pv-shape-circle-icon::before {
  content: '\e671';
  font-family: 'e-icons';
}

.e-pv-shape-pentagon-icon::before {
  content: '\e672';
  font-family: 'e-icons';
}

.e-pv-annotation-shape-icon::before {
  content: '\ec23';
}

.e-pv-cut-icon::before {
  content: '\e33b';
  font-family: 'e-icons';
}

.e-pv-paste-icon::before {
  content: '\e355';
  font-family: 'e-icons';
}

.e-pv-delete-icon::before {
  content: '\ec1c';
  font-family: 'e-icons';
}

.e-pv-properties-fill-color-icon::before {
  content: '\ec19';
  font-family: 'e-icons';
}

.e-pv-properties-stroke-color-icon::before {
  content: '\e668';
  font-family: 'e-icons';
}

.e-pv-comment-icon::before {
  content: '\e680';
  font-family: 'e-icons';
}

.e-pv-comment-selection-icon::before {
  color: rgba(0, 176, 255, 0.6);
  content: '\e680';
  font-family: 'e-icons';
}

.e-pv-comment-panel-icon::before {
  content: '\eb5d';
  font-family: 'e-icons';
}

.e-pv-accepted-icon::before {
  color: #fff;
  content: '\e682';
  font-family: 'e-icons';
  font-size: 14px;
  padding: 1px 1px 1px 4px;
  position: absolute;
}

.e-pv-rejected-icon::before {
  color: #fff;
  content: '\e683';
  font-family: 'e-icons';
  font-size: 14px;
  padding: 2px 1px 1px 4px;
  position: absolute;
}

.e-pv-completed-icon::before {
  color: #fff;
  content: '\e614';
  font-family: 'e-icons';
  font-size: 14px;
  padding: 2px 1px 1px 3.5px;
  position: absolute;
}

.e-pv-cancelled-icon::before {
  color: #fff;
  content: '\e60a';
  font-family: 'e-icons';
  font-size: 14px;
  padding: 2px 1px 1px 3.5px;
  position: absolute;
}

.e-pv-scale-ratio-icon::before {
  content: '\e678';
  font-family: 'e-icons';
}

.e-pv-calibrate-distance-icon::before {
  content: '\e673';
  font-family: 'e-icons';
}

.e-pv-calibrate-perimeter-icon::before {
  content: '\e674';
  font-family: 'e-icons';
}

.e-pv-calibrate-area-icon::before {
  content: '\e675';
  font-family: 'e-icons';
}

.e-pv-calibrate-radius-icon::before {
  content: '\e676';
  font-family: 'e-icons';
}

.e-pv-calibrate-volume-icon::before {
  content: '\e677';
  font-family: 'e-icons';
}

.e-pv-annotation-stamp-container .e-menu-wrapper ul .e-menu-item .e-caret::before {
  content: '\e969';
}

/*! component layout */
.e-pdfviewer .e-pv-viewer-container {
  border-style: solid;
  border-width: 1px 1px 1px 0;
}

.e-pdfviewer .e-pv-text-layer {
  position: absolute;
  top: 0;
  z-index: 2;
}

.e-pdfviewer .e-pv-annotation-canvas {
  left: 0;
  position: absolute;
  top: 0;
}

.e-pdfviewer .e-pv-text {
  background: transparent;
  color: transparent;
  line-height: normal;
  opacity: 0.6;
  position: absolute;
}

.e-pdfviewer .e-pv-search-text-highlight, .e-pdfviewer .e-pv-search-text-highlightother {
  line-height: normal;
  opacity: 0.6;
  position: absolute;
}

.e-pdfviewer .e-pv-hyperlink {
  z-index: 2;
}

.e-pdfviewer .e-pv-hyperlink.e-pv-onselection {
  z-index: 1;
}

.e-pdfviewer .e-enable-text-selection {
  -moz-user-select: text;
  -ms-user-select: text;
  -webkit-user-select: text;
  user-select: text;
}

.e-pdfviewer .e-disable-text-selection {
  -moz-user-select: none;
  -ms-user-select: none;
  -webkit-user-select: none;
  user-select: none;
}

.e-pdfviewer .e-enable-text-selection .e-pv-text {
  display: inline-block;
}

.e-pdfviewer .e-disable-text-selection .e-pv-text {
  display: none;
}

.e-pdfviewer .e-pv-cursor {
  cursor: text;
}

.e-pdfviewer .e-pv-crosshair-cursor {
  cursor: crosshair;
}

.e-pdfviewer .e-pv-touch-select-drop {
  display: inline-block;
  height: 30px;
  position: absolute;
  width: 30px;
  z-index: 1000;
}

.e-pdfviewer .e-pv-touch-ellipse {
  background-color: #3088ed;
  border-bottom-left-radius: 50%;
  border-bottom-right-radius: 50%;
  border-color: #3088ed;
  border-style: solid;
  border-top-left-radius: 50%;
  border-width: 2px;
  height: 20px;
  margin: 0 0 0 9px;
  position: absolute;
  width: 20px;
}

.e-pdfviewer .e-pv-annotation-note {
  border-radius: 2px;
  cursor: auto;
  font-size: 14px;
  height: auto;
  max-width: 200px;
  min-height: 10px;
  min-width: 30px;
  overflow-wrap: break-word;
  padding: 5px;
  position: absolute;
  width: auto;
  z-index: 10000;
}

.e-pdfviewer .e-pv-annotation-note-author {
  border-bottom: 1px solid #000;
  font-weight: bold;
}

.e-pdfviewer .e-pv-annotation-popup-menu {
  cursor: auto;
  min-width: 396px;
  padding-bottom: 6px;
  position: absolute;
  width: auto;
  z-index: 10000;
}

.e-pdfviewer .e-pv-annotation-popup-header {
  height: auto;
  min-height: 29px;
  padding-top: 14px;
}

.e-pdfviewer .e-pv-annotation-popup-author {
  float: left;
  font-size: 16px;
  font-weight: bold;
  margin-left: 14px;
}

.e-pdfviewer .e-pv-annotation-popup-close {
  float: right;
  height: 20px;
  margin-right: 14px;
  width: 20px;
}

.e-pdfviewer .e-pv-annotation-modified-time {
  height: 14px;
  margin-left: 14px;
  padding-top: 8px;
}

.e-pdfviewer .e-pv-annotation-popup-note-container {
  height: auto;
  padding: 14px;
  width: auto;
}

.e-pdfviewer .e-pv-annotation-popup-content {
  background-color: #fff;
  border: 1px solid;
  font-size: 16px;
  min-height: 132.08px;
  overflow: hidden auto;
  width: 368px;
}

.e-pdfviewer .e-pv-properties-fill-color-icon, .e-pdfviewer .e-pv-properties-stroke-color-icon {
  border-bottom: 2px solid;
}

.e-pv-viewer-container {
  background-color: #303030;
  border-color: #616161;
  height: calc(100% - 56px);
  overflow: auto;
  position: relative;
  -ms-touch-action: pan-x pan-y;
      touch-action: pan-x pan-y;
}

.e-pv-page-container {
  margin: 0;
  padding: 0;
  -ms-touch-action: pan-x pan-y;
      touch-action: pan-x pan-y;
}

.e-pv-mobilespanscroll-container {
  color: rgba(255, 255, 255, 0.87);
  font-family: Roboto-Regular;
  font-size: 14px;
}

.e-pv-annotation-color-container.e-dropdown-btn.e-btn .e-btn-icon:not(.e-caret), .e-pv-annotation-opacity-container.e-dropdown-btn.e-btn .e-btn-icon:not(.e-caret), .e-pv-annotation-stroke-container.e-dropdown-btn.e-btn .e-btn-icon:not(.e-caret), .e-pv-annotation-thickness-container.e-dropdown-btn.e-btn .e-btn-icon:not(.e-caret), .e-pv-annotation-shapes-container.e-dropdown-btn.e-btn .e-btn-icon:not(.e-caret), .e-pv-annotation-calibrate-container.e-dropdown-btn.e-btn .e-btn-icon:not(.e-caret), .e-pv-annotation-stamp-container .e-menu-wrapper ul .e-menu-item .e-menu-icon, .e-bigger .e-pv-icon-search, .e-pv-icon {
  font-size: 16px;
}

.e-pv-mobilescroll-container {
  background-color: #303030;
  border: 3px solid #757575;
  border-radius: 56px 0 0 56px;
  line-height: 27px;
  position: absolute;
  text-align: center;
}

.e-pv-viewer-container .e-pv-page-div {
  border-color: #757575;
  border-style: none;
  border-width: 1px;
  box-shadow: 0 2px 4px 0 rgba(255, 255, 255, 0.21);
  box-sizing: content-box;
  position: absolute;
}

.e-pv-toolbar, .e-pv-nav-toolbar, .e-pv-annotation-toolbar {
  border: 0 solid #616161;
  border-radius: 0;
  border-width: 1px 1px 0 1px;
}

.e-pv-toolbar, .e-pv-annotation-toolbar {
  height: 56px;
}

.e-pv-nav-toolbar, .e-bigger .e-pv-toolbar, .e-pv-toolbar.e-pv-mobile-toolbar, .e-bigger .e-pv-annotation-toolbar, .e-pv-mobile-view .e-pv-annotation-toolbar {
  height: 56px;
}

.e-toolbar.e-pv-toolbar .e-pv-zoom-drop-down-container {
  height: 32px;
  width: 86px;
}

.e-toolbar.e-pv-toolbar .e-toolbar-pop .e-pv-zoom-drop-down-container {
  display: none;
}

.e-pv-toolbar.e-toolbar .e-toolbar-items .e-toolbar-item .e-tbar-btn-text, .e-pv-toolbar.e-toolbar.e-toolpop .e-toolbar-items .e-toolbar-item.e-popup-text .e-tbar-btn-text {
  display: none;
}

.e-pv-current-page-box.e-input-group.e-control-wrapper {
  height: 28px;
  margin-top: 3px;
  width: 46px;
}

.e-pv-mobilepagenoscroll-container {
  background: #303030;
  border: 3px solid #757575;
  border-radius: 10px;
  padding: 6px;
  text-align: center;
  vertical-align: middle;
}

.e-pv-mobilecurrentpage-container {
  color: rgba(255, 255, 255, 0.87);
  display: block;
  font-family: Roboto-Regular;
  font-size: 24px;
}

.e-pv-mobiledashedline-container {
  color: rgba(255, 255, 255, 0.87);
  display: block;
  padding: 10px;
}

.e-pv-number-ofpages {
  width: 10%;
}

.e-pv-gotopage-popup {
  max-width: 500px;
  padding-left: 16px;
  padding-top: 16px;
}

.e-pv-gotopage-apply-btn.e-btn.e-flat.e-primary:disabled {
  background-color: transparent;
}

.e-pv-mobiletotalpage-container {
  color: rgba(255, 255, 255, 0.87);
  display: block;
  font-family: Roboto-Regular;
  font-size: 14px;
}

.e-pv-password-input {
  margin-top: 8px;
}

.e-pv-password-error {
  color: #ff6652;
}

.e-pv-corrupted-popup-header {
  color: #fff;
  opacity: 0.87;
}

.e-pv-corrupted-popup-content {
  color: #fff;
  font-size: 16px;
  line-height: 24px;
  margin-left: 50px;
  opacity: 0.87;
  padding-top: 4.7px;
  text-align: left;
}

.e-pv-corrupted-popup-content-rtl {
  color: #fff;
  font-size: 16px;
  line-height: 24px;
  margin-right: 53px;
  opacity: 0.87;
  padding-top: 4.7px;
  text-align: right;
}

.e-pv-toolbar.e-toolbar:not(.e-pv-mobile-toolbar) .e-toolbar-items .e-toolbar-item:not(.e-separator) {
  height: auto;
  margin-left: 0.5px;
  margin-right: 0.5px;
  padding-bottom: 6px;
  padding-top: 6px;
}

.e-pv-annotation-toolbar.e-toolbar .e-toolbar-items .e-toolbar-item:not(.e-separator) {
  margin-left: 0.5px;
  margin-right: 0.5px;
}

.e-pv-toolbar.e-toolbar .e-toolbar-items .e-toolbar-item.e-separator, .e-pv-annotation-toolbar.e-toolbar .e-toolbar-items .e-toolbar-item.e-separator {
  border-right-width: 1px;
  height: 27px;
  margin-left: 3px;
  margin-right: 3px;
}

.e-pdfviewer .e-pv-toolbar.e-toolbar:not(.e-rtl) .e-toolbar-items .e-toolbar-item:first-child, .e-pdfviewer .e-pv-annotation-toolbar.e-toolbar:not(.e-rtl) .e-toolbar-items .e-toolbar-item:first-child {
  margin-left: 4.5px;
}

.e-pdfviewer .e-pv-toolbar.e-toolbar.e-rtl .e-toolbar-items .e-toolbar-item:first-child, .e-pdfviewer .e-pv-annotation-toolbar.e-toolbar.e-rtl .e-toolbar-items .e-toolbar-item:first-child {
  margin-right: 4.5px;
}

.e-pdfviewer .e-pv-toolbar.e-toolbar:not(.e-rtl) .e-toolbar-items .e-toolbar-item:last-child, .e-pdfviewer .e-pv-annotation-toolbar.e-toolbar:not(.e-rtl) .e-toolbar-items .e-toolbar-item:last-child {
  margin-right: 4.5px;
}

.e-pdfviewer .e-pv-toolbar.e-toolbar.e-rtl .e-toolbar-items .e-toolbar-item:last-child, .e-pdfviewer .e-pv-annotation-toolbar.e-toolbar.e-rtl .e-toolbar-items .e-toolbar-item:last-child {
  margin-left: 4.5px;
}

.e-pv-toolbar.e-toolbar .e-toolbar-items .e-toolbar-item .e-pv-tbar-btn.e-btn, .e-pv-annotation-toolbar.e-toolbar .e-toolbar-items .e-toolbar-item .e-pv-tbar-btn.e-btn {
  height: 36px;
  min-width: 20px;
  width: 36px;
}

.e-pv-open-document-icon {
  font-size: 16px;
  height: 16px;
  line-height: 16px;
  width: 18px;
}

.e-toolbar-items:not(.e-toolbar-pop) .e-pv-download-document-icon {
  line-height: 16px;
}

.e-toolbar-items:not(.e-toolbar-pop) .e-pv-print-document-icon {
  line-height: 16px;
}

.e-pv-first-page-navigation-icon {
  line-height: 14px;
}

.e-pv-previous-page-navigation-icon {
  line-height: 14px;
}

.e-pv-next-page-navigation-icon {
  line-height: 14px;
}

.e-pv-last-page-navigation-icon {
  line-height: 14px;
}

.e-toolbar-items:not(.e-toolbar-pop) .e-pv-zoom-out-icon, .e-toolbar-items:not(.e-toolbar-pop) .e-pv-zoom-in-icon {
  line-height: 16px;
  size: 16px;
}

.e-pv-toolbar.e-toolbar.e-rtl .e-toolbar-items .e-pv-current-page-container {
  padding-left: 8px;
}

.e-pv-toolbar.e-toolbar .e-toolbar-items .e-toolbar-item .e-btn.e-pv-pan-tool .e-pv-icon {
  margin-right: 2px;
}

.e-pv-toolbar.e-toolbar .e-toolbar-items .e-toolbar-item.e-pv-zoom-drop-down-container {
  margin: 0 4px;
  padding: 6px 0;
}

.e-pv-toolbar.e-toolbar .e-toolbar-items .e-pv-total-page-container.e-toolbar-item:not(.e-separator) {
  color: rgba(255, 255, 255, 0.87);
  cursor: default;
  font-size: 13px;
  margin-left: 0.5px;
  margin-right: 9.5px;
  min-width: unset;
  padding-bottom: 6.5px;
  padding-left: 0.5px;
  position: relative;
}

.e-pv-total-page {
  color: #fff;
  font-size: inherit;
}

.e-pv-total-page-ms {
  -ms-transform: translateY(-50%);
  margin: 0;
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
}

.e-pv-zoom-drop-down.e-input-group.e-control-wrapper.e-ddl:not(.e-error) {
  background-color: transparent;
  border: 0;
  height: inherit;
  margin-top: 3px;
  padding: 0;
}

.e-input-group.e-control-wrapper.e-pv-zoom-drop-down.e-ddl.e-input-focus:not(.e-float-icon-left):not(.e-success):not(.e-warning):not(.e-error) {
  background-color: #616161;
  border: 0;
  border-radius: 2px;
}

.e-input-group.e-control-wrapper.e-pv-zoom-drop-down.e-ddl.e-input-focus .e-search-icon.e-ddl-icon {
  color: inherit;
}

.e-input-group.e-control-wrapper.e-pv-zoom-drop-down.e-ddl .e-input.e-control.e-keyboard {
  height: auto;
  margin-top: 0;
  padding-bottom: 5px;
  padding-top: 7px;
}

.e-pv-toolbar:not(.e-rtl) .e-input-group.e-control-wrapper.e-pv-zoom-drop-down.e-ddl .e-input.e-control.e-keyboard {
  padding-left: 14px;
}

.e-pv-toolbar.e-rtl .e-input-group.e-control-wrapper.e-pv-zoom-drop-down.e-ddl .e-input.e-control.e-keyboard {
  padding-right: 14px;
}

.e-input-group.e-control-wrapper.e-pv-zoom-drop-down.e-ddl .e-search-icon.e-ddl-icon {
  background-color: transparent;
  border-left: 0;
  margin-bottom: 4.5px;
  margin-left: 0;
  margin-top: 7.5px;
  padding-right: 15px;
}

.e-input-group.e-control-wrapper.e-pv-zoom-drop-down.e-ddl .e-input.e-control, .e-input-group.e-control-wrapper.e-pv-current-page-box .e-numerictextbox.e-input {
  text-align: center;
}

.e-pv-zoom-drop-down.e-ddl.e-popup.e-popup-open {
  text-align: left;
}

.e-pv-zoom-drop-down.e-popup.e-popup-open .e-dropdownbase .e-list-item.e-active {
  color: #00b0ff;
}

.e-pv-zoom-drop-down.e-popup.e-popup-open {
  min-height: 327px;
  min-width: 110px;
}

.e-input-group.e-control-wrapper.e-pv-current-page-box:not(.e-success):not(.e-warning):not(.e-error):not(.e-float-icon-left) {
  border: 1px solid rgba(255, 255, 255, 0.42);
  border-radius: 2px;
}

.e-pv-password-popup.e-dialog .e-dlg-header, .e-pv-corrupted-popup.e-dialog .e-pv-corrupted-popup-header, .e-pv-notification-popup.e-dialog .e-dlg-header {
  font-size: 20px;
  font-weight: 500;
  letter-spacing: -0.2px;
}

.e-pv-password-popup.e-dialog .e-dlg-header-content .e-btn.e-dlg-closeicon-btn, .e-pv-corrupted-popup.e-dialog .e-dlg-header-content .e-btn.e-dlg-closeicon-btn, .e-pv-notification-popup.e-dialog .e-dlg-header-content .e-btn.e-dlg-closeicon-btn {
  padding-top: 6px;
}

.e-pv-password-popup.e-dialog {
  height: 198px;
  margin: 0 10px;
  max-width: 500px;
}

.e-pv-corrupted-popup.e-dialog {
  height: 180px;
  margin: 0 10px;
  max-width: 409px;
}

.e-pv-corrupted-popup.e-dialog .e-dlg-header-content, .e-pv-notification-popup.e-dialog .e-dlg-header-content {
  border-bottom: 0;
}

.e-pv-corrupted-popup.e-dialog .e-footer-content, .e-pv-notification-popup.e-dialog .e-footer-content {
  border-top: 0;
}

.e-pv-corrupted-popup.e-dialog .e-dlg-content {
  padding-bottom: 15px;
  padding-top: 6.9px;
}

.e-pv-password-popup.e-dialog span[id*='_prompt'] {
  font-size: 16px;
  letter-spacing: -0.16px;
}

.e-pv-password-popup.e-dialog .e-dlg-header-content {
  border-bottom: 0;
}

.e-pv-password-popup.e-dialog .e-footer-content {
  border-top: 0;
}

.e-pv-password-popup.e-dialog .e-dlg-content {
  padding-bottom: 11px;
  padding-top: 6px;
}

.e-pv-current-page-box.e-input-group.e-control-wrapper input.e-input {
  max-height: 28px;
  padding-bottom: 8px;
}

.e-pv-toolbar:not(.e-rtl) .e-pv-current-page-box.e-input-group.e-control-wrapper input.e-input {
  padding-left: 0;
}

.e-pv-toolbar.e-rtl .e-pv-current-page-box.e-input-group.e-control-wrapper input.e-input {
  padding-right: 0;
}

.e-pv-zoom-drop-down.e-ddl.e-control-wrapper.e-icon-anim .e-ddl-icon::before {
  transform: rotate(0deg);
}

.e-input-group.e-control-wrapper.e-pv-current-page-box:not(.e-float-icon-left):not(.e-float-input)::after, .e-input-group.e-control-wrapper.e-pv-current-page-box:not(.e-float-icon-left):not(.e-float-input)::before, .e-input-group.e-control-wrapper.e-pv-current-page-box.e-input-focus:not(.e-float-icon-left):not(.e-float-input)::after, .e-input-group.e-control-wrapper.e-pv-current-page-box.e-input-focus:not(.e-float-icon-left):not(.e-float-input)::before, .e-input-group.e-control-wrapper.e-pv-zoom-drop-down.e-ddl::after, .e-input-group.e-control-wrapper.e-pv-zoom-drop-down.e-ddl::before, .e-input-group.e-control-wrapper.e-pv-zoom-drop-down.e-ddl.e-input-focus::after, .e-input-group.e-control-wrapper.e-pv-zoom-drop-down.e-ddl.e-input-focus::before {
  background: none;
}

.e-pv-sidebar-container {
  background: #212121;
  border-top: 1px solid #616161;
  bottom: 0;
  position: absolute;
  top: 56px;
  transition: transform .3s ease;
  transition-duration: 200ms;
  transition-timing-function: ease;
  width: 250px;
  z-index: 100;
}

.e-pv-main-container {
  height: 100%;
  overflow: hidden;
  position: relative;
  width: 100%;
}

.e-pv-viewer-main-container {
  background-color: #303030;
  bottom: 0;
  left: 0;
  position: absolute;
  right: 0;
  top: 0;
}

.e-pdfviewer:not(.e-pv-mobile-view) .e-pv-viewer-main-container {
  min-width: 400px;
}

.e-pdfviewer:not(.e-pv-mobile-view) .e-pv-main-container {
  min-height: 500px;
}

.e-pv-sidebar-content-container {
  border-top: 1px solid #616161;
  display: none;
  float: left;
  height: calc(100% - 56px);
  position: absolute;
  transition-duration: 200ms;
  transition-timing-function: ease;
  width: 202px;
}

.e-pv-sidebar-content-container.e-thumbnail {
  height: 100%;
  left: 1px;
  position: unset;
}

.e-pv-sidebar-title-container {
  background: #212121;
  height: 50px;
  position: absolute;
  top: 0;
  width: 202px;
}

.e-pv-sidebar-title {
  color: rgba(255, 255, 255, 0.87);
  font-size: 14px;
  height: 16px;
  margin: 0;
  position: absolute;
  text-align: left;
  top: 15px;
}

.e-pv-sidebar-title.e-left {
  left: 8px;
  width: 160px;
}

.e-pv-sidebar-title.e-right {
  right: 8px;
}

.e-pv-sidebar-toolbar {
  background: #212121;
  border: 1px solid #616161;
  border-width: 1px 0 1px 1px;
  bottom: 0;
  height: calc(100% - 57px);
  position: absolute;
  width: 47px;
  z-index: 100;
}

.e-pv-sidebar-content {
  -webkit-overflow-scrolling: touch;
  background: #212121;
  bottom: 0;
  overflow: auto;
  overflow-x: hidden;
  position: absolute;
  top: 48px;
  width: 202px;
  z-index: 103;
}

.e-pv-sidebar-content.e-thumbnail {
  top: 1px;
}

.e-pv-sidebar-resizer {
  background: #303030;
  border: 1px solid #616161;
  border-width: 0;
  color: #fff;
  cursor: ew-resize;
  height: calc(100%);
  position: absolute;
  width: 8px;
  z-index: 105;
}

.e-pv-sidebar-content::-webkit-scrollbar {
  width: 16px;
}

.e-pv-sidebar-content::-webkit-scrollbar-track {
  -webkit-box-shadow: inset 0 0 0 rgba(255, 255, 255, 0.3);
  background: #616161;
  border: 1px solid #616161;
}

.e-pv-sidebar-content::-webkit-scrollbar-thumb {
  -webkit-box-shadow: inset 0 0 0 rgba(255, 255, 255, 0.5);
  background: #9e9e9e;
  background-clip: content-box;
  border: 4px solid transparent;
  border-radius: 100px;
}

.e-pv-thumbnail-view-button.e-btn {
  background: transparent;
  border: none;
  border-radius: none;
  box-shadow: none;
  font-weight: 500;
  height: 32px;
  line-height: none;
  margin: 8px;
  outline: none;
  padding: 10px;
  width: 32px;
}

.e-pv-thumbnail-view-button-selection.e-pv-thumbnail-view-button.e-btn, .e-pv-bookmark-button-selection.e-pv-bookmark-button.e-btn {
  background: transparent;
  border: none;
  border-radius: none;
  line-height: none;
  padding: 10px;
}

.e-pv-bookmark-button.e-btn {
  background: transparent;
  border: none;
  box-shadow: none;
  font-weight: 500;
  height: 34px;
  margin: 8px;
  outline: none;
  padding: 10px;
  width: 34px;
}

.e-pv-title-close-div.e-btn {
  background: transparent;
  border: none;
  box-shadow: none;
  height: 32px;
  left: 170px;
  padding: 0 10px 2px 0;
  position: absolute;
  right: 21px;
  top: 8px;
  width: 32px;
}

.e-pv-sidebar-toolbar-splitter {
  background: #616161;
  bottom: 0;
  height: calc(100% - 56px);
  position: absolute;
  width: 1px;
  z-index: 104;
}

.e-pv-sidebar-content-splitter {
  background: #616161;
  height: 1px;
  position: absolute;
  top: 47px;
  width: 201px;
  z-index: 104;
}

.e-pv-thumbnail-view {
  bottom: 0;
  height: 50%;
  padding: 8px 30px 0;
  position: absolute;
  top: 0;
}

.e-pv-thumbnail {
  height: 140px;
  margin: 0 20px 40px;
  width: 100px;
}

.e-pv-thumbnail-number {
  color: rgba(255, 255, 255, 0.87);
  font-family: Roboto;
  padding-left: 50px;
  padding-right: 50px;
  padding-top: 8px;
}

.e-pv-thumbnail-selection-ring {
  border-radius: 2px;
  box-shadow: 0 0 4px 0 rgba(0, 0, 0, 0.25);
  padding: 6px;
}

.e-pv-thumbnail-image {
  background-clip: content-box;
  background-color: #fff;
  height: 126px;
  width: 86px;
}

.e-pv-thumbnail-hover {
  border: 3px solid #757575;
  border-radius: 2px;
  box-shadow: 0 0 3px 0 rgba(0, 0, 0, 0.25);
  cursor: pointer;
  padding: 5px;
}

.e-pv-thumbnail-focus {
  border: 6px solid rgba(255, 255, 255, 0.3);
  border-radius: 2px;
  box-shadow: 0 0 4px 0 rgba(0, 0, 0, 0.25);
  cursor: pointer;
  padding: 0;
}

.e-pv-thumbnail-selection {
  border: 6px solid rgba(0, 176, 255, 0.7);
  border-radius: 2px;
  box-shadow: 0 0 4px 0 rgba(0, 0, 0, 0.25);
  cursor: pointer;
  padding: 0;
}

.e-pv-thumbnail-row {
  -ms-flex-wrap: wrap;
  display: -ms-flexbox;
  display: flex;
  flex-wrap: wrap;
}

.e-pv-thumbnail-row {
  display: -ms-flexbox;
}

.e-pv-thumbnail-column {
  -ms-flex: 25%;
  flex: 25%;
}

.e-pv-thumbnail-column img {
  display: block;
  margin-left: auto;
  margin-right: auto;
}

.e-pv-bookmark-view {
  height: 100%;
  overflow: auto;
  padding-top: 15px;
  position: relative;
}

div > .e-pv-bookmark-icon.e-pv-icon {
  float: left;
  font-size: 14px;
  padding-left: 6px;
  padding-right: 18px;
}

.e-pv-sidebar-bookmark-title {
  color: #000 87%;
  font-family: Roboto-Regular;
  font-size: 14px;
}

.e-pv-bookmark-view::-webkit-scrollbar {
  height: 16px;
  width: 16px;
}

.e-pv-bookmark-view::-webkit-scrollbar-track {
  -webkit-box-shadow: inset 0 0 0 rgba(255, 255, 255, 0.3);
  background: #616161;
  border: 1px solid #616161;
}

.e-pv-bookmark-view::-webkit-scrollbar-thumb {
  -webkit-box-shadow: inset 0 0 0 rgba(255, 255, 255, 0.5);
  background: #9e9e9e;
  background-clip: content-box;
  border: 4px solid transparent;
  border-radius: 100px;
}

.e-pv-bookmark-view > .e-ul {
  height: inherit;
  overflow: unset;
}

.e-pv-bookmark-view .e-fullrow {
  height: 34px;
}

.e-pv-bookmark-view .e-icon-collapsible::before, .e-pv-bookmark-view .e-icon-expandable::before {
  display: inline-block;
  height: 13px;
  padding: 7px;
  width: 13px;
}

.e-pv-bookmark-view .e-list-item .e-ul {
  padding-left: 32px;
}

.e-pv-bookmark-view .e-ul {
  padding: 0 0 0 16px;
}

.e-pv-text::selection {
  background: #247796;
}

.e-pv-text .e-pv-maintaincontent {
  background: #247796;
}

.e-pv-toolbar .e-pv-tbar-btn.e-pv-tbar-btn, .e-pv-annotation-toolbar .e-pv-tbar-btn.e-pv-tbar-btn, .e-pv-annotation-toolbar .e-dropdown-btn.e-btn, .e-pv-shapes-toolbar .e-pv-tbar-btn, .e-pv-annotation-stamp-container .e-menu-wrapper ul .e-menu-item.e-focused, .e-pv-annotation-stamp-container .e-menu-wrapper ul .e-menu-item .e-menu-icon, .e-pv-annotation-stamp-container .e-menu-wrapper ul .e-menu-item .e-caret, .e-pv-annotation-stamp-container .e-menu-wrapper ul .e-menu-item.e-focused .e-menu-icon, .e-pv-annotation-stamp-container .e-menu-wrapper ul .e-menu-item.e-focused .e-caret {
  color: #fff;
}

.e-pv-toolbar .e-toolbar-item.e-overlay .e-pv-tbar-btn.e-pv-tbar-btn .e-pv-icon, .e-pv-annotation-toolbar .e-toolbar-item.e-overlay .e-pv-tbar-btn.e-pv-tbar-btn .e-pv-icon, .e-pv-annotation-toolbar .e-toolbar-item.e-overlay .e-dropdown-btn.e-btn .e-pv-icon {
  color: #fff;
}

.e-pv-annotation-stamp-container .e-menu-wrapper ul .e-menu-item {
  height: inherit;
}

.e-pv-print-popup-container {
  height: 100%;
  left: 0;
  overflow: auto;
  position: absolute;
  top: 0;
  width: 100%;
  z-index: 2000;
}

.e-pdfviewer .e-pv-print-popup-container .e-spinner-pane.e-spin-center.e-spin-show {
  background-color: rgba(0, 0, 0, 0.3);
}

.e-pv-toolbar.e-toolbar:not(.e-rtl) .e-toolbar-pop .e-toolbar-item .e-tbar-btn.e-btn .e-pv-icon {
  padding: 0 16px 0 0;
}

.e-pv-toolbar.e-toolbar.e-rtl .e-toolbar-pop .e-toolbar-item .e-tbar-btn.e-btn .e-pv-icon {
  padding: 0 0 0 16px;
}

.e-pv-toolbar .e-tbar-btn.e-pv-tbar-btn.e-pv-select, .e-pv-annotation-toolbar .e-tbar-btn.e-pv-tbar-btn.e-pv-select {
  background: #212121;
  border: 0;
  border-radius: 0;
  color: rgba(0, 176, 255, 0.7);
}

.e-pv-sidebar-toolbar .e-pv-tbar-btn {
  color: #fff;
}

.e-pv-search-bar {
  background-color: #212121;
  border: 0 solid #616161;
  border-radius: 2px;
  box-shadow: 0 3px 8px 0 rgba(0, 0, 0, 0.26);
  height: 104px;
  padding: 16px;
  position: absolute;
  top: 50px;
  width: 348px;
  z-index: 100;
}

.e-pv-search-input {
  height: 31px;
  width: 244px;
}

.e-pv-search-input-mobile {
  height: 31px;
}

.e-pv-match-case-container {
  margin-top: 12px;
}

.e-pv-search-btn.e-btn {
  background-color: transparent;
  border: 0;
  box-shadow: none;
  height: 24px;
  margin-top: -6px;
  width: 24px;
}

.e-pv-search-btn.e-btn:active {
  background-color: transparent;
}

.e-pv-search-bar:not(.e-rtl) .e-pv-prev-search {
  margin-left: 16px;
}

.e-pv-search-bar:not(.e-rtl) .e-pv-next-search {
  margin-left: 8px;
}

.e-pv-search-bar.e-rtl .e-pv-prev-search {
  margin-right: 16px;
}

.e-pv-search-bar.e-rtl .e-pv-next-search {
  margin-right: 8px;
}

.e-pv-search-bar.e-rtl .e-pv-prev-search .e-pv-icon-search {
  padding-right: 5px;
}

.e-pv-search-text-highlight {
  background-color: #fdd835;
}

.e-pv-search-text-highlightother {
  background-color: #8b4c12;
}

.e-pv-notification-popup {
  height: 175px;
  margin: 0 10px;
  max-width: 409px;
}

.e-pv-notification-popup.e-pv-notification-large-content {
  height: 190px;
  max-width: 400px;
}

.e-pv-notification-popup .e-pv-notification-popup-content {
  font-size: 16px;
}

.e-pv-search-input.e-input-group .e-input-search-group-icon.e-input-group-icon {
  background: transparent;
}

.e-pv-search-input.e-input-group:not(.e-disabled) .e-input-search-group-icon.e-input-group-icon:active {
  background: transparent;
  color: rgba(0, 0, 0, 0.54);
}

.e-pv-search-input.e-input-group:not(.e-disabled) .e-input-search-group-icon.e-input-group-icon:hover {
  color: rgba(0, 0, 0, 0.54);
}

.e-pv-bookmark-container {
  background-color: #303030;
  border: 1px solid #616161;
}

.e-pv-bookmark-container .e-listview:not(.e-list-template) .e-list-item {
  border-bottom: 1px solid #616161;
  height: 48px;
}

.e-pv-bookmark-container .e-pv-bookmark-view.e-listview .e-ul .e-list-item:last-child {
  border-bottom-width: 0;
}

.e-pv-bookmark-container .e-listview .e-list-text {
  -ms-transform: translateY(-50%);
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
}

.e-pv-nav-toolbar.e-toolbar .e-tbar-btn:active .e-tbar-btn-text {
  color: #fff;
}

.e-pv-nav-toolbar.e-toolbar .e-tbar-btn.e-tbtn-txt {
  background-color: transparent;
  border-width: 0;
  box-shadow: none;
}

.e-pv-bookmark-container .e-pv-bookmark-view {
  padding-top: 0;
}

.e-pv-bookmark-container .e-pv-bookmark-view .e-list-parent.e-ul {
  padding-left: 0;
}

.e-pv-annotation-color-icon, .e-pv-annotation-stroke-icon {
  border-bottom: 2px solid;
}

.e-pv-annotation-opacity-popup-container, .e-pv-annotation-thickness-popup-container {
  box-shadow: 0 3px 8px 0 rgba(0, 0, 0, 0.26);
  height: auto;
  min-height: 48px;
  padding: 16px;
  width: auto;
}

.e-pv-annotation-opacity-label, .e-pv-annotation-opacity-indicator, .e-pv-annotation-thickness-label, .e-pv-annotation-thickness-indicator {
  float: left;
  font-family: inherit;
  font-size: inherit;
}

.e-pv-annotation-opacity-slider-container.e-control-wrapper.e-slider-container.e-horizontal, .e-pv-annotation-thickness-slider-container.e-control-wrapper.e-slider-container.e-horizontal {
  float: left;
  height: auto;
  margin-left: 11px;
  margin-right: 10px;
}

.e-dropdown-popup:not(.e-rtl) .e-pv-annotation-opacity-slider-container.e-control-wrapper.e-slider-container.e-horizontal, .e-dropdown-popup:not(.e-rtl) .e-pv-annotation-thickness-slider-container.e-control-wrapper.e-slider-container.e-horizontal {
  width: 172px;
}

.e-dropdown-popup.e-rtl .e-pv-annotation-opacity-slider-container.e-control-wrapper.e-slider-container.e-horizontal, .e-dropdown-popup.e-rtl .e-pv-annotation-thickness-slider-container.e-control-wrapper.e-slider-container.e-horizontal {
  width: 140px;
}

.e-pv-annotation-opacity-slider-container.e-control-wrapper.e-slider-container .e-pv-annotation-opacity-slider.e-slider, .e-pv-annotation-thickness-slider-container.e-control-wrapper.e-slider-container .e-pv-annotation-opacity-slider.e-slider {
  height: 16px;
}

.e-pv-annotation-color-container.e-dropdown-btn.e-btn .e-caret
.e-pv-annotation-opacity-container.e-dropdown-btn.e-btn .e-caret {
  font-size: 12px;
}

.e-pv-annotation-color-container, .e-pv-annotation-opacity-container, .e-pv-annotation-stroke-container, .e-pv-annotation-thickness-container {
  height: 32px;
  width: 54px;
}

.e-pv-annotation-thickness-container.e-btn.e-icon-btn.e-dropdown-btn {
  padding-top: 8px;
}

.e-pv-shapes-toolbar {
  box-shadow: none;
}

.e-pv-shapes-toolbar.e-toolbar .e-toolbar-items .e-toolbar-item:not(.e-separator) {
  padding: 4px;
}

.e-pv-shapes-toolbar.e-toolbar .e-toolbar-items.e-toolbar-multirow {
  margin-bottom: 0;
  margin-left: 4px;
  margin-right: 4px;
}

.e-pv-shapes-toolbar.e-toolbar .e-toolbar-items .e-toolbar-item .e-tbar-btn.e-btn {
  height: 32px;
  width: 32px;
}

.e-pv-annotation-thickness-slider-container {
  margin-top: -6px;
}

.e-pv-annotation-opacity-icon {
  padding-top: 1px;
}

.e-pv-annotation-color-container.e-btn, .e-pv-annotation-opacity-container.e-btn, .e-pv-annotation-stroke-container.e-btn, .e-pv-annotation-thickness-container.e-btn, .e-pv-annotation-shapes-container.e-btn, .e-pv-annotation-calibrate-container.e-btn, .e-pv-stamp.e-menu-wrapper ul .e-menu-item.e-focused:not(.e-selected) {
  background-color: #212121;
  border-color: transparent;
}

.e-pv-annotation-color-container.e-btn:hover, .e-pv-annotation-opacity-container.e-btn:hover, .e-pv-annotation-stroke-container.e-btn:hover, .e-pv-annotation-thickness-container.e-btn:hover, .e-pv-annotation-shapes-container.e-btn:hover, .e-pv-annotation-calibrate-container.e-btn:hover, .e-pv-stamp.e-menu-wrapper ul .e-menu-item.e-focused:hover {
  background-color: rgba(255, 255, 255, 0.1);
  border-color: transparent;
}

.e-pv-annotation-color-container.e-btn:active, .e-pv-annotation-opacity-container.e-btn:active, .e-pv-annotation-stroke-container.e-btn:active, .e-pv-annotation-thickness-container.e-btn:active, .e-pv-annotation-shapes-container.e-btn:active, .e-pv-annotation-calibrate-container.e-btn:active {
  background-color: #616161;
  border-color: transparent;
  box-shadow: none;
  color: rgba(0, 176, 255, 0.7);
}

.e-pv-annotation-shapes-container.e-btn.e-dropdown-btn.e-active:not(:hover), .e-pv-annotation-calibrate-container.e-btn.e-dropdown-btn.e-active:not(:hover) {
  background-color: #e0e0e0;
}

.e-pv-annotation-color-container.e-dropdown-btn.e-btn, .e-pv-annotation-opacity-container.e-dropdown-btn.e-btn, .e-pv-annotation-stroke-container.e-dropdown-btn.e-btn, .e-pv-annotation-thickness-container.e-dropdown-btn.e-btn {
  line-height: 1.143em;
  padding: 6px 7px 4px;
}

.e-pv-mobile-toolbar .e-toolbar-items .e-toolbar-item .e-btn.e-tbar-btn, .e-pv-nav-toolbar .e-toolbar-items .e-toolbar-item .e-btn.e-tbar-btn {
  height: 36px;
  min-width: 36px;
}

.e-pv-annotation-popup-menu {
  background-color: #212121;
  border-color: #616161;
  color: rgba(255, 255, 255, 0.87);
}

.e-pv-properties-window {
  max-width: 422px;
}

.e-pv-scale-ratio-window {
  max-width: 420px;
}

.e-pv-line-styles-content, .e-pv-line-styles-item {
  border-bottom-color: #000;
}

.e-pv-line-styles-container:hover {
  background-color: #e0e0e0;
}

.e-pv-properties-line-fill-color-container .e-dropdown-btn.e-btn, .e-pv-properties-line-stroke-color-container .e-dropdown-btn.e-btn {
  height: 32px;
  line-height: 16px;
}

.e-pv-properties-window .e-dlg-content {
  padding-top: 6px;
}

.e-pv-scale-ratio-text, .e-pv-depth-text {
  margin-bottom: 8px;
}

.e-pv-properties-line-start-container, .e-pv-properties-line-end-container, .e-pv-properties-line-style-container, .e-pv-properties-line-thickness-container, .e-pv-properties-line-fill-color-container, .e-pv-properties-line-stroke-color-container, .e-pv-properties-line-leader-length-container {
  float: left;
  height: 52px;
  margin-bottom: 16px;
}

.e-pv-properties-line-opacity-container {
  float: left;
  height: auto;
}

.e-pv-scale-ratio-src-input-container, .e-pv-scale-ratio-src-unit-container, .e-pv-scale-ratio-dest-input-container, .e-pv-scale-ratio-dest-unit-container, .e-pv-depth-input-container, .e-pv-depth-unit-container {
  float: left;
  height: 24px;
}

.e-pv-scale-ratio-src-input-container, .e-pv-scale-ratio-src-unit-container, .e-pv-scale-ratio-dest-input-container, .e-pv-scale-ratio-dest-unit-container {
  margin-bottom: 24px;
}

.e-pv-scale-ratio-src-unit-container, .e-pv-scale-ratio-dest-unit-container, .e-pv-depth-unit-container {
  margin-left: 24px;
}

.e-pv-scale-ratio-src-unit.e-btn, .e-pv-scale-ratio-dest-unit.e-btn, .e-pv-depth-unit.e-btn {
  padding: 6px 8px 4px;
}

.e-pv-scale-unit-content {
  float: left;
  width: 33px;
}

.e-pv-scale-ratio-src-unit .e-caret, .e-pv-scale-ratio-dest-unit .e-caret, .e-pv-depth-unit .e-caret {
  margin-left: 96px;
  margin-top: 4px;
}

.e-pv-properties-line-opacity {
  margin-top: -10px;
}

.e-pv-properties-line-start-label, .e-pv-properties-line-end-label, .e-pv-properties-line-style-label, .e-pv-properties-line-thickness-label, .e-pv-properties-line-fill-color-label, .e-pv-properties-line-stroke-color-label, .e-pv-properties-line-opacity-label, .e-pv-properties-line-leader-length-label {
  font-size: 14px;
  margin-bottom: 8px;
}

.e-pv-properties-line-end-container, .e-pv-properties-line-thickness-container {
  margin-left: 24px;
}

.e-pv-properties-line-leader-length-container {
  margin-left: 21px;
}

.e-pv-properties-line-stroke-color-container {
  margin-left: 150px;
  margin-right: 87px;
}

.e-pv-properties-line-start, .e-pv-properties-line-end, .e-pv-properties-line-style, .e-pv-properties-line-thickness, .e-pv-properties-line-opacity, .e-pv-properties-line-leader-length, .e-pv-scale-ratio-src-input, .e-pv-scale-ratio-dest-input, .e-pv-depth-input {
  max-width: 180px;
}

.e-pv-properties-line-start, .e-pv-properties-line-end, .e-pv-properties-line-style, .e-pv-scale-ratio-src-unit, .e-pv-scale-ratio-dest-unit, .e-pv-depth-unit {
  width: 180px;
}

.e-pv-line-styles-container {
  line-height: 0;
  padding: 17px;
}

.e-pv-line-styles-item {
  border-bottom-width: 2px;
  box-sizing: border-box;
  display: inline-block;
  width: 100%;
}

.e-pv-line-styles-content {
  border-bottom-width: 2px;
  display: inline-block;
  width: 100%;
}

.e-pv-properties-line-style-content {
  float: left;
  line-height: 16px;
  width: 44px;
}

.e-pv-properties-line-start.e-btn, .e-pv-properties-line-end.e-btn {
  height: 32px;
  padding: 8px;
}

.e-pv-properties-line-style.e-btn {
  height: 32px;
  line-height: 0;
  padding: 8px;
  white-space: pre;
}

.e-pv-properties-line-start.e-btn .e-caret, .e-pv-properties-line-end.e-btn .e-caret {
  display: inline;
  line-height: 16px;
  margin-left: 95px;
}

.e-pv-line-styles-content-container {
  float: left;
  font-size: 0;
  margin-right: 7.5px;
  margin-top: 2.5px;
  width: 138px;
}

.e-pv-annotation-shapes-container.e-btn.e-icon-btn, .e-pv-annotation-calibrate-container.e-btn.e-icon-btn, .e-menu-wrapper.e-custom-scroll.e-lib.e-keyboard.e-pv-stamp ul {
  height: 32px;
  width: 72px;
}

.e-pv-annotation-shapes-container.e-btn.e-icon-btn, .e-pv-annotation-calibrate-container.e-btn.e-icon-btn {
  padding-top: 1px;
}

.e-pv-annotation-shape-icon, .e-pv-annotation-calibrate-icon {
  margin-right: 10px;
}

.e-pv-properties-opacity-indicator {
  margin-left: 153px;
}

.e-pv-annotation-stamp-container .e-menu-parent.e-menu {
  border-radius: 2px;
}

.e-pv-annotation-shapes-container.e-btn.e-icon-btn, .e-pv-annotation-calibrate-container.e-btn.e-icon-btn, .e-pv-annotation-stamp-container .e-menu-parent.e-menu .e-menu-item, .e-pv-annotation-stamp-container .e-menu-parent.e-menu .e-menu-item .e-pv-stamp-icon, .e-pv-annotation-stamp-container .e-menu-parent.e-menu .e-menu-item .e-caret {
  line-height: 32px;
}

.e-bigger .e-pv-toolbar.e-toolbar .e-toolbar-items .e-toolbar-item .e-tbar-btn.e-btn, .e-bigger .e-pv-annotation-toolbar.e-toolbar .e-toolbar-items .e-toolbar-item .e-pv-tbar-btn.e-btn {
  height: 36px;
  line-height: 25px;
  margin: 4px 0;
  padding: 0 1.5px;
  width: 36px;
}

.e-bigger .e-pv-current-page-box.e-input-group.e-control-wrapper input.e-input {
  padding-top: 4px;
}

.e-bigger .e-toolbar .e-toolbar-items .e-toolbar-item.e-pv-zoom-drop-down-container {
  width: 93px;
}

.e-bigger .e-pv-current-page-box.e-input-group.e-control-wrapper input.e-input:focus {
  padding-left: 0;
}

.e-bigger .e-pv-password-popup.e-dialog, .e-pv-mobile-view .e-pv-password-popup.e-dialog {
  height: 240px;
}

.e-bigger .e-pv-notification-popup.e-dialog, .e-pv-mobile-view .e-pv-notification-popup.e-dialog {
  height: 195px;
}

.e-bigger .e-pv-notification-popup.e-pv-notification-large-content.e-dialog, .e-pv-mobile-view .e-pv-notification-popup.e-pv-notification-large-content.e-dialog {
  height: 220px;
}

.e-bigger .e-pv-corrupted-popup.e-dialog, .e-pv-mobile-view .e-pv-corrupted-popup.e-dialog {
  height: 198px;
}

.e-bigger .e-pv-password-popup.e-dialog .e-dlg-header-content .e-btn.e-dlg-closeicon-btn, .e-bigger .e-pv-corrupted-popup.e-dialog .e-dlg-header-content .e-btn.e-dlg-closeicon-btn, .e-pv-mobile-view .e-pv-password-popup.e-dialog .e-dlg-header-content .e-btn.e-dlg-closeicon-btn, .e-pv-mobile-view .e-pv-corrupted-popup.e-dialog .e-dlg-header-content .e-btn.e-dlg-closeicon-btn {
  padding-top: 6px;
}

.e-bigger .e-toolbar.e-pv-toolbar .e-toolbar-items.e-tbar-pos .e-toolbar-left, .e-bigger .e-toolbar.e-pv-toolbar .e-toolbar-items.e-tbar-pos .e-toolbar-right {
  top: 0;
}

.e-bigger .e-pv-toolbar.e-rtl .e-pv-current-page-box.e-input-group.e-control-wrapper input.e-input {
  padding-right: 0;
}

.e-bigger .e-pv-toolbar.e-rtl .e-input-group.e-control-wrapper.e-pv-zoom-drop-down.e-ddl input.e-input.e-control.e-keyboard {
  padding-left: 0;
  padding-right: 14px;
}

.e-pv-sidebar-content-container.e-left {
  left: 47px;
}

.e-pv-sidebar-content-container.e-right {
  right: 47px;
}

.e-pv-sidebar-toolbar-splitter.e-left {
  left: 47px;
}

.e-pv-sidebar-toolbar-splitter.e-right {
  right: 47px;
}

.e-pv-sidebar-resizer.e-left {
  left: 202px;
}

.e-pv-sidebar-resizer.e-right {
  right: 202px;
}

.e-pdfviewer .e-checkbox-wrapper .e-frame + .e-label, .e-pdfviewer .e-css.e-checkbox-wrapper .e-frame + .e-label {
  margin-right: 10px;
}

div > .e-pv-bookmark-icon.e-pv-icon.e-right {
  float: left;
  font-size: 14px;
  padding-left: 1px;
  padding-right: 1px;
  position: absolute;
}

.e-bigger .e-pv-properties-window {
  max-width: 435px;
}

.e-bigger .e-pv-scale-ratio-window {
  max-width: 435px;
}

.e-pdfviewer .e-treeview .e-list-item {
  white-space: pre-wrap;
  word-break: break-word;
}

.e-pdfviewer .e-treeview .e-list-text {
  width: 100%;
}

.e-pdfviewer .e-treeview.e-fullrow-wrap .e-icon-collapsible, .e-pdfviewer .e-treeview.e-fullrow-wrap .e-icon-expandable, .e-pdfviewer .e-treeview.e-fullrow-wrap .e-input, .e-pdfviewer .e-treeview.e-fullrow-wrap .e-list-url {
  margin-top: 3px;
  position: absolute;
}

.e-pdfviewer .e-inplaceeditor .e-editable-value-wrapper.e-hide {
  display: none;
}

.e-pdfviewer .e-editable-inline {
  padding: 7px 8px 42px;
}

.e-pv-comment-textbox {
  min-height: 22px;
}

.e-pdfviewer .e-editable-value-wrapper {
  color: rgba(255, 255, 255, 0.9);
  font-size: 14px;
  line-height: normal;
  min-height: 22px;
  padding: 2px 8px 3px;
}

.e-pdfviewer .e-pv-comment-textbox .e-editable-value-wrapper {
  color: rgba(255, 255, 255, 0.9);
  font-size: 14px;
  line-height: normal;
  min-height: 24px;
  padding: 2px 8px 3px 32px;
}

.e-pdfviewer .e-inplaceeditor .e-editable-action-buttons {
  float: right;
  padding-bottom: 7px;
  padding-top: 5px;
  position: relative;
  right: 8px;
}

.e-pdfviewer .e-pv-reply-div .e-inplaceeditor .e-editable-action-buttons {
  float: right;
  padding-bottom: 7px;
  padding-top: 5px;
  position: relative;
  right: 8px;
}

.e-pv-comment-title-container {
  height: 36px;
}

.e-pv-reply-title-container {
  height: 36px;
}

.e-pv-more-options-button.e-btn {
  background: transparent;
  border: none;
  border-radius: none;
  box-shadow: none;
  float: right;
  padding: 8px 7px 1px 1px;
}

.e-pdfviewer .e-editable-inline .e-btn.e-outline.e-primary, .e-pdfviewer .e-editable-inline .e-btn.e-outline {
  border-color: transparent;
}

.e-pdfviewer .e-editable-inline .e-editable-form {
  margin-bottom: 1px;
}

.e-pdfviewer .e-editable-inline .e-clear-icon {
  padding-right: 2px;
}

.e-pdfviewer .e-btn-cancel {
  font-size: 14px;
  padding-left: 5px;
  padding-right: 5px;
}

.e-pdfviewer .e-btn-save {
  font-size: 14px;
  margin-right: 12px;
  padding-left: 7px;
  padding-right: 7px;
}

.e-pdfviewer .e-btn-save.e-outline.e-primary:disabled {
  background-color: rgba(255, 255, 255, 0.2);
  border-radius: 2px;
  color: rgba(255, 255, 255, 0.6);
}

.e-pdfviewer .e-accordion .e-acrdn-item .e-acrdn-panel .e-acrdn-content {
  box-shadow: 0 0 0 8px #212121 inset;
  padding: 8px;
}

.e-pdfviewer .e-accordion .e-acrdn-item .e-acrdn-header {
  line-height: 32px;
  min-height: 32px;
}

.e-pv-comment-panel {
  background-color: #212121;
  border: 1px solid #616161;
  border-width: 1px 1px 0;
  opacity: 1;
  overflow: hidden;
  position: absolute;
  top: 57px;
  width: 300px;
  z-index: 100;
}

.e-pv-comment-panel-title-container {
  background-color: #212121;
  border-color: #616161;
  border-style: double;
  border-width: 0 1px 1px 0;
  height: 45px;
  position: relative;
}

.e-pv-comment-panel-title {
  color: rgba(255, 255, 255, 0.9);
  font-size: 14px;
  left: 15px;
  position: absolute;
  text-align: left;
  top: 14px;
}

.e-pv-comment-panel-title-close-div.e-btn {
  background: transparent;
  border: none;
  box-shadow: none;
  height: 32px;
  position: absolute;
  right: 11px;
  top: 5px;
  width: 32px;
}

.e-pv-title-close-icon {
  color: rgba(255, 255, 255, 0.6);
}

.e-pv-comments-panel-text {
  font-size: 15px;
  padding-left: 85px;
}

.e-pv-comments-content-container {
  background-color: #212121;
  border-color: #616161;
  border-style: double;
  border-width: 0 1px 1px 0;
  height: calc(100% - 45px);
  overflow: auto;
}

.e-pv-comments-container {
  background-color: #212121;
  border-radius: 2px;
  left: 7px;
  right: 7px;
}

.e-pv-comments-border {
  border: 2px;
  border-color: rgba(255, 128, 171, 0.6);
  border-radius: 4px;
  border-style: groove;
}

.e-pv-comment-title {
  color: rgba(255, 255, 255, 0.6);
  font-size: 14px;
  padding-left: 32px;
  padding-top: 7px;
  position: absolute;
}

.e-pv-reply-title {
  color: rgba(255, 255, 255, 0.6);
  font-size: 14px;
  padding-left: 8px;
  padding-top: 7px;
  position: absolute;
}

.e-pv-comments-hover {
  background-color: rgba(255, 255, 255, 0.2);
}

.e-pv-comments-select {
  background-color: #212121;
}

.e-pv-comments-select .e-pv-comment-textbox .e-editable-value-wrapper {
  color: rgba(255, 128, 171, 0.9);
}

.e-pv-comments-leave {
  background-color: #212121;
}

.e-pdfviewer .e-accordion {
  background-color: #212121;
  border: transparent;
}

.e-menu-wrapper.e-custom-scroll.e-lib.e-keyboard.e-pv-stamp {
  border: 0;
  display: block;
}

.e-menu-icon.e-pv-stamp-icon.e-pv-icon {
  padding-left: 7px;
}

.e-pdfviewer .e-inplaceeditor {
  display: block;
}

.e-pdfviewer .e-inplaceeditor .e-editable-value-wrapper .e-editable-overlay-icon {
  display: none;
}

.e-pdfviewer .e-inplaceeditor .e-editable-value-wrapper .e-editable-value {
  border-bottom: 0;
  word-break: break-all;
}

.e-pdfviewer .e-inplaceeditor .e-editable-value-wrapper {
  display: block;
}

.e-pdfviewer .e-inplaceeditor .e-editable-value-wrapper:hover {
  background: transparent;
}

.e-pv-status-div {
  height: 20px;
  left: 8px;
  position: relative;
  width: 20px;
}

.e-pv-status-container {
  padding-bottom: 5px;
  padding-top: 2px;
}

.e-pdfviewer .e-input-group.e-control-wrapper.e-editable-elements.e-input-focus {
  caret-color: #ff80ab;
}

.e-pv-reply-div {
  margin-top: 3px;
}

.e-pdfviewer .e-pv-accordion-container.e-accordion .e-acrdn-item.e-select.e-selected.e-expand-state > .e-acrdn-header, .e-pdfviewer .e-pv-accordion-container.e-accordion .e-acrdn-item.e-select.e-expand-state > .e-acrdn-header {
  background: #212121;
  border: #616161;
}

.e-pdfviewer .e-pv-accordion-container.e-accordion .e-acrdn-item.e-select.e-selected.e-expand-state > .e-acrdn-header .e-acrdn-header-icon, .e-pdfviewer .e-pv-accordion-container.e-accordion .e-acrdn-item.e-select.e-selected.e-expand-state > .e-acrdn-header .e-acrdn-header-content, .e-pdfviewer .e-pv-accordion-container.e-accordion .e-acrdn-item.e-select.e-expand-state > .e-acrdn-header .e-acrdn-header-icon, .e-pdfviewer .e-pv-accordion-container.e-accordion .e-acrdn-item.e-select.e-expand-state > .e-acrdn-header .e-acrdn-header-content {
  color: rgba(255, 255, 255, 0.6);
  font-size: 14px;
}

.e-content-placeholder.e-pdfviewer.e-placeholder-pdfviewer {
  background-size: 100%;
}

.e-pdfviewer .e-accordion .e-acrdn-item .e-acrdn-panel .e-acrdn-content {
  line-height: normal;
}

.e-control .e-js [class^='e-'], .e-control .e-js [class*=' e-'] {
  box-sizing: content-box;
}
