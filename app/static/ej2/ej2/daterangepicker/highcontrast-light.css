/*! component icons */
.e-input-group-icon.e-range-icon,
.e-control-wrapper .e-input-group-icon.e-range-icon {
  font-size: 16px;
  margin: 0;
  outline: none;
}

.e-input-group-icon.e-range-icon::before,
.e-control-wrapper .e-input-group-icon.e-range-icon::before {
  content: '\e244';
  font-family: 'e-icons';
}

.e-input-group-icon.e-range-icon:focus,
.e-control-wrapper .e-input-group-icon.e-range-icon:focus {
  background: #ecf;
  border-radius: 0;
}

.e-daterangepicker .e-calendar .e-header .e-date-icon-next::before {
  content: '\e85c';
}

.e-daterangepicker .e-calendar .e-header .e-date-icon-prev::before {
  content: '\e98f';
}

.e-daterangepicker .e-change-icon::before {
  content: '\e85f';
}

.e-daterangepicker .e-calendar.e-rtl .e-header .e-date-icon-next::before {
  content: '\e98f';
}

.e-daterangepicker .e-calendar.e-rtl .e-header .e-date-icon-prev::before {
  content: '\e85c';
}

.e-daterangepicker.e-rtl .e-start-end .e-change-icon::before {
  content: '\e85b';
}

.e-bigger .e-input-group-icon.e-range-icon,
.e-bigger.e-control-wrapper .e-input-group-icon.e-range-icon,
.e-bigger .e-control-wrapper .e-input-group-icon.e-range-icon {
  font-size: 20px;
  margin: 0;
  outline: none;
}

.e-small .e-input-group-icon.e-range-icon,
.e-control-wrapper.e-small .e-input-group-icon.e-range-icon,
.e-small .e-control-wrapper .e-input-group-icon.e-range-icon {
  font-size: 14px;
}

.e-small.e-bigger .e-input-group-icon.e-range-icon,
.e-control-wrapper.e-small.e-bigger .e-input-group-icon.e-range-icon,
.e-small.e-bigger .e-control-wrapper .e-input-group-icon.e-range-icon {
  font-size: 18px;
}

/*! daterangepicker layout */
.e-input-group.e-control-wrapper.e-date-range-wrapper.e-non-edit.e-input-focus .e-input:focus ~ .e-clear-icon,
.e-float-input.e-control-wrapper.e-input-group.e-date-range-wrapper.e-non-edit.e-input-focus input:focus ~ .e-clear-icon {
  display: -ms-flexbox;
  display: flex;
}

.e-daterangepicker.e-popup,
.e-bigger.e-small .e-daterangepicker.e-popup {
  border: 1px solid #000;
  border-radius: 0;
  box-shadow: none;
  max-height: 500px;
  max-width: 730px;
}

.e-daterangepicker.e-popup.e-daterange-day-header-lg,
.e-bigger.e-small .e-daterangepicker.e-popup.e-daterange-day-header-lg {
  max-width: 100%;
}

.e-daterangepicker.e-popup.e-preset-wrapper,
.e-bigger.e-small .e-daterangepicker.e-popup.e-preset-wrapper {
  min-width: 580px;
}

.e-daterangepicker.e-popup.e-preset-wrapper .e-presets,
.e-bigger.e-small .e-daterangepicker.e-popup.e-preset-wrapper .e-presets {
  max-height: none;
}

.e-daterangepicker.e-popup .e-range-header,
.e-bigger.e-small .e-daterangepicker.e-popup .e-range-header {
  margin: 12px 12px 0 12px;
  width: auto;
}

.e-daterangepicker.e-popup .e-range-header .e-start-label,
.e-daterangepicker.e-popup .e-range-header .e-end-label,
.e-bigger.e-small .e-daterangepicker.e-popup .e-range-header .e-start-label,
.e-bigger.e-small .e-daterangepicker.e-popup .e-range-header .e-end-label {
  cursor: default;
  display: inline-block;
  font-size: 18px;
  overflow: hidden;
  text-align: center;
  text-decoration: none;
  text-overflow: ellipsis;
  -webkit-user-select: none;
     -moz-user-select: none;
      -ms-user-select: none;
          user-select: none;
  white-space: nowrap;
  width: 48%;
}

.e-daterangepicker.e-popup .e-range-header .e-change-icon,
.e-bigger.e-small .e-daterangepicker.e-popup .e-range-header .e-change-icon {
  font-size: 14px;
  font-weight: 300;
  text-align: center;
  width: 4%;
}

.e-daterangepicker.e-popup .e-range-header .e-day-span,
.e-bigger.e-small .e-daterangepicker.e-popup .e-range-header .e-day-span {
  direction: ltr;
  font-size: 14px;
  height: 14px;
  margin: 0 0 15px 0;
  text-align: center;
  -webkit-user-select: none;
     -moz-user-select: none;
      -ms-user-select: none;
          user-select: none;
  width: 100%;
}

.e-daterangepicker.e-popup .e-range-header .e-start-end,
.e-bigger.e-small .e-daterangepicker.e-popup .e-range-header .e-start-end {
  -ms-flex-align: center;
      align-items: center;
  display: -ms-flexbox;
  display: flex;
  height: 35px;
}

.e-daterangepicker.e-popup .e-separator,
.e-bigger.e-small .e-daterangepicker.e-popup .e-separator {
  height: 1px;
  margin: 0 16px;
}

.e-daterangepicker.e-popup .e-calendar,
.e-bigger.e-small .e-daterangepicker.e-popup .e-calendar {
  border: none;
  margin: 0;
  padding: 0;
}

.e-daterangepicker.e-popup .e-calendar .e-content table,
.e-bigger.e-small .e-daterangepicker.e-popup .e-calendar .e-content table {
  padding: 0 11.5px 10px;
}

.e-daterangepicker.e-popup .e-calendar .e-header .e-title,
.e-bigger.e-small .e-daterangepicker.e-popup .e-calendar .e-header .e-title {
  cursor: pointer;
  float: none;
  font-weight: normal;
  line-height: 36px;
  margin-left: 0;
  width: auto;
}

.e-daterangepicker.e-popup .e-calendar .e-header.e-month, .e-daterangepicker.e-popup .e-calendar .e-header.e-year, .e-daterangepicker.e-popup .e-calendar .e-header.e-decade,
.e-bigger.e-small .e-daterangepicker.e-popup .e-calendar .e-header.e-month,
.e-bigger.e-small .e-daterangepicker.e-popup .e-calendar .e-header.e-year,
.e-bigger.e-small .e-daterangepicker.e-popup .e-calendar .e-header.e-decade {
  padding: 10px 12px 0 12px;
}

.e-daterangepicker.e-popup .e-calendar .e-header .e-next,
.e-bigger.e-small .e-daterangepicker.e-popup .e-calendar .e-header .e-next {
  float: right;
}

.e-daterangepicker.e-popup .e-calendar .e-header .e-prev,
.e-bigger.e-small .e-daterangepicker.e-popup .e-calendar .e-header .e-prev {
  float: left;
}

.e-daterangepicker.e-popup .e-calendar .e-header .e-next,
.e-daterangepicker.e-popup .e-calendar .e-header .e-prev,
.e-bigger.e-small .e-daterangepicker.e-popup .e-calendar .e-header .e-next,
.e-bigger.e-small .e-daterangepicker.e-popup .e-calendar .e-header .e-prev {
  height: 36px;
  width: 36px;
}

.e-daterangepicker.e-popup .e-calendar .e-header .e-next span,
.e-daterangepicker.e-popup .e-calendar .e-header .e-prev span,
.e-bigger.e-small .e-daterangepicker.e-popup .e-calendar .e-header .e-next span,
.e-bigger.e-small .e-daterangepicker.e-popup .e-calendar .e-header .e-prev span {
  padding: 9px 10px;
}

.e-daterangepicker.e-popup .e-calendar .e-start-date.e-selected.e-range-hover,
.e-bigger.e-small .e-daterangepicker.e-popup .e-calendar .e-start-date.e-selected.e-range-hover {
  border-radius: 50% 0 0 50%;
}

.e-daterangepicker.e-popup .e-calendar .e-end-date.e-selected.e-range-hover,
.e-bigger.e-small .e-daterangepicker.e-popup .e-calendar .e-end-date.e-selected.e-range-hover {
  border-radius: 0;
}

.e-daterangepicker.e-popup .e-calendar .e-end-date.e-selected.e-range-hover span.e-day,
.e-daterangepicker.e-popup .e-calendar .e-start-date.e-selected.e-range-hover span.e-day,
.e-bigger.e-small .e-daterangepicker.e-popup .e-calendar .e-end-date.e-selected.e-range-hover span.e-day,
.e-bigger.e-small .e-daterangepicker.e-popup .e-calendar .e-start-date.e-selected.e-range-hover span.e-day {
  border: 1px solid #160028;
}

.e-daterangepicker.e-popup .e-footer,
.e-bigger.e-small .e-daterangepicker.e-popup .e-footer {
  -ms-flex-align: center;
      align-items: center;
  border-top: 1px solid #757575;
  clear: both;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-direction: row-reverse;
      flex-direction: row-reverse;
  height: 48px;
}

.e-daterangepicker.e-popup .e-footer .e-btn,
.e-bigger.e-small .e-daterangepicker.e-popup .e-footer .e-btn {
  font-weight: 500;
  height: 32px;
  line-height: 30px;
  overflow: hidden;
  padding: 0 16px;
  text-overflow: ellipsis;
}

.e-daterangepicker.e-popup .e-footer .e-btn.e-apply,
.e-bigger.e-small .e-daterangepicker.e-popup .e-footer .e-btn.e-apply {
  margin: 0 10px 0 5px;
}

.e-daterangepicker.e-popup .e-date-range-container,
.e-bigger.e-small .e-daterangepicker.e-popup .e-date-range-container {
  float: left;
}

.e-daterangepicker.e-popup .e-date-range-container.e-range-border,
.e-bigger.e-small .e-daterangepicker.e-popup .e-date-range-container.e-range-border {
  border-right: 1px solid #757575;
}

.e-daterangepicker.e-popup .e-calendar-container,
.e-bigger.e-small .e-daterangepicker.e-popup .e-calendar-container {
  display: -ms-flexbox;
  display: flex;
}

.e-daterangepicker.e-popup .e-calendar-container .e-left-container,
.e-daterangepicker.e-popup .e-calendar-container .e-right-container,
.e-bigger.e-small .e-daterangepicker.e-popup .e-calendar-container .e-left-container,
.e-bigger.e-small .e-daterangepicker.e-popup .e-calendar-container .e-right-container {
  float: left;
}

.e-daterangepicker.e-popup .e-calendar-container .e-left-container,
.e-bigger.e-small .e-daterangepicker.e-popup .e-calendar-container .e-left-container {
  border-right: 1px solid #757575;
}

.e-daterangepicker.e-popup .e-presets,
.e-bigger.e-small .e-daterangepicker.e-popup .e-presets {
  max-height: 216px;
  overflow: auto;
  width: auto;
}

.e-daterangepicker.e-popup .e-presets .e-list-item,
.e-bigger.e-small .e-daterangepicker.e-popup .e-presets .e-list-item {
  border-radius: 0;
  cursor: pointer;
  line-height: 36px;
  overflow: hidden;
  padding: 0 10px;
  white-space: nowrap;
}

.e-daterangepicker.e-popup .e-presets .e-list-parent,
.e-bigger.e-small .e-daterangepicker.e-popup .e-presets .e-list-parent {
  margin: 0;
  max-width: 160px;
  padding: 0;
}

.e-daterangepicker.e-popup .e-presets .e-text-content,
.e-bigger.e-small .e-daterangepicker.e-popup .e-presets .e-text-content {
  line-height: 47px;
}

.e-daterangepicker.e-popup .e-presets .e-ul li.e-list-item,
.e-bigger.e-small .e-daterangepicker.e-popup .e-presets .e-ul li.e-list-item {
  font-size: 14px;
  height: 36px;
  line-height: 36px;
}

.e-daterangepicker.e-popup .e-hide-range,
.e-bigger.e-small .e-daterangepicker.e-popup .e-hide-range {
  display: none;
}

.e-daterangepicker.e-rtl .e-date-range-container,
.e-bigger.e-small .e-daterangepicker.e-rtl .e-date-range-container {
  float: right;
}

.e-daterangepicker.e-rtl .e-date-range-container.e-range-border,
.e-bigger.e-small .e-daterangepicker.e-rtl .e-date-range-container.e-range-border {
  border-left: 1px solid #757575;
  border-right: 0;
}

.e-daterangepicker.e-rtl .e-date-range-container .e-left-container,
.e-bigger.e-small .e-daterangepicker.e-rtl .e-date-range-container .e-left-container {
  border-left: 1px solid #757575;
  border-right: 0;
}

.e-daterangepicker.e-rtl .e-date-range-container .e-calendar .e-next,
.e-bigger.e-small .e-daterangepicker.e-rtl .e-date-range-container .e-calendar .e-next {
  float: left;
}

.e-daterangepicker.e-rtl .e-date-range-container .e-calendar .e-prev,
.e-bigger.e-small .e-daterangepicker.e-rtl .e-date-range-container .e-calendar .e-prev {
  float: right;
}

.e-daterangepicker.e-rtl .e-date-range-container .e-calendar .e-start-date.e-selected.e-range-hover,
.e-bigger.e-small .e-daterangepicker.e-rtl .e-date-range-container .e-calendar .e-start-date.e-selected.e-range-hover {
  border-radius: 0;
}

.e-daterangepicker.e-rtl .e-date-range-container .e-calendar .e-end-date.e-selected.e-range-hover,
.e-bigger.e-small .e-daterangepicker.e-rtl .e-date-range-container .e-calendar .e-end-date.e-selected.e-range-hover {
  border-radius: 50% 0 0 50%;
}

.e-daterangepicker.e-rtl .e-footer,
.e-bigger.e-small .e-daterangepicker.e-rtl .e-footer {
  -ms-flex-direction: row;
      flex-direction: row;
  -ms-flex-pack: end;
      justify-content: flex-end;
}

.e-daterangepicker.e-rtl .e-footer .e-btn.e-cancel,
.e-bigger.e-small .e-daterangepicker.e-rtl .e-footer .e-btn.e-cancel {
  margin: 0 5px 0 10px;
}

.e-daterangepicker.e-rtl .e-footer .e-btn.e-apply,
.e-bigger.e-small .e-daterangepicker.e-rtl .e-footer .e-btn.e-apply {
  margin-left: 0;
}

.e-bigger .e-daterangepicker.e-range-modal,
.e-device.e-daterangepicker.e-range-modal {
  background-color: rgba(255, 255, 255, 0.6);
  height: 100%;
  left: 0;
  opacity: .5;
  pointer-events: auto;
  position: fixed;
  top: 0;
  width: 100%;
  z-index: 999;
}

.e-bigger.e-small .e-daterangepicker .e-calendar {
  max-width: 232px;
}

.e-bigger .e-daterangepicker.e-popup.e-preset-wrapper,
.e-bigger.e-daterangepicker.e-popup.e-preset-wrapper,
.e-device.e-daterangepicker.e-popup.e-preset-wrapper {
  max-width: 770px;
  min-width: auto;
}

.e-bigger .e-daterangepicker.e-popup.e-preset-wrapper .e-presets,
.e-bigger.e-daterangepicker.e-popup.e-preset-wrapper .e-presets,
.e-device.e-daterangepicker.e-popup.e-preset-wrapper .e-presets {
  max-height: none;
}

.e-bigger .e-daterangepicker.e-popup .e-range-header,
.e-bigger.e-daterangepicker.e-popup .e-range-header,
.e-device.e-daterangepicker.e-popup .e-range-header {
  width: auto;
}

.e-bigger .e-daterangepicker.e-popup .e-range-header .e-start-end,
.e-bigger.e-daterangepicker.e-popup .e-range-header .e-start-end,
.e-device.e-daterangepicker.e-popup .e-range-header .e-start-end {
  -ms-flex-align: center;
      align-items: center;
  cursor: pointer;
  display: -ms-flexbox;
  display: flex;
  height: 36px;
  -ms-flex-pack: center;
      justify-content: center;
  -webkit-user-select: none;
     -moz-user-select: none;
      -ms-user-select: none;
          user-select: none;
  width: 100%;
}

.e-bigger .e-daterangepicker.e-popup .e-range-header .e-start-end .e-start-btn,
.e-bigger .e-daterangepicker.e-popup .e-range-header .e-start-end .e-end-btn,
.e-bigger.e-daterangepicker.e-popup .e-range-header .e-start-end .e-start-btn,
.e-bigger.e-daterangepicker.e-popup .e-range-header .e-start-end .e-end-btn,
.e-device.e-daterangepicker.e-popup .e-range-header .e-start-end .e-start-btn,
.e-device.e-daterangepicker.e-popup .e-range-header .e-start-end .e-end-btn {
  border: 1px solid #000;
  box-shadow: none;
  font-size: 16px;
  font-weight: normal;
  height: 32px;
  line-height: 30px;
  max-width: 116px;
  overflow: hidden;
  padding: 1px 6px;
  text-overflow: ellipsis;
  width: 50%;
}

.e-bigger .e-daterangepicker.e-popup .e-range-header .e-start-end .e-end-btn,
.e-bigger.e-daterangepicker.e-popup .e-range-header .e-start-end .e-end-btn,
.e-device.e-daterangepicker.e-popup .e-range-header .e-start-end .e-end-btn {
  border-left: 0;
  border-radius: 0 2px 2px 0;
}

.e-bigger .e-daterangepicker.e-popup .e-range-header .e-start-end .e-start-btn,
.e-bigger.e-daterangepicker.e-popup .e-range-header .e-start-end .e-start-btn,
.e-device.e-daterangepicker.e-popup .e-range-header .e-start-end .e-start-btn {
  border-radius: 2px 0 0 2px;
}

.e-bigger .e-daterangepicker.e-popup .e-range-header .e-start-end .e-start-btn:hover,
.e-bigger .e-daterangepicker.e-popup .e-range-header .e-start-end .e-end-btn:hover:not([disabled]),
.e-bigger.e-daterangepicker.e-popup .e-range-header .e-start-end .e-start-btn:hover,
.e-bigger.e-daterangepicker.e-popup .e-range-header .e-start-end .e-end-btn:hover:not([disabled]),
.e-device.e-daterangepicker.e-popup .e-range-header .e-start-end .e-start-btn:hover,
.e-device.e-daterangepicker.e-popup .e-range-header .e-start-end .e-end-btn:hover:not([disabled]) {
  box-shadow: none;
}

.e-bigger .e-daterangepicker.e-popup .e-range-header .e-start-end .e-start-btn.e-active,
.e-bigger .e-daterangepicker.e-popup .e-range-header .e-start-end .e-start-btn.e-active:active,
.e-bigger .e-daterangepicker.e-popup .e-range-header .e-start-end .e-end-btn.e-active,
.e-bigger .e-daterangepicker.e-popup .e-range-header .e-start-end .e-end-btn.e-active:active:not([disabled]),
.e-bigger .e-daterangepicker.e-popup .e-range-header .e-start-end .e-start-btn.e-active:hover,
.e-bigger .e-daterangepicker.e-popup .e-range-header .e-start-end .e-end-btn.e-active:hover,
.e-bigger.e-daterangepicker.e-popup .e-range-header .e-start-end .e-start-btn.e-active,
.e-bigger.e-daterangepicker.e-popup .e-range-header .e-start-end .e-start-btn.e-active:active,
.e-bigger.e-daterangepicker.e-popup .e-range-header .e-start-end .e-end-btn.e-active,
.e-bigger.e-daterangepicker.e-popup .e-range-header .e-start-end .e-end-btn.e-active:active:not([disabled]),
.e-bigger.e-daterangepicker.e-popup .e-range-header .e-start-end .e-start-btn.e-active:hover,
.e-bigger.e-daterangepicker.e-popup .e-range-header .e-start-end .e-end-btn.e-active:hover,
.e-device.e-daterangepicker.e-popup .e-range-header .e-start-end .e-start-btn.e-active,
.e-device.e-daterangepicker.e-popup .e-range-header .e-start-end .e-start-btn.e-active:active,
.e-device.e-daterangepicker.e-popup .e-range-header .e-start-end .e-end-btn.e-active,
.e-device.e-daterangepicker.e-popup .e-range-header .e-start-end .e-end-btn.e-active:active:not([disabled]),
.e-device.e-daterangepicker.e-popup .e-range-header .e-start-end .e-start-btn.e-active:hover,
.e-device.e-daterangepicker.e-popup .e-range-header .e-start-end .e-end-btn.e-active:hover {
  box-shadow: none;
}

.e-bigger .e-daterangepicker.e-popup .e-range-header .e-start-end .e-start-btn,
.e-bigger .e-daterangepicker.e-popup .e-range-header .e-start-end .e-end-btn,
.e-bigger.e-daterangepicker.e-popup .e-range-header .e-start-end .e-start-btn,
.e-bigger.e-daterangepicker.e-popup .e-range-header .e-start-end .e-end-btn,
.e-device.e-daterangepicker.e-popup .e-range-header .e-start-end .e-start-btn,
.e-device.e-daterangepicker.e-popup .e-range-header .e-start-end .e-end-btn {
  max-width: 132px;
}

.e-bigger .e-daterangepicker.e-popup .e-presets,
.e-bigger.e-daterangepicker.e-popup .e-presets,
.e-device.e-daterangepicker.e-popup .e-presets {
  max-height: 240px;
}

.e-bigger .e-daterangepicker.e-popup .e-presets.e-preset-wrapper,
.e-bigger.e-daterangepicker.e-popup .e-presets.e-preset-wrapper,
.e-device.e-daterangepicker.e-popup .e-presets.e-preset-wrapper {
  max-height: none;
}

.e-bigger .e-daterangepicker.e-popup .e-presets ul,
.e-bigger.e-daterangepicker.e-popup .e-presets ul,
.e-device.e-daterangepicker.e-popup .e-presets ul {
  max-width: none;
}

.e-bigger .e-daterangepicker.e-popup .e-presets ul li.e-list-item,
.e-bigger.e-daterangepicker.e-popup .e-presets ul li.e-list-item,
.e-device.e-daterangepicker.e-popup .e-presets ul li.e-list-item {
  font-size: 15px;
  height: 45px;
  line-height: 45px;
  padding: 0 12px;
}

.e-bigger .e-daterangepicker .e-calendar,
.e-bigger.e-daterangepicker .e-calendar,
.e-device.e-daterangepicker .e-calendar {
  max-width: 292px;
  padding: 0;
}

.e-bigger .e-daterangepicker .e-calendar .e-content table,
.e-bigger.e-daterangepicker .e-calendar .e-content table,
.e-device.e-daterangepicker .e-calendar .e-content table {
  padding: 0 16px 15px;
}

.e-bigger .e-daterangepicker .e-calendar .e-header .e-next,
.e-bigger .e-daterangepicker .e-calendar .e-header .e-prev,
.e-bigger.e-daterangepicker .e-calendar .e-header .e-next,
.e-bigger.e-daterangepicker .e-calendar .e-header .e-prev,
.e-device.e-daterangepicker .e-calendar .e-header .e-next,
.e-device.e-daterangepicker .e-calendar .e-header .e-prev {
  height: 48px;
  width: 48px;
}

.e-bigger .e-daterangepicker .e-calendar .e-header .e-next span,
.e-bigger .e-daterangepicker .e-calendar .e-header .e-prev span,
.e-bigger.e-daterangepicker .e-calendar .e-header .e-next span,
.e-bigger.e-daterangepicker .e-calendar .e-header .e-prev span,
.e-device.e-daterangepicker .e-calendar .e-header .e-next span,
.e-device.e-daterangepicker .e-calendar .e-header .e-prev span {
  padding: 15px;
}

.e-bigger .e-daterangepicker .e-calendar .e-header .e-title,
.e-bigger.e-daterangepicker .e-calendar .e-header .e-title,
.e-device.e-daterangepicker .e-calendar .e-header .e-title {
  cursor: pointer;
  line-height: 48px;
}

.e-bigger .e-daterangepicker .e-calendar .e-header.e-month, .e-bigger .e-daterangepicker .e-calendar .e-header.e-year, .e-bigger .e-daterangepicker .e-calendar .e-header.e-decade,
.e-bigger.e-daterangepicker .e-calendar .e-header.e-month,
.e-bigger.e-daterangepicker .e-calendar .e-header.e-year,
.e-bigger.e-daterangepicker .e-calendar .e-header.e-decade,
.e-device.e-daterangepicker .e-calendar .e-header.e-month,
.e-device.e-daterangepicker .e-calendar .e-header.e-year,
.e-device.e-daterangepicker .e-calendar .e-header.e-decade {
  padding: 10px 16px 0 16px;
}

.e-bigger .e-daterangepicker .e-footer,
.e-bigger.e-daterangepicker .e-footer,
.e-device.e-daterangepicker .e-footer {
  height: 58px;
}

.e-bigger .e-daterangepicker .e-footer .e-btn,
.e-bigger.e-daterangepicker .e-footer .e-btn,
.e-device.e-daterangepicker .e-footer .e-btn {
  height: 40px;
  line-height: 38px;
  overflow: hidden;
}

.e-bigger .e-daterangepicker .e-footer .e-btn.e-apply,
.e-bigger.e-daterangepicker .e-footer .e-btn.e-apply,
.e-device.e-daterangepicker .e-footer .e-btn.e-apply {
  margin: 0 15px 0 5px;
}

.e-bigger .e-daterangepicker.e-rtl.e-popup .e-range-header .e-start-end .e-end-btn,
.e-bigger.e-daterangepicker.e-rtl.e-popup .e-range-header .e-start-end .e-end-btn,
.e-device.e-daterangepicker.e-rtl.e-popup .e-range-header .e-start-end .e-end-btn {
  border: 1px solid #000;
  border-radius: 2px 0 0 2px;
  border-right: 0;
}

.e-bigger .e-daterangepicker.e-rtl.e-popup .e-range-header .e-start-end .e-start-btn,
.e-bigger.e-daterangepicker.e-rtl.e-popup .e-range-header .e-start-end .e-start-btn,
.e-device.e-daterangepicker.e-rtl.e-popup .e-range-header .e-start-end .e-start-btn {
  border-radius: 0 2px 2px 0;
}

.e-bigger .e-daterangepicker.e-rtl.e-popup .e-footer.e-btn.e-cancel,
.e-bigger.e-daterangepicker.e-rtl.e-popup .e-footer.e-btn.e-cancel,
.e-device.e-daterangepicker.e-rtl.e-popup .e-footer.e-btn.e-cancel {
  margin: 0 5px 0 15px;
}

.e-bigger .e-daterangepicker.e-rtl.e-popup .e-footer .e-btn.e-apply,
.e-bigger.e-daterangepicker.e-rtl.e-popup .e-footer .e-btn.e-apply,
.e-device.e-daterangepicker.e-rtl.e-popup .e-footer .e-btn.e-apply {
  margin-left: 0;
}

.e-bigger .e-daterangepicker.e-device.e-popup,
.e-bigger.e-daterangepicker.e-device.e-popup,
.e-device.e-daterangepicker.e-device.e-popup {
  max-width: 294px;
}

.e-bigger .e-daterangepicker.e-device.e-popup .e-range-header,
.e-bigger.e-daterangepicker.e-device.e-popup .e-range-header,
.e-device.e-daterangepicker.e-device.e-popup .e-range-header {
  margin: 20px 10px 0 10px;
}

.e-bigger .e-daterangepicker.e-device.e-popup .e-range-header .e-day-span,
.e-bigger.e-daterangepicker.e-device.e-popup .e-range-header .e-day-span,
.e-device.e-daterangepicker.e-device.e-popup .e-range-header .e-day-span {
  margin: 10px 0;
}

.e-small .e-daterangepicker.e-popup .e-range-header,
.e-small.e-daterangepicker.e-popup .e-range-header {
  margin: 10px 10px 0 10px;
}

.e-small .e-daterangepicker.e-popup .e-range-header .e-start-label,
.e-small .e-daterangepicker.e-popup .e-range-header .e-end-label,
.e-small.e-daterangepicker.e-popup .e-range-header .e-start-label,
.e-small.e-daterangepicker.e-popup .e-range-header .e-end-label {
  font-size: 16px;
}

.e-small .e-daterangepicker.e-popup .e-range-header .e-change-icon,
.e-small.e-daterangepicker.e-popup .e-range-header .e-change-icon {
  font-size: 12px;
}

.e-small .e-daterangepicker.e-popup .e-range-header .e-start-end,
.e-small.e-daterangepicker.e-popup .e-range-header .e-start-end {
  height: 32px;
}

.e-small .e-daterangepicker.e-popup .e-range-header .e-day-span,
.e-small.e-daterangepicker.e-popup .e-range-header .e-day-span {
  font-size: 12px;
  margin: 0 0 10px 0;
}

.e-small .e-daterangepicker.e-popup .e-range-header .e-separator,
.e-small.e-daterangepicker.e-popup .e-range-header .e-separator {
  margin: 0 10px;
}

.e-small .e-daterangepicker.e-popup .e-footer .e-btn.e-apply,
.e-small.e-daterangepicker.e-popup .e-footer .e-btn.e-apply {
  margin: 10px 10px 10px 8px;
}

.e-small .e-daterangepicker.e-popup.e-preset-wrapper .e-presets .e-list-parent.e-ul .e-list-item,
.e-small.e-daterangepicker.e-popup.e-preset-wrapper .e-presets .e-list-parent.e-ul .e-list-item {
  font-size: 12px;
  height: 26px;
  line-height: 26px;
}

.e-range-overflow {
  overflow: hidden;
}

.e-daterangepick-mob-popup-wrap {
  -ms-flex-align: center;
      align-items: center;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-direction: column;
      flex-direction: column;
  height: 100%;
  -ms-flex-pack: center;
      justify-content: center;
  left: 0;
  max-height: 100%;
  position: fixed;
  top: 0;
  width: 100%;
  z-index: 1002;
}

.e-daterangepick-mob-popup-wrap .e-daterangepicker.e-popup.e-control.e-lib.e-device.e-popup-open {
  position: relative;
  top: 0 !important;
  left: 0 !important;
}

.e-content-placeholder.e-daterangepicker.e-placeholder-daterangepicker {
  background-size: 250px 33px;
  min-height: 33px;
}

.e-bigger .e-content-placeholder.e-daterangepicker.e-placeholder-daterangepicker,
.e-bigger.e-content-placeholder.e-daterangepicker.e-placeholder-daterangepicker {
  background-size: 250px 40px;
  min-height: 40px;
}

/*! daterangepicker theme */
.e-date-range-wrapper .e-input-group-icon.e-icons.e-active {
  color: #000;
}

.e-date-range-wrapper.e-input-group:not(.e-disabled) .e-input-group-icon.e-active:active {
  color: #fff;
}

.e-daterangepicker.e-popup,
.e-bigger.e-small .e-daterangepicker.e-popup {
  background: #fff;
}

.e-daterangepicker.e-popup .e-calendar,
.e-bigger.e-small .e-daterangepicker.e-popup .e-calendar {
  background-color: #fff;
}

.e-daterangepicker.e-popup .e-calendar .e-header .e-title,
.e-daterangepicker.e-popup .e-calendar .e-header .e-title:hover,
.e-bigger.e-small .e-daterangepicker.e-popup .e-calendar .e-header .e-title,
.e-bigger.e-small .e-daterangepicker.e-popup .e-calendar .e-header .e-title:hover {
  color: #000;
  text-decoration: none;
}

.e-daterangepicker.e-popup .e-calendar .e-content .e-range-hover,
.e-bigger.e-small .e-daterangepicker.e-popup .e-calendar .e-content .e-range-hover {
  background-color: #400074;
  color: #fff;
}

.e-daterangepicker.e-popup .e-calendar .e-content.e-month .e-today.e-range-hover span,
.e-bigger.e-small .e-daterangepicker.e-popup .e-calendar .e-content.e-month .e-today.e-range-hover span {
  background-color: #400074;
  border: 1px solid #ecf;
  color: #fff;
}

.e-daterangepicker.e-popup .e-calendar .e-content .e-range-hover span,
.e-bigger.e-small .e-daterangepicker.e-popup .e-calendar .e-content .e-range-hover span {
  background: #400074;
  border: 1px solid #400074;
  color: #fff;
}

.e-daterangepicker.e-popup .e-calendar .e-range-hover:not(.e-selected):hover span.e-day,
.e-daterangepicker.e-popup .e-calendar .e-range-hover.e-focused-date:not(.e-selected) span.e-day,
.e-daterangepicker.e-popup .e-calendar .e-range-hover.e-focused-date.e-today span.e-day,
.e-bigger.e-small .e-daterangepicker.e-popup .e-calendar .e-range-hover:not(.e-selected):hover span.e-day,
.e-bigger.e-small .e-daterangepicker.e-popup .e-calendar .e-range-hover.e-focused-date:not(.e-selected) span.e-day,
.e-bigger.e-small .e-daterangepicker.e-popup .e-calendar .e-range-hover.e-focused-date.e-today span.e-day {
  background-color: #160028;
  border: none;
  color: #fff;
}

.e-daterangepicker.e-popup .e-calendar .e-range-hover.e-today:hover span.e-day,
.e-daterangepicker.e-popup .e-calendar .e-range-hover.e-focused-date.e-today span.e-day,
.e-daterangepicker.e-popup .e-calendar .e-range-hover.e-start-date.e-selected.e-today span.e-day,
.e-daterangepicker.e-popup .e-calendar .e-range-hover.e-end-date.e-selected.e-today span.e-day,
.e-bigger.e-small .e-daterangepicker.e-popup .e-calendar .e-range-hover.e-today:hover span.e-day,
.e-bigger.e-small .e-daterangepicker.e-popup .e-calendar .e-range-hover.e-focused-date.e-today span.e-day,
.e-bigger.e-small .e-daterangepicker.e-popup .e-calendar .e-range-hover.e-start-date.e-selected.e-today span.e-day,
.e-bigger.e-small .e-daterangepicker.e-popup .e-calendar .e-range-hover.e-end-date.e-selected.e-today span.e-day {
  border: 1px solid #ecf;
}

.e-daterangepicker.e-popup .e-calendar .e-range-hover.e-selected.e-today:hover span.e-day,
.e-bigger.e-small .e-daterangepicker.e-popup .e-calendar .e-range-hover.e-selected.e-today:hover span.e-day {
  border: 1px solid #ecf;
}

.e-daterangepicker.e-popup .e-calendar .e-content .e-other-month.e-range-hover,
.e-daterangepicker.e-popup .e-calendar .e-content .e-other-month.e-today.e-range-hover span,
.e-daterangepicker.e-popup .e-calendar .e-content .e-other-month.e-range-hover span,
.e-daterangepicker.e-popup .e-calendar .e-content .e-other-month.e-range-hover:hover span.e-day,
.e-daterangepicker.e-popup .e-calendar .e-content .e-other-month.e-selected,
.e-daterangepicker.e-popup .e-calendar .e-content .e-other-month.e-selected span,
.e-bigger.e-small .e-daterangepicker.e-popup .e-calendar .e-content .e-other-month.e-range-hover,
.e-bigger.e-small .e-daterangepicker.e-popup .e-calendar .e-content .e-other-month.e-today.e-range-hover span,
.e-bigger.e-small .e-daterangepicker.e-popup .e-calendar .e-content .e-other-month.e-range-hover span,
.e-bigger.e-small .e-daterangepicker.e-popup .e-calendar .e-content .e-other-month.e-range-hover:hover span.e-day,
.e-bigger.e-small .e-daterangepicker.e-popup .e-calendar .e-content .e-other-month.e-selected,
.e-bigger.e-small .e-daterangepicker.e-popup .e-calendar .e-content .e-other-month.e-selected span {
  background-color: #fff;
  border: 1px solid #fff;
  color: #4f4f4f;
}

.e-daterangepicker.e-popup .e-calendar .e-content .e-other-month.e-range-hover,
.e-daterangepicker.e-popup .e-calendar .e-content .e-other-month.e-selected,
.e-bigger.e-small .e-daterangepicker.e-popup .e-calendar .e-content .e-other-month.e-range-hover,
.e-bigger.e-small .e-daterangepicker.e-popup .e-calendar .e-content .e-other-month.e-selected {
  background-color: #fff;
  border: none;
  color: #4f4f4f;
}

.e-daterangepicker.e-popup .e-calendar .e-end-date.e-selected.e-range-hover span.e-day,
.e-daterangepicker.e-popup .e-calendar .e-start-date.e-selected.e-range-hover span.e-day,
.e-bigger.e-small .e-daterangepicker.e-popup .e-calendar .e-end-date.e-selected.e-range-hover span.e-day,
.e-bigger.e-small .e-daterangepicker.e-popup .e-calendar .e-start-date.e-selected.e-range-hover span.e-day {
  background-color: #160028;
  color: #fff;
}

.e-daterangepicker.e-popup .e-calendar .e-start-date.e-selected.e-range-hover.e-today span.e-day,
.e-bigger.e-small .e-daterangepicker.e-popup .e-calendar .e-start-date.e-selected.e-range-hover.e-today span.e-day {
  background-color: #160028;
  color: #fff;
}

.e-daterangepicker.e-popup .e-calendar .e-other-month.e-selected span,
.e-bigger.e-small .e-daterangepicker.e-popup .e-calendar .e-other-month.e-selected span {
  color: #fff;
}

.e-daterangepicker.e-popup .e-presets,
.e-bigger.e-small .e-daterangepicker.e-popup .e-presets {
  background-color: #fff;
  color: #000;
}

.e-daterangepicker.e-popup .e-presets .e-list-item.e-active,
.e-bigger.e-small .e-daterangepicker.e-popup .e-presets .e-list-item.e-active {
  background-color: #400074;
  color: #fff;
}

.e-daterangepicker.e-popup .e-presets .e-list-item.e-hover,
.e-bigger.e-small .e-daterangepicker.e-popup .e-presets .e-list-item.e-hover {
  background-color: #ecf;
  color: #000;
}

.e-daterangepicker.e-popup .e-start-label,
.e-daterangepicker.e-popup .e-end-label,
.e-bigger.e-small .e-daterangepicker.e-popup .e-start-label,
.e-bigger.e-small .e-daterangepicker.e-popup .e-end-label {
  color: #400074;
}

.e-daterangepicker.e-popup .e-change-icon,
.e-bigger.e-small .e-daterangepicker.e-popup .e-change-icon {
  color: #000;
}

.e-daterangepicker.e-popup .e-day-span,
.e-bigger.e-small .e-daterangepicker.e-popup .e-day-span {
  color: #000;
}

.e-daterangepicker.e-popup .e-separator,
.e-bigger.e-small .e-daterangepicker.e-popup .e-separator {
  background-color: #757575;
}

.e-daterangepicker.e-popup .e-footer,
.e-bigger.e-small .e-daterangepicker.e-popup .e-footer {
  background-color: #fff;
}

.e-bigger .e-daterangepicker,
.e-bigger.e-daterangepicker,
.e-device.e-daterangepicker {
  background-color: #fff;
  padding: 0;
}

.e-bigger .e-daterangepicker .e-calendar th,
.e-bigger.e-daterangepicker .e-calendar th,
.e-device.e-daterangepicker .e-calendar th {
  color: #000;
}

.e-bigger .e-daterangepicker .e-start-btn,
.e-bigger .e-daterangepicker .e-end-btn,
.e-bigger.e-daterangepicker .e-start-btn,
.e-bigger.e-daterangepicker .e-end-btn,
.e-device.e-daterangepicker .e-start-btn,
.e-device.e-daterangepicker .e-end-btn {
  background: #fff;
}

.e-bigger .e-daterangepicker .e-start-btn.e-active,
.e-bigger .e-daterangepicker .e-start-btn.e-active:active,
.e-bigger .e-daterangepicker .e-end-btn.e-active,
.e-bigger .e-daterangepicker .e-end-btn.e-active:active:not([disabled]),
.e-bigger .e-daterangepicker .e-start-btn.e-active:hover,
.e-bigger .e-daterangepicker .e-end-btn.e-active:hover,
.e-bigger.e-daterangepicker .e-start-btn.e-active,
.e-bigger.e-daterangepicker .e-start-btn.e-active:active,
.e-bigger.e-daterangepicker .e-end-btn.e-active,
.e-bigger.e-daterangepicker .e-end-btn.e-active:active:not([disabled]),
.e-bigger.e-daterangepicker .e-start-btn.e-active:hover,
.e-bigger.e-daterangepicker .e-end-btn.e-active:hover,
.e-device.e-daterangepicker .e-start-btn.e-active,
.e-device.e-daterangepicker .e-start-btn.e-active:active,
.e-device.e-daterangepicker .e-end-btn.e-active,
.e-device.e-daterangepicker .e-end-btn.e-active:active:not([disabled]),
.e-device.e-daterangepicker .e-start-btn.e-active:hover,
.e-device.e-daterangepicker .e-end-btn.e-active:hover {
  background: #400074;
  color: #fff;
}
