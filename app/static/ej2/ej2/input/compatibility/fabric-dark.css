.e-float-input .e-clear-icon::before, .e-float-input.e-control-wrapper .e-clear-icon::before {
  content: '\e953';
  font-family: 'e-icons';
}

.e-input-group .e-clear-icon::before, .e-input-group.e-control-wrapper .e-clear-icon::before {
  content: '\e953';
  font-family: 'e-icons';
}

/*! input layout */
.e-input-group, .e-input-group.e-control-wrapper {
  display: table;
  line-height: 1.4;
  margin-bottom: 0;
}

input.e-input.e-css, .e-input-group input.e-input, .e-input-group.e-control-wrapper input.e-input, textarea.e-input.e-css, .e-input-group textarea.e-input, .e-input-group.e-control-wrapper textarea.e-input {
  border: 0 solid;
  border-width: 1px;
  height: auto;
  line-height: inherit;
  margin: 0;
  margin-bottom: 0;
  outline: none;
  padding: 0;
  text-indent: 10px;
  width: 100%;
}

input.e-input.e-css, textarea.e-input.e-css, .e-input-group, .e-input-group.e-control-wrapper {
  font-family: "Segoe UI", "GeezaPro", "DejaVu Serif", sans-serif, "-apple-system", "BlinkMacSystemFont";
  font-size: 14px;
  font-weight: 400;
}

.e-input-group.e-disabled, .e-input-group.e-control-wrapper.e-disabled {
  font-family: "Segoe UI", "GeezaPro", "DejaVu Serif", sans-serif, "-apple-system", "BlinkMacSystemFont";
  font-size: 14px;
  font-weight: 400;
}

.e-input-group input.e-input, .e-input-group.e-control-wrapper input.e-input, .e-input-group textarea.e-input, .e-input-group.e-control-wrapper textarea.e-input {
  font: inherit;
}

input.e-input.e-css, .e-input-group input.e-input, .e-input-group input, .e-input-group.e-control-wrapper input.e-input, .e-input-group.e-control-wrapper input, .e-float-input input, .e-float-input.e-input-group input, .e-float-input.e-control-wrapper input, .e-float-input.e-control-wrapper.e-input-group input, input.e-input.e-css:focus, .e-input-group input.e-input:focus, .e-input-group input:focus, .e-input-group.e-control-wrapper input.e-input:focus, .e-input-group.e-control-wrapper input:focus, .e-float-input input:focus, .e-float-input.e-input-group input:focus, .e-float-input.e-control-wrapper input:focus, .e-float-input.e-control-wrapper.e-input-group input:focus {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

input.e-input.e-css, .e-input-group input.e-input, .e-input-group input, .e-input-group.e-control-wrapper input.e-input, .e-input-group.e-control-wrapper input, .e-float-input input, .e-float-input.e-input-group input, .e-float-input.e-control-wrapper input, .e-float-input.e-control-wrapper.e-input-group input, .e-input-group, .e-input-group.e-control-wrapper, .e-float-input, .e-float-input.e-control-wrapper {
  border-radius: 0;
}

.e-input.e-css:focus {
  border-width: 1px;
  padding-bottom: 0;
}

.e-input.e-small.e-css:focus {
  border-width: 1px;
  padding-bottom: 0;
}

.e-input.e-bigger.e-css:focus, .e-bigger .e-input.e-css:focus {
  padding-bottom: 0;
}

.e-input.e-small.e-bigger.e-css:focus, .e-bigger .e-input.e-small.e-css:focus {
  padding-bottom: 0;
}

.e-input-group input.e-input:focus, .e-input-group.e-control-wrapper input.e-input:focus, .e-input-group textarea.e-input:focus, .e-input-group.e-control-wrapper textarea.e-input:focus {
  padding: 0;
}

input.e-input.e-bigger.e-css, textarea.e-input.e-bigger.e-css, .e-bigger input.e-input.e-css, .e-bigger textarea.e-input.e-css, .e-input-group.e-bigger, .e-bigger .e-input-group, .e-input-group.e-control-wrapper.e-bigger, .e-bigger .e-input-group.e-control-wrapper, .e-input-group.e-bigger.e-disabled, .e-bigger .e-input-group.e-disabled, .e-input-group.e-control-wrapper.e-bigger.e-disabled, .e-bigger .e-input-group.e-control-wrapper.e-disabled {
  font-size: 15px;
}

.e-input-group.e-bigger .e-input, .e-input-group.e-bigger.e-control-wrapper .e-input, .e-bigger .e-input-group .e-input, .e-bigger .e-input-group.e-control-wrapper .e-input {
  font: inherit;
}

input.e-input.e-bigger.e-css, textarea.e-input.e-bigger.e-css, .e-input-group.e-bigger .e-input, .e-input-group.e-bigger.e-control-wrapper .e-input, .e-bigger input.e-input.e-css, .e-bigger textarea.e-input.e-css, .e-bigger .e-input-group .e-input, .e-bigger .e-input-group.e-control-wrapper .e-input {
  line-height: inherit;
  margin-bottom: 0;
  padding: 0;
}

.e-input-group.e-bigger .e-input:focus, .e-bigger .e-input-group .e-input:focus, .e-input-group.e-control-wrapper.e-bigger .e-input:focus, .e-bigger .e-input-group.e-control-wrapper .e-input:focus {
  padding: 0;
}

.e-input-group .e-input-group-icon, .e-input-group.e-control-wrapper .e-input-group-icon {
  -ms-flex-align: center;
      align-items: center;
  border: 0 solid;
  border-width: 0;
  box-sizing: content-box;
  cursor: pointer;
  -ms-flex-direction: column;
      flex-direction: column;
  font-size: 12px;
  -ms-flex-pack: center;
      justify-content: center;
  line-height: 1;
  min-height: 30px;
  min-width: 30px;
  padding: 0;
  text-align: center;
}

.e-input-group.e-bigger .e-input-group-icon, .e-input-group .e-input-group-icon.e-bigger, .e-bigger .e-input-group .e-input-group-icon, .e-input-group.e-control-wrapper.e-bigger .e-input-group-icon, .e-input-group.e-control-wrapper .e-input-group-icon.e-bigger, .e-bigger .e-input-group.e-control-wrapper .e-input-group-icon {
  min-height: 38px;
  min-width: 38px;
}

.e-input-group.e-bigger .e-input-group-icon, .e-input-group .e-input-group-icon.e-bigger, .e-bigger .e-input-group .e-input-group-icon, .e-input-group.e-control-wrapper.e-bigger .e-input-group-icon, .e-input-group.e-control-wrapper .e-input-group-icon.e-bigger, .e-bigger .e-input-group.e-control-wrapper .e-input-group-icon {
  font-size: 12px;
  padding: 0;
}

.e-input.e-css[disabled], .e-input-group .e-input[disabled], .e-input-group.e-control-wrapper .e-input.e-css[disabled], .e-input-group.e-disabled, .e-input-group.e-disabled input, .e-input-group.e-disabled input.e-input, .e-input-group.e-disabled textarea, .e-input-group.e-disabled textarea.e-input, .e-input-group.e-control-wrapper.e-disabled, .e-input-group.e-control-wrapper.e-disabled input, .e-input-group.e-control-wrapper.e-disabled input.e-input, .e-input-group.e-control-wrapper.e-disabled textarea, .e-input-group.e-control-wrapper.e-disabled textarea.e-input, .e-float-input.e-disabled input, .e-float-input.e-disabled textarea, .e-float-input input[disabled], .e-float-input input.e-disabled, .e-float-input textarea[disabled], .e-float-input textarea.e-disabled, .e-float-input.e-control-wrapper.e-disabled input, .e-float-input.e-control-wrapper.e-disabled textarea, .e-float-input.e-control-wrapper input[disabled], .e-float-input.e-control-wrapper input.e-disabled, .e-float-input.e-control-wrapper textarea[disabled], .e-float-input.e-control-wrapper textarea.e-disabled, .e-input-group.e-disabled span, .e-input-group.e-control-wrapper.e-disabled span {
  cursor: not-allowed;
}

.e-input.e-css[disabled], .e-input-group.e-disabled, .e-input-group.e-control-wrapper.e-disabled, .e-float-input input[disabled], .e-float-input input.e-disabled, .e-float-input.e-control-wrapper input[disabled], .e-float-input.e-control-wrapper input.e-disabled {
  border-color: #333232;
  border-style: solid;
}

.e-input-group.e-disabled, .e-input-group.e-control-wrapper.e-disabled {
  border-bottom-style: solid;
  border-width: 1px;
}

.e-input.e-css[disabled], .e-input-group.e-disabled, .e-input-group.e-control-wrapper.e-disabled, .e-float-input.e-disabled, .e-float-input input[disabled], .e-float-input input.e-disabled, .e-float-input.e-disabled input, .e-float-input.e-control-wrapper.e-disabled, .e-float-input.e-control-wrapper input[disabled], .e-float-input.e-control-wrapper input.e-disabled, .e-float-input.e-control-wrapper.e-disabled input {
  filter: alpha(opacity=100);
  opacity: 1;
}

.e-input.e-css.e-rtl, .e-input-group.e-rtl, .e-input-group.e-control-wrapper.e-rtl {
  direction: rtl;
}

.e-input.e-css.e-corner {
  border-radius: 0;
}

.e-input-group, .e-input-group.e-control-wrapper {
  display: -ms-inline-flexbox;
  display: inline-flex;
  vertical-align: middle;
}

.e-float-input:not(.e-input-group), .e-float-input.e-control-wrapper:not(.e-input-group) {
  display: inline-block;
}

.e-input-group .e-input-group-icon, .e-input-group.e-control-wrapper .e-input-group-icon {
  display: -ms-flexbox;
  display: flex;
}

.e-input-group .e-input-group-icon:first-child, .e-input-group.e-control-wrapper .e-input-group-icon:first-child {
  border-left-width: 0;
}

.e-input-group .e-input-group-icon, .e-input-group.e-control-wrapper .e-input-group-icon {
  white-space: nowrap;
}

.e-input-group .e-input-group-icon:not(:last-child), .e-input-group.e-control-wrapper .e-input-group-icon:not(:last-child) {
  border-right-width: 0;
}

.e-input + .e-input-group-icon, .e-input-group .e-input + .e-input-group-icon, .e-input-group.e-control-wrapper .e-input + .e-input-group-icon {
  border-left-width: 0;
}

.e-input-group.e-corner .e-input:first-child, .e-input-group.e-corner .e-input-group-icon:first-child, .e-input-group.e-control-wrapper.e-corner .e-input:first-child, .e-input-group.e-control-wrapper.e-corner .e-input-group-icon:first-child {
  border-bottom-left-radius: 0;
  border-top-left-radius: 0;
}

.e-input-group.e-corner .e-input:last-child, .e-input-group.e-corner .e-input-group-icon:last-child, .e-input-group.e-control-wrapper.e-corner .e-input:last-child, .e-input-group.e-control-wrapper.e-corner .e-input-group-icon:last-child {
  border-bottom-right-radius: 0;
  border-top-right-radius: 0;
}

.e-input-group.e-rtl .e-input-group-icon:first-child, .e-input-group.e-control-wrapper.e-rtl .e-input-group-icon:first-child {
  border-left-width: 0;
  border-right-width: 0;
}

.e-input-group.e-rtl .e-input-group-icon:last-child, .e-input-group.e-control-wrapper.e-rtl .e-input-group-icon:last-child {
  border-left-width: 0;
  border-right-width: 0;
}

.e-input-group.e-rtl .e-input-group-icon:not(:last-child), .e-input-group.e-control-wrapper.e-rtl .e-input-group-icon:not(:last-child) {
  border-left-width: 0;
}

.e-input-group.e-rtl .e-input-group-icon + .e-input, .e-input-group.e-control-wrapper.e-rtl .e-input-group-icon + .e-input {
  border-right-width: 0;
}

input.e-input.e-small.e-css, textarea.e-input.e-small.e-css, .e-small input.e-input.e-css, .e-small textarea.e-input.e-css, .e-input-group.e-small, .e-small .e-input-group, .e-input-group.e-control-wrapper.e-small, .e-small .e-input-group.e-control-wrapper, .e-input-group.e-small.e-disabled, .e-small .e-input-group.e-disabled, .e-input-group.e-control-wrapper.e-small.e-disabled, .e-small .e-input-group.e-control-wrapper.e-disabled {
  font-size: 13px;
}

.e-input-group.e-small .e-input, .e-input-group.e-small.e-control-wrapper .e-input, .e-small .e-input-group .e-input, .e-small .e-input-group.e-control-wrapper .e-input {
  font: inherit;
}

.e-input.e-css.e-small, .e-input-group.e-small .e-input, .e-input-group.e-control-wrapper.e-small .e-input {
  line-height: inherit;
  padding: 0;
}

.e-input-group.e-small .e-input:focus, .e-input-group.e-control-wrapper.e-small .e-input:focus {
  padding: 0;
}

.e-input.e-small.e-bigger.e-css, .e-input-group.e-small.e-bigger, .e-small.e-bigger .e-input-group, .e-input-group.e-control-wrapper.e-small.e-bigger, .e-small.e-bigger .e-input-group.e-control-wrapper, .e-bigger .e-input.e-css.e-small, .e-small .e-input.e-css.e-bigger, .e-bigger .e-input-group.e-small, .e-bigger .e-input-group.e-control-wrapper.e-small, .e-small .e-input-group.e-bigger, .e-small .e-input-group.e-control-wrapper.e-bigger {
  font-size: 14px;
}

.e-input-group.e-small.e-bigger.e-disabled, .e-small.e-bigger .e-input-group.e-disabled, .e-input-group.e-control-wrapper.e-small.e-bigger.e-disabled, .e-small.e-bigger .e-input-group.e-control-wrapper.e-disabled, .e-bigger .e-input.e-css.e-small.e-disabled, .e-small .e-input.e-css.e-bigger.e-disabled, .e-bigger .e-input-group.e-small.e-disabled, .e-bigger .e-input-group.e-control-wrapper.e-small.e-disabled, .e-small .e-input-group.e-bigger.e-disabled, .e-small .e-input-group.e-control-wrapper.e-bigger.e-disabled {
  font-size: 14px;
}

.e-input.e-small.e-bigger.e-css, .e-bigger .e-input.e-css.e-small, .e-small .e-input.e-css.e-bigger {
  padding: 0;
}

.e-input-group.e-small.e-bigger .e-input, .e-input-group.e-small .e-input.e-bigger, .e-input-group.e-control-wrapper.e-small.e-bigger .e-input, .e-input-group.e-control-wrapper.e-small .e-input.e-bigger, .e-bigger .e-input-group.e-small .e-input, .e-bigger .e-input-group.e-control-wrapper.e-small .e-input {
  font: inherit;
  padding: 0;
}

.e-input-group.e-small.e-bigger .e-input:focus, .e-input-group.e-small .e-input.e-bigger:focus, .e-bigger .e-input-group.e-small .e-input:focus, .e-input-group.e-control-wrapper.e-small.e-bigger .e-input:focus, .e-input-group.e-control-wrapper.e-small .e-input.e-bigger:focus, .e-bigger .e-input-group.e-control-wrapper.e-small .e-input:focus {
  padding: 0;
}

.e-input-group.e-small .e-input-group-icon, .e-input-group.e-control-wrapper.e-small .e-input-group-icon {
  font-size: 10px;
  min-height: 24px;
  min-width: 24px;
  padding: 0;
}

.e-input-group.e-small.e-bigger .e-input-group-icon, .e-input-group.e-small .e-input-group-icon.e-bigger, .e-input-group.e-control-wrapper.e-small.e-bigger .e-input-group-icon, .e-input-group.e-control-wrapper.e-small .e-input-group-icon.e-bigger, .e-bigger .e-input-group.e-small .e-input-group-icon, .e-bigger .e-input-group.e-control-wrapper.e-small .e-input-group-icon {
  font-size: 12px;
  min-height: 34px;
  min-width: 34px;
  padding: 0;
}

label.e-float-text, .e-float-input label.e-float-text, .e-float-input.e-control-wrapper label.e-float-text, .e-float-input input:not(:focus):valid ~ label.e-float-text.e-label-bottom, .e-float-input.e-control-wrapper input:not(:focus):valid ~ label.e-float-text.e-label-bottom {
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  left: 0;
  overflow: hidden;
  padding-left: 10px;
  pointer-events: none;
  position: absolute;
  text-overflow: ellipsis;
  top: -11px;
  transform: translate3d(0, 16px, 0) scale(1);
  transform-origin: left top;
  transition: 0.25s cubic-bezier(0.25, 0.8, 0.25, 1);
  -webkit-user-select: none;
     -moz-user-select: none;
      -ms-user-select: none;
          user-select: none;
  white-space: nowrap;
  width: 100%;
}

label.e-float-text, .e-float-input label.e-float-text, .e-float-input.e-control-wrapper label.e-float-text, .e-float-input input:not(:focus):valid ~ label.e-float-text.e-label-bottom, .e-float-input.e-control-wrapper input:not(:focus):valid ~ label.e-float-text.e-label-bottom {
  top: -11px;
}

label.e-float-text, .e-float-input label.e-float-text, .e-float-input.e-control-wrapper label.e-float-text, .e-float-input input:not(:focus):valid ~ label.e-float-text.e-label-bottom, .e-float-input.e-bigger input:not(:focus):valid ~ label.e-float-text.e-label-bottom, .e-bigger .e-float-input input:not(:focus):valid ~ label.e-float-text.e-label-bottom, .e-float-input.e-small input:not(:focus):valid ~ label.e-float-text.e-label-bottom, .e-small .e-float-input input:not(:focus):valid ~ label.e-float-text.e-label-bottom, .e-bigger .e-float-input.e-small input:not(:focus):valid ~ label.e-float-text.e-label-bottom, .e-small .e-float-input.e-bigger input:not(:focus):valid ~ label.e-float-text.e-label-bottom, .e-float-input.e-bigger.e-small input:not(:focus):valid ~ label.e-float-text.e-label-bottom, .e-bigger.e-small .e-float-input input:not(:focus):valid ~ label.e-float-text.e-label-bottom, .e-float-input.e-bigger.e-small input:not(:focus):valid ~ label.e-float-text.e-label-bottom, .e-float-input.e-control-wrapper input:not(:focus):valid ~ label.e-float-text.e-label-bottom, .e-float-input.e-control-wrapper.e-bigger input:not(:focus):valid ~ label.e-float-text.e-label-bottom, .e-bigger .e-float-input.e-control-wrapper input:not(:focus):valid ~ label.e-float-text.e-label-bottom, .e-float-input.e-control-wrapper.e-small input:not(:focus):valid ~ label.e-float-text.e-label-bottom, .e-small .e-float-input.e-control-wrapper input:not(:focus):valid ~ label.e-float-text.e-label-bottom, .e-bigger .e-float-input.e-control-wrapper.e-small input:not(:focus):valid ~ label.e-float-text.e-label-bottom, .e-small .e-float-input.e-control-wrapper.e-bigger input:not(:focus):valid ~ label.e-float-text.e-label-bottom, .e-float-input.e-control-wrapper.e-bigger.e-small input:not(:focus):valid ~ label.e-float-text.e-label-bottom, .e-bigger.e-small .e-float-input.e-control-wrapper input:not(:focus):valid ~ label.e-float-text.e-label-bottom, .e-float-input.e-control-wrapper.e-bigger.e-small input:not(:focus):valid ~ label.e-float-text.e-label-bottom {
  -moz-transform: translate(0%, -50%);
  -ms-transform: translate(0%, -50%);
  -o-transform: translate(0%, -50%);
  -webkit-transform: translate(0%, -50%);
  box-sizing: border-box;
  left: 0%;
  top: 50%;
  transform: translate(0%, -50%);
  width: 100%;
  font-style: normal;
}

.e-float-input.e-bigger label.e-float-text, .e-float-input label.e-float-text.e-bigger, .e-float-input input.e-bigger ~ label.e-float-text, .e-bigger .e-float-input label.e-float-text, .e-float-input.e-bigger input:not(:focus):valid ~ label.e-float-text.e-label-bottom, .e-bigger .e-float-input input:not(:focus):valid ~ label.e-float-text.e-label-bottom, .e-float-input.e-control-wrapper.e-bigger label.e-float-text, .e-float-input.e-control-wrapper label.e-float-text.e-bigger, .e-float-input.e-control-wrapper input.e-bigger ~ label.e-float-text, .e-bigger .e-float-input.e-control-wrapper label.e-float-text, .e-float-input.e-control-wrapper.e-bigger input:not(:focus):valid ~ label.e-float-text.e-label-bottom, .e-bigger .e-float-input.e-control-wrapper input:not(:focus):valid ~ label.e-float-text.e-label-bottom {
  font-size: 15px;
}

.e-float-input.e-small label.e-float-text, .e-float-input.e-small input:not(:focus):valid ~ label.e-float-text.e-label-bottom, .e-small .e-float-input input:not(:focus):valid ~ label.e-float-text.e-label-bottom, .e-float-input.e-control-wrapper.e-small label.e-float-text, .e-float-input.e-control-wrapper.e-small input:not(:focus):valid ~ label.e-float-text.e-label-bottom, .e-small .e-float-input.e-control-wrapper input:not(:focus):valid ~ label.e-float-text.e-label-bottom {
  font-size: 13px;
}

.e-float-input.e-small.e-bigger label.e-float-text, .e-bigger .e-float-input.e-small label.e-float-text, .e-bigger .e-float-input.e-small input:not(:focus):valid ~ label.e-float-text.e-label-bottom, .e-small .e-float-input.e-bigger input:not(:focus):valid ~ label.e-float-text.e-label-bottom, .e-float-input.e-bigger.e-small input:not(:focus):valid ~ label.e-float-text.e-label-bottom, .e-bigger.e-small .e-float-input input:not(:focus):valid ~ label.e-float-text.e-label-bottom, .e-float-input.e-bigger.e-small input:not(:focus):valid ~ label.e-float-text.e-label-bottom, .e-float-input.e-control-wrapper.e-small.e-bigger label.e-float-text, .e-bigger .e-float-input.e-control-wrapper.e-small label.e-float-text, .e-bigger .e-float-input.e-control-wrapper.e-small input:not(:focus):valid ~ label.e-float-text.e-label-bottom, .e-small .e-float-input.e-control-wrapper.e-bigger input:not(:focus):valid ~ label.e-float-text.e-label-bottom, .e-float-input.e-control-wrapper.e-bigger.e-small input:not(:focus):valid ~ label.e-float-text.e-label-bottom, .e-bigger.e-small .e-float-input.e-control-wrapper input:not(:focus):valid ~ label.e-float-text.e-label-bottom, .e-float-input.e-control-wrapper.e-bigger.e-small input:not(:focus):valid ~ label.e-float-text.e-label-bottom {
  font-size: 14px;
  font-style: normal;
}

.e-float-input input:focus ~ label.e-float-text, .e-float-input input:valid ~ label.e-float-text, .e-float-input input ~ label.e-label-top.e-float-text, .e-float-input input[readonly] ~ label.e-label-top.e-float-text, .e-float-input input[disabled] ~ label.e-label-top.e-float-text, .e-float-input input label.e-float-text.e-label-top, .e-float-input.e-control-wrapper input:focus ~ label.e-float-text, .e-float-input.e-control-wrapper input:valid ~ label.e-float-text, .e-float-input.e-control-wrapper input ~ label.e-label-top.e-float-text, .e-float-input.e-control-wrapper input[readonly] ~ label.e-label-top.e-float-text, .e-float-input.e-control-wrapper input[disabled] ~ label.e-label-top.e-float-text, .e-float-input.e-control-wrapper input label.e-float-text.e-label-top {
  font-size: 12px;
  font-style: normal;
  padding-right: 0;
  transform: translate3d(-10px, -43px, 0) scale(1);
}

.e-float-input.e-bigger input:focus ~ label.e-float-text, .e-float-input.e-bigger input:valid ~ label.e-float-text, .e-float-input.e-bigger input ~ label.e-label-top.e-float-text, .e-float-input.e-bigger input[readonly] ~ label.e-label-top.e-float-text, .e-float-input.e-bigger input[disabled] ~ label.e-label-top.e-float-text, .e-float-input.e-bigger input label.e-float-text.e-label-top, .e-bigger .e-float-input input:focus ~ label.e-float-text, .e-bigger .e-float-input input:valid ~ label.e-float-text, .e-bigger .e-float-input input ~ label.e-label-top.e-float-text, .e-bigger .e-float-input input[readonly] ~ label.e-label-top.e-float-text, .e-bigger .e-float-input input[disabled] ~ label.e-label-top.e-float-text, .e-bigger .e-float-input input label.e-float-text.e-label-top, .e-float-input.e-control-wrapper.e-bigger input:focus ~ label.e-float-text, .e-float-input.e-control-wrapper.e-bigger input:valid ~ label.e-float-text, .e-float-input.e-control-wrapper.e-bigger input ~ label.e-label-top.e-float-text, .e-float-input.e-control-wrapper.e-bigger input[readonly] ~ label.e-label-top.e-float-text, .e-float-input.e-control-wrapper.e-bigger input[disabled] ~ label.e-label-top.e-float-text, .e-float-input.e-control-wrapper.e-bigger input label.e-float-text.e-label-top, .e-bigger .e-float-input.e-control-wrapper input:focus ~ label.e-float-text, .e-bigger .e-float-input.e-control-wrapper input:valid ~ label.e-float-text, .e-bigger .e-float-input.e-control-wrapper input ~ label.e-label-top.e-float-text, .e-bigger .e-float-input.e-control-wrapper input[readonly] ~ label.e-label-top.e-float-text, .e-bigger .e-float-input.e-control-wrapper input[disabled] ~ label.e-label-top.e-float-text, .e-bigger .e-float-input.e-control-wrapper input label.e-float-text.e-label-top {
  font-style: normal;
  padding-right: 0;
  transform: translate3d(-10px, -43px, 0) scale(1);
}

.e-float-input.e-bigger.e-small input:focus ~ label.e-float-text, .e-float-input.e-bigger.e-small input:valid ~ label.e-float-text, .e-float-input.e-bigger.e-small input ~ label.e-label-top.e-float-text, .e-float-input.e-bigger.e-small input[readonly] ~ label.e-label-top.e-float-text, .e-float-input.e-bigger.e-small input[disabled] ~ label.e-label-top.e-float-text, .e-float-input.e-bigger.e-small input label.e-float-text.e-label-top, .e-bigger .e-float-input.e-small input:focus ~ label.e-float-text, .e-bigger .e-float-input.e-small input:valid ~ label.e-float-text, .e-bigger .e-float-input.e-small input ~ label.e-label-top.e-float-text, .e-bigger .e-float-input.e-small input[readonly] ~ label.e-label-top.e-float-text, .e-bigger .e-float-input.e-small input[disabled] ~ label.e-label-top.e-float-text, .e-bigger .e-float-input.e-small input label.e-float-text.e-label-top, .e-small .e-float-input.e-bigger input:focus ~ label.e-float-text, .e-small .e-float-input.e-bigger input:valid ~ label.e-float-text, .e-small .e-float-input.e-bigger input ~ label.e-label-top.e-float-text, .e-small .e-float-input.e-bigger input[readonly] ~ label.e-label-top.e-float-text, .e-small .e-float-input.e-bigger input[disabled] ~ label.e-label-top.e-float-text, .e-small .e-float-input.e-bigger input label.e-float-text.e-label-top, .e-float-input.e-control-wrapper.e-bigger.e-small input:focus ~ label.e-float-text, .e-float-input.e-control-wrapper.e-bigger.e-small input:valid ~ label.e-float-text, .e-float-input.e-control-wrapper.e-bigger.e-small input ~ label.e-label-top.e-float-text, .e-float-input.e-control-wrapper.e-bigger.e-small input[readonly] ~ label.e-label-top.e-float-text, .e-float-input.e-control-wrapper.e-bigger.e-small input[disabled] ~ label.e-label-top.e-float-text, .e-float-input.e-control-wrapper.e-bigger.e-small input label.e-float-text.e-label-top, .e-bigger .e-float-input.e-control-wrapper.e-small input:focus ~ label.e-float-text, .e-bigger .e-float-input.e-control-wrapper.e-small input:valid ~ label.e-float-text, .e-bigger .e-float-input.e-control-wrapper.e-small input ~ label.e-label-top.e-float-text, .e-bigger .e-float-input.e-control-wrapper.e-small input[readonly] ~ label.e-label-top.e-float-text, .e-bigger .e-float-input.e-control-wrapper.e-small input[disabled] ~ label.e-label-top.e-float-text, .e-bigger .e-float-input.e-control-wrapper.e-small input label.e-float-text.e-label-top, .e-small .e-float-input.e-control-wrapper.e-bigger input:focus ~ label.e-float-text, .e-small .e-float-input.e-control-wrapper.e-bigger input:valid ~ label.e-float-text, .e-small .e-float-input.e-control-wrapper.e-bigger input ~ label.e-label-top.e-float-text, .e-small .e-float-input.e-control-wrapper.e-bigger input[readonly] ~ label.e-label-top.e-float-text, .e-small .e-float-input.e-control-wrapper.e-bigger input[disabled] ~ label.e-label-top.e-float-text, .e-small .e-float-input.e-control-wrapper.e-bigger input label.e-float-text.e-label-top {
  font-style: normal;
  padding-right: 0;
  transform: translate3d(-10px, -43px, 0) scale(1);
}

.e-float-input.e-bigger input:focus ~ label.e-float-text, .e-float-input.e-bigger input:valid ~ label.e-float-text, .e-float-input.e-bigger input ~ label.e-label-top.e-float-text, .e-float-input.e-bigger input[readonly] ~ label.e-label-top.e-float-text, .e-float-input.e-bigger input[disabled] ~ label.e-label-top.e-float-text, .e-bigger .e-float-input input:focus ~ label.e-float-text, .e-bigger .e-float-input input:valid ~ label.e-float-text, .e-bigger .e-float-input input ~ label.e-label-top.e-float-text, .e-bigger .e-float-input input[readonly] ~ label.e-label-top.e-float-text, .e-bigger .e-float-input input[disabled] ~ label.e-label-top.e-float-text, .e-float-input.e-control-wrapper.e-bigger input:focus ~ label.e-float-text, .e-float-input.e-control-wrapper.e-bigger input:valid ~ label.e-float-text, .e-float-input.e-control-wrapper.e-bigger input ~ label.e-label-top.e-float-text, .e-float-input.e-control-wrapper.e-bigger input[readonly] ~ label.e-label-top.e-float-text, .e-float-input.e-control-wrapper.e-bigger input[disabled] ~ label.e-label-top.e-float-text, .e-bigger .e-float-input.e-control-wrapper input:focus ~ label.e-float-text, .e-bigger .e-float-input.e-control-wrapper input:valid ~ label.e-float-text, .e-bigger .e-float-input.e-control-wrapper input ~ label.e-label-top.e-float-text, .e-bigger .e-float-input.e-control-wrapper input[readonly] ~ label.e-label-top.e-float-text, .e-bigger .e-float-input.e-control-wrapper input[disabled] ~ label.e-label-top.e-float-text {
  font-size: 13px;
  padding-right: 0;
  top: 14px;
}

.e-float-input.e-small input:focus ~ label.e-float-text, .e-float-input.e-small input:valid ~ label.e-float-text, .e-float-input.e-small input ~ label.e-label-top.e-float-text, .e-small .e-float-input input ~ label.e-label-top.e-float-text, .e-float-input.e-small input[readonly] ~ label.e-label-top.e-float-text, .e-float-input.e-small input[disabled] ~ label.e-label-top.e-float-text, .e-float-input.e-control-wrapper.e-small input:focus ~ label.e-float-text, .e-float-input.e-control-wrapper.e-small input:valid ~ label.e-float-text, .e-float-input.e-control-wrapper.e-small input ~ label.e-label-top.e-float-text, .e-small .e-float-input.e-control-wrapper input ~ label.e-label-top.e-float-text, .e-float-input.e-control-wrapper.e-small input[readonly] ~ label.e-label-top.e-float-text, .e-float-input.e-control-wrapper.e-small input[disabled] ~ label.e-label-top.e-float-text {
  font-size: 11px;
  padding-right: 0;
  top: 17px;
}

.e-float-input.e-small.e-bigger input:focus ~ label.e-float-text, .e-float-input.e-small.e-bigger input:valid ~ label.e-float-text, .e-float-input.e-small.e-bigger input ~ label.e-label-top.e-float-text, .e-float-input.e-small.e-bigger input[readonly] ~ label.e-float-text, .e-float-input.e-small.e-bigger input[disabled] ~ label.e-float-text, .e-bigger .e-float-input.e-small input:focus ~ label.e-float-text, .e-bigger .e-float-input.e-small input:valid ~ label.e-float-text, .e-bigger .e-float-input.e-small input ~ label.e-label-top.e-float-text, .e-bigger .e-float-input.e-small input[readonly] ~ label.e-float-text, .e-bigger .e-float-input.e-small input[disabled] ~ label.e-float-text, .e-float-input.e-control-wrapper.e-small.e-bigger input:focus ~ label.e-float-text, .e-float-input.e-control-wrapper.e-small.e-bigger input:valid ~ label.e-float-text, .e-float-input.e-control-wrapper.e-small.e-bigger input ~ label.e-label-top.e-float-text, .e-float-input.e-control-wrapper.e-small.e-bigger input[readonly] ~ label.e-float-text, .e-float-input.e-control-wrapper.e-small.e-bigger input[disabled] ~ label.e-float-text, .e-bigger .e-float-input.e-control-wrapper.e-small input:focus ~ label.e-float-text, .e-bigger .e-float-input.e-control-wrapper.e-small input:valid ~ label.e-float-text, .e-bigger .e-float-input.e-control-wrapper.e-small input ~ label.e-label-top.e-float-text, .e-bigger .e-float-input.e-control-wrapper.e-small input[readonly] ~ label.e-float-text, .e-bigger .e-float-input.e-control-wrapper.e-small input[disabled] ~ label.e-float-text {
  font-size: 12px;
  padding-right: 0;
  top: 16px;
}

.e-float-input, .e-float-input.e-control-wrapper {
  line-height: 1.4;
  margin-bottom: 0;
  margin-top: 28px;
  padding-top: 0;
  position: relative;
  width: 100%;
}

.e-float-input.e-bigger, .e-bigger .e-float-input, .e-float-input.e-control-wrapper.e-bigger, .e-bigger .e-float-input.e-control-wrapper {
  line-height: 1.5;
  margin-bottom: 0;
  margin-top: 28px;
  padding-top: 0;
  position: relative;
  width: 100%;
}

.e-float-input.e-small, .e-float-input.e-control-wrapper.e-small, .e-small .e-float-input.e-control-wrapper {
  line-height: 1.35;
  margin-bottom: 0;
  margin-top: 25px;
  padding-top: 0;
}

.e-float-input.e-small.e-bigger, .e-bigger.e-small .e-float-input.e-small, .e-bigger .e-float-input.e-small, .e-small .e-float-input.e-bigger, .e-float-input.e-control-wrapper.e-small.e-bigger, .e-bigger.e-small .e-float-input.e-control-wrapper.e-small, .e-bigger .e-float-input.e-control-wrapper.e-small, .e-small .e-float-input.e-control-wrapper.e-bigger {
  line-height: 1.4;
  margin-bottom: 0;
  margin-top: 26px;
  padding-top: 0;
}

.e-input-group.e-bigger, .e-bigger .e-input-group, .e-input-group.e-control-wrapper.e-bigger, .e-bigger .e-input-group.e-control-wrapper {
  line-height: 1.5;
}

.e-input-group.e-small, .e-input-group.e-control-wrapper.e-small, .e-small .e-input-group, .e-small .e-input-group.e-control-wrapper, .e-input-group.e-small.e-bigger, .e-bigger.e-small .e-input-group.e-small, .e-bigger .e-input-group.e-small, .e-small .e-input-group.e-bigger, .e-input-group.e-control-wrapper.e-small.e-bigger, .e-bigger.e-small .e-input-group.e-control-wrapper.e-small, .e-bigger .e-input-group.e-control-wrapper.e-small, .e-small .e-input-group.e-control-wrapper.e-bigger {
  line-height: normal;
}

.e-float-input.e-no-float-label, .e-float-input.e-bigger.e-no-float-label, .e-bigger .e-float-input.e-no-float-label, .e-float-input.e-small.e-no-float-label, .e-small .e-float-input.e-no-float-label, .e-float-input.e-small.e-bigger.e-no-float-label, .e-bigger .e-float-input.e-small.e-no-float-label, .e-small .e-float-input.e-bigger.e-no-float-label, .e-float-input.e-control-wrapper.e-no-float-label, .e-float-input.e-control-wrapper.e-bigger.e-no-float-label, .e-bigger .e-float-input.e-control-wrapper.e-no-float-label, .e-float-input.e-control-wrapper.e-small.e-no-float-label, .e-small .e-float-input.e-control-wrapper.e-no-float-label, .e-float-input.e-control-wrapper.e-small.e-bigger.e-no-float-label, .e-bigger .e-float-input.e-control-wrapper.e-small.e-no-float-label, .e-small .e-float-input.e-control-wrapper.e-bigger.e-no-float-label {
  margin-top: 0;
}

.e-float-input, .e-float-input.e-control-wrapper, .e-float-input.e-disabled, .e-float-input.e-control-wrapper.e-disabled, .e-float-input.e-input-group.e-disabled, .e-float-input.e-input-group.e-control-wrapper.e-disabled {
  font-family: "Segoe UI", "GeezaPro", "DejaVu Serif", sans-serif, "-apple-system", "BlinkMacSystemFont";
  font-size: 14px;
  font-weight: 400;
}

.e-float-input input, .e-float-input textarea, .e-float-input.e-control-wrapper input, .e-float-input.e-control-wrapper textarea {
  border: 0 solid;
  border-width: 1px;
  display: block;
  font: inherit;
  width: 100%;
}

.e-float-input input, .e-float-input.e-control-wrapper input {
  min-width: 0;
  padding: 0;
}

.e-float-input input, .e-input-group input, .e-float-input.e-control-wrapper input, .e-input-group.e-control-wrapper input {
  text-indent: 10px;
}

.e-float-input textarea, .e-input-group textarea, .e-float-input.e-control-wrapper textarea, .e-input-group.e-control-wrapper textarea {
  text-indent: 10px;
}

.e-float-input.e-bigger, .e-bigger .e-float-input, .e-float-input.e-control-wrapper.e-bigger, .e-bigger .e-float-input.e-control-wrapper {
  font-size: 15px;
}

.e-float-input.e-bigger.e-disabled, .e-bigger .e-float-input.e-disabled, .e-float-input.e-control-wrapper.e-bigger.e-disabled, .e-bigger .e-float-input.e-control-wrapper.e-disabled, .e-float-input.e-input-group.e-bigger.e-disabled, .e-bigger .e-float-input.e-input-group.e-disabled, .e-float-input.e-input-group.e-control-wrapper.e-bigger.e-disabled, .e-bigger .e-float-input.e-input-group.e-control-wrapper.e-disabled {
  font-size: 15px;
}

.e-float-input.e-bigger input, .e-float-input input.e-bigger, .e-bigger .e-float-input input, .e-float-input.e-control-wrapper.e-bigger input, .e-float-input.e-control-wrapper input.e-bigger, .e-bigger .e-float-input.e-control-wrapper input {
  font: inherit;
  line-height: inherit;
  padding: 0;
}

.e-float-input.e-small, .e-small .e-float-input, .e-float-input.e-control-wrapper.e-small, .e-small .e-float-input.e-control-wrapper {
  font-size: 13px;
}

.e-float-input.e-small.e-disabled, .e-small .e-float-input.e-disabled, .e-float-input.e-control-wrapper.e-small.e-disabled, .e-small .e-float-input.e-control-wrapper.e-disabled, .e-float-input.e-input-group.e-small.e-disabled, .e-small .e-float-input.e-input-group.e-disabled, .e-float-input.e-input-group.e-control-wrapper.e-small.e-disabled, .e-small .e-float-input.e-input-group.e-control-wrapper.e-disabled {
  font-size: 13px;
}

.e-float-input.e-small input, .e-float-input.e-control-wrapper.e-small input {
  font: inherit;
  line-height: inherit;
  padding: 0;
}

.e-float-input.e-small.e-bigger, .e-small.e-bigger .e-float-input, .e-bigger .e-float-input.e-small, .e-small .e-float-input.e-bigger, .e-float-input.e-control-wrapper.e-small.e-bigger, .e-small.e-bigger .e-float-input.e-control-wrapper, .e-bigger .e-float-input.e-control-wrapper.e-small, .e-small .e-float-input.e-control-wrapper.e-bigger {
  font-size: 14px;
}

.e-float-input.e-small.e-bigger.e-disabled, .e-small.e-bigger .e-float-input.e-disabled, .e-bigger .e-float-input.e-small.e-disabled, .e-small .e-float-input.e-bigger.e-disabled, .e-float-input.e-control-wrapper.e-small.e-bigger.e-disabled, .e-small.e-bigger .e-float-input.e-control-wrapper.e-disabled, .e-bigger .e-float-input.e-control-wrapper.e-small.e-disabled, .e-small .e-float-input.e-control-wrapper.e-bigger.e-disabled, .e-float-input.e-input-group.e-small.e-bigger.e-disabled, .e-small.e-bigger .e-float-input.e-input-group.e-disabled, .e-bigger .e-float-input.e-input-group.e-small.e-disabled, .e-small .e-float-input.e-input-group.e-bigger.e-disabled, .e-float-input.e-input-group.e-control-wrapper.e-small.e-bigger.e-disabled, .e-small.e-bigger .e-float-input.e-input-group.e-control-wrapper.e-disabled, .e-bigger .e-float-input.e-input-group.e-control-wrapper.e-small.e-disabled, .e-small .e-float-input.e-input-group.e-control-wrapper.e-bigger.e-disabled {
  font-size: 14px;
}

.e-float-input.e-small.e-bigger input, .e-float-input.e-small input.e-bigger, .e-bigger .e-float-input.e-small input, .e-float-input.e-control-wrapper.e-small.e-bigger input, .e-float-input.e-control-wrapper.e-small input.e-bigger, .e-bigger .e-float-input.e-control-wrapper.e-small input {
  font: inherit;
  line-height: inherit;
  padding: 0;
}

.e-float-input input:focus, .e-float-input.e-control-wrapper input:focus, .e-float-input textarea:focus, .e-float-input.e-control-wrapper textarea:focus {
  outline: none;
}

label.e-float-text, .e-float-input label.e-float-text, .e-float-input.e-control-wrapper label.e-float-text {
  font-family: "Segoe UI", "GeezaPro", "DejaVu Serif", sans-serif, "-apple-system", "BlinkMacSystemFont";
}

.e-float-input input:valid ~ label.e-float-text, .e-float-input input:focus ~ label.e-float-text, .e-float-input input:valid ~ label.e-float-text.e-label-top, .e-float-input input ~ label.e-float-text.e-label-top, .e-float-input .e-input-in-wrap input:valid ~ label.e-float-text, .e-float-input .e-input-in-wrap input:valid ~ label.e-float-text.e-label-top, .e-float-input .e-input-in-wrap input ~ label.e-float-text.e-label-top, .e-float-input.e-control-wrapper input:valid ~ label.e-float-text, .e-float-input.e-control-wrapper input:focus ~ label.e-float-text, .e-float-input.e-control-wrapper input:valid ~ label.e-float-text.e-label-top, .e-float-input.e-control-wrapper input ~ label.e-float-text.e-label-top, .e-float-input.e-control-wrapper .e-input-in-wrap input:valid ~ label.e-float-text, .e-float-input.e-control-wrapper .e-input-in-wrap input:valid ~ label.e-float-text.e-label-top, .e-float-input.e-control-wrapper .e-input-in-wrap input ~ label.e-float-text.e-label-top {
  -webkit-user-select: text;
     -moz-user-select: text;
      -ms-user-select: text;
          user-select: text;
}

.e-float-input textarea:valid ~ label.e-float-text, .e-float-input textarea:focus ~ label.e-float-text, .e-float-input textarea:valid ~ label.e-float-text.e-label-top, .e-float-input textarea ~ label.e-float-text.e-label-top, .e-float-input.e-control-wrapper textarea:valid ~ label.e-float-text, .e-float-input.e-control-wrapper textarea:focus ~ label.e-float-text, .e-float-input.e-control-wrapper textarea:valid ~ label.e-float-text.e-label-top, .e-float-input.e-control-wrapper textarea ~ label.e-float-text.e-label-top {
  -webkit-user-select: text;
     -moz-user-select: text;
      -ms-user-select: text;
          user-select: text;
}

label.e-float-text, .e-float-input label.e-float-text, .e-float-input.e-control-wrapper label.e-float-text, .e-float-input:not(.e-error) input:not(:focus):valid ~ label.e-float-text.e-label-bottom, .e-float-input.e-control-wrapper:not(.e-error) input:not(:focus):valid ~ label.e-float-text.e-label-bottom, .e-float-input.e-bigger:not(.e-error) input:not(:focus):valid ~ label.e-float-text.e-label-bottom, .e-float-input.e-bigger.e-control-wrapper:not(.e-error) input:not(:focus):valid ~ label.e-float-text.e-label-bottom, .e-bigger .e-float-input:not(.e-error) input:not(:focus):valid ~ label.e-float-text.e-label-bottom, .e-float-input.e-small:not(.e-error) input:not(:focus):valid ~ label.e-float-text.e-label-bottom, .e-small .e-float-input:not(.e-error) input:not(:focus):valid ~ label.e-float-text.e-label-bottom, .e-float-input.e-small.e-bigger:not(.e-error) input:not(:focus):valid ~ label.e-float-text.e-label-bottom, .e-bigger .e-float-input.e-small:not(.e-error) input:not(:focus):valid ~ label.e-float-text.e-label-bottom, .e-float-input.e-control-wrapper:not(.e-error) input:not(:focus):valid ~ label.e-float-text.e-label-bottom, .e-float-input.e-control-wrapper.e-bigger:not(.e-error) input:not(:focus):valid ~ label.e-float-text.e-label-bottom, .e-bigger .e-float-input.e-control-wrapper:not(.e-error) input:not(:focus):valid ~ label.e-float-text.e-label-bottom, .e-float-input.e-control-wrapper.e-small:not(.e-error) input:not(:focus):valid ~ label.e-float-text.e-label-bottom, .e-small .e-float-input.e-control-wrapper:not(.e-error) input:not(:focus):valid ~ label.e-float-text.e-label-bottom, .e-float-input.e-control-wrapper.e-small.e-bigger:not(.e-error) input:not(:focus):valid ~ label.e-float-text.e-label-bottom, .e-bigger .e-float-input.e-control-wrapper.e-small:not(.e-error) input:not(:focus):valid ~ label.e-float-text.e-label-bottom {
  font-weight: 400;
}

.e-float-input:not(.e-error) textarea:not(:focus):valid ~ label.e-float-text.e-label-bottom, .e-float-input.e-control-wrapper:not(.e-error) textarea:not(:focus):valid ~ label.e-float-text.e-label-bottom, .e-float-input.e-bigger:not(.e-error) textarea:not(:focus):valid ~ label.e-float-text.e-label-bottom, .e-float-input.e-bigger.e-control-wrapper:not(.e-error) textarea:not(:focus):valid ~ label.e-float-text.e-label-bottom, .e-bigger .e-float-input:not(.e-error) textarea:not(:focus):valid ~ label.e-float-text.e-label-bottom, .e-float-input.e-small:not(.e-error) textarea:not(:focus):valid ~ label.e-float-text.e-label-bottom, .e-small .e-float-input:not(.e-error) textarea:not(:focus):valid ~ label.e-float-text.e-label-bottom, .e-float-input.e-small.e-bigger:not(.e-error) textarea:not(:focus):valid ~ label.e-float-text.e-label-bottom, .e-bigger .e-float-input.e-small:not(.e-error) textarea:not(:focus):valid ~ label.e-float-text.e-label-bottom, .e-float-input.e-control-wrapper:not(.e-error) textarea:not(:focus):valid ~ label.e-float-text.e-label-bottom, .e-float-input.e-control-wrapper.e-bigger:not(.e-error) textarea:not(:focus):valid ~ label.e-float-text.e-label-bottom, .e-bigger .e-float-input.e-control-wrapper:not(.e-error) textarea:not(:focus):valid ~ label.e-float-text.e-label-bottom, .e-float-input.e-control-wrapper.e-small:not(.e-error) textarea:not(:focus):valid ~ label.e-float-text.e-label-bottom, .e-small .e-float-input.e-control-wrapper:not(.e-error) textarea:not(:focus):valid ~ label.e-float-text.e-label-bottom, .e-float-input.e-control-wrapper.e-small.e-bigger:not(.e-error) textarea:not(:focus):valid ~ label.e-float-text.e-label-bottom, .e-bigger .e-float-input.e-control-wrapper.e-small:not(.e-error) textarea:not(:focus):valid ~ label.e-float-text.e-label-bottom {
  font-weight: 400;
}

.e-float-input:not(.e-input-group) .e-float-line::before, .e-float-input:not(.e-input-group) .e-float-line::after, .e-float-input.e-control-wrapper:not(.e-input-group) .e-float-line::before, .e-float-input.e-control-wrapper:not(.e-input-group) .e-float-line::after {
  bottom: 0;
  content: '';
  height: 2px;
  position: absolute;
  transition: .2s ease;
  width: 0;
}

.e-float-input:not(.e-input-group) .e-float-line::before, .e-float-input.e-control-wrapper:not(.e-input-group) .e-float-line::before {
  left: 50%;
}

.e-float-input:not(.e-input-group) .e-float-line::after, .e-float-input.e-control-wrapper:not(.e-input-group) .e-float-line::after {
  right: 50%;
}

.e-float-input:not(.e-input-group) input:focus ~ .e-float-line::before, .e-float-input:not(.e-input-group) textarea:focus ~ .e-float-line::before, .e-float-input:not(.e-input-group) input:focus ~ .e-float-line::after, .e-float-input:not(.e-input-group) textarea:focus ~ .e-float-line::after, .e-float-input.e-control-wrapper:not(.e-input-group) input:focus ~ .e-float-line::before, .e-float-input.e-control-wrapper:not(.e-input-group) textarea:focus ~ .e-float-line::before, .e-float-input.e-control-wrapper:not(.e-input-group) input:focus ~ .e-float-line::after, .e-float-input.e-control-wrapper:not(.e-input-group) textarea:focus ~ .e-float-line::after {
  width: 50%;
}

.e-float-input .e-float-line, .e-float-input.e-control-wrapper .e-float-line {
  display: block;
  position: relative;
  width: 100%;
}

.e-float-input.e-rtl, .e-float-input.e-control-wrapper.e-rtl {
  direction: rtl;
}

.e-float-input.e-rtl label.e-float-text, .e-float-input.e-control-wrapper.e-rtl label.e-float-text, .e-rtl .e-float-input label.e-float-text, .e-rtl .e-float-input.e-control-wrapper label.e-float-text {
  right: 0;
  transform-origin: right top;
}

.e-float-input.e-rtl label.e-float-text, .e-float-input.e-rtl label.e-float-text.e-label-bottom, .e-rtl .e-float-input label.e-float-text
.e-float-input.e-control-wrapper.e-rtl label.e-float-text, .e-float-input.e-rtl.e-control-wrapper label.e-float-text.e-label-bottom, .e-rtl .e-float-input.e-control-wrapper label.e-float-text {
  padding-right: 10px;
}

.e-float-input.e-bigger.e-rtl label.e-float-text, .e-float-input.e-rtl.e-bigger label.e-float-text.e-label-bottom, .e-rtl .e-float-input.e-bigger label.e-float-text
.e-float-input.e-control-wrapper.e-rtl.e-bigger label.e-float-text, .e-float-input.e-rtl.e-control-wrapper.e-bigger label.e-float-text.e-label-bottom, .e-rtl .e-float-input.e-control-wrapper.e-bigger label.e-float-text, .e-bigger .e-float-input.e-rtl label.e-float-text, .e-bigger .e-float-input.e-rtl label.e-float-text.e-label-bottom, .e-rtl.e-bigger .e-float-input label.e-float-text
.e-bigger .e-float-input.e-control-wrapper.e-rtl label.e-float-text, .e-bigger .e-float-input.e-rtl.e-control-wrapper label.e-float-text.e-label-bottom, .e-rtl.e-bigger .e-float-input.e-control-wrapper label.e-float-text {
  padding-right: 10px;
}

.e-float-input.e-small.e-rtl label.e-float-text, .e-float-input.e-rtl.e-small label.e-float-text.e-label-bottom, .e-rtl .e-float-input.e-small label.e-float-text
.e-float-input.e-control-wrapper.e-rtl.e-small label.e-float-text, .e-float-input.e-rtl.e-control-wrapper.e-small label.e-float-text.e-label-bottom, .e-rtl .e-float-input.e-control-wrapper.e-small label.e-float-text, .e-small .e-float-input.e-rtl label.e-float-text, .e-small .e-float-input.e-rtl label.e-float-text.e-label-bottom, .e-rtl.e-small .e-float-input label.e-float-text
.e-small .e-float-input.e-control-wrapper.e-rtl label.e-float-text, .e-small .e-float-input.e-rtl.e-control-wrapper label.e-float-text.e-label-bottom, .e-rtl.e-small .e-float-input.e-control-wrapper label.e-float-text {
  padding-right: 10px;
}

.e-float-input.e-small.e-bigger.e-rtl label.e-float-text, .e-float-input.e-rtl.e-small.e-bigger label.e-float-text.e-label-bottom, .e-rtl .e-float-input.e-small.e-bigger label.e-float-text
.e-float-input.e-control-wrapper.e-rtl.e-small.e-bigger label.e-float-text, .e-float-input.e-rtl.e-control-wrapper.e-small.e-bigger label.e-float-text.e-label-bottom, .e-rtl .e-float-input.e-control-wrapper.e-small.e-bigger label.e-float-text, .e-small.e-bigger .e-float-input.e-rtl label.e-float-text, .e-small.e-bigger .e-float-input.e-rtl label.e-float-text.e-label-bottom, .e-rtl.e-small.e-bigger .e-float-input label.e-float-text
.e-small.e-bigger .e-float-input.e-control-wrapper.e-rtl label.e-float-text, .e-small.e-bigger .e-float-input.e-rtl.e-control-wrapper label.e-float-text.e-label-bottom, .e-rtl.e-small.e-bigger .e-float-input.e-control-wrapper label.e-float-text {
  padding-right: 10px;
}

.e-input-group.e-corner.e-rtl .e-input:first-child, .e-input-group.e-corner.e-rtl .e-input-group-icon:first-child, .e-input-group.e-control-wrapper.e-corner.e-rtl .e-input:first-child, .e-input-group.e-control-wrapper.e-corner.e-rtl .e-input-group-icon:first-child {
  border-bottom-left-radius: 0;
  border-bottom-right-radius: 0;
  border-top-left-radius: 0;
  border-top-right-radius: 0;
}

.e-input-group.e-corner.e-rtl .e-input:last-child, .e-input-group.e-corner.e-rtl .e-input-group-icon:last-child, .e-input-group.e-control-wrapper.e-corner.e-rtl .e-input:last-child, .e-input-group.e-control-wrapper.e-corner.e-rtl .e-input-group-icon:last-child {
  border-bottom-left-radius: 0;
  border-bottom-right-radius: 0;
  border-top-left-radius: 0;
  border-top-right-radius: 0;
}

.e-input-group.e-warning::before, .e-input-group.e-control-wrapper.e-warning::before {
  content: '';
}

.e-float-input input[disabled], .e-float-input input.e-disabled, .e-float-input.e-control-wrapper input[disabled], .e-float-input.e-control-wrapper input.e-disabled {
  background: transparent;
  background-image: none;
  cursor: not-allowed;
}

.e-input-group.e-corner.e-rtl input.e-input:only-child, .e-input-group.e-control-wrapper.e-corner.e-rtl input.e-input:only-child {
  border-radius: 0;
}

.e-input-group.e-rtl .e-input:not(:first-child):focus, .e-input-group.e-control-wrapper.e-rtl .e-input:not(:first-child):focus {
  border-right-width: 0;
}

.e-input-group input.e-input, .e-input-group.e-control-wrapper input.e-input {
  min-width: 0;
  width: 100%;
}

.e-input-group input.e-input, .e-input-group textarea.e-input, .e-input-group input.e-input:hover:not(.e-success):not(.e-warning):not(.e-error):not([disabled]):not(:focus), .e-input-group textarea.e-input:hover:not(.e-success):not(.e-warning):not(.e-error):not([disabled]), .e-input-group.e-control-wrapper input.e-input, .e-input-group.e-control-wrapper textarea.e-input, .e-input-group.e-control-wrapper input.e-input:hover:not(.e-success):not(.e-warning):not(.e-error):not([disabled]):not(:focus), .e-input-group.e-control-wrapper textarea.e-input:hover:not(.e-success):not(.e-warning):not(.e-error):not([disabled]) {
  border: 0 solid;
  border-width: 0;
}

.e-input-group input.e-input, .e-input-group textarea.e-input, .e-input-group.e-bigger .e-input, .e-input-group.e-control-wrapper input.e-input, .e-input-group.e-control-wrapper textarea.e-input, .e-input-group.e-control-wrapper.e-bigger .e-input, .e-bigger .e-input-group .e-input, .e-bigger .e-input-group.e-control-wrapper .e-input {
  margin-bottom: 0;
}

.e-input-group::before, .e-input-group::after, .e-input-group.e-control-wrapper::before, .e-input-group.e-control-wrapper::after {
  content: '';
}

.e-input-group::before, .e-input-group.e-control-wrapper::before {
  content: '';
}

.e-input-group.e-input-focus::before, .e-input-group.e-input-focus::after, .e-input-group.e-control-wrapper.e-input-focus::before, .e-input-group.e-control-wrapper.e-input-focus::after {
  content: '';
}

.e-input-group::after, .e-input-group.e-control-wrapper::after {
  content: '';
}

.e-input-group, .e-input-group.e-control-wrapper {
  position: relative;
  width: 100%;
}

.e-input-group .e-input-group-icon:hover, .e-input-group.e-rtl.e-corner .e-input-group-icon:hover, .e-input-group.e-control-wrapper .e-input-group-icon:hover, .e-input-group.e-control-wrapper.e-rtl.e-corner .e-input-group-icon:hover {
  border-radius: 0;
}

.e-input.e-css.e-small, .e-input-group.e-small, .e-input-group.e-control-wrapper.e-small {
  margin-bottom: 0;
}

.e-input.e-css.e-small.e-bigger, .e-input-group.e-small.e-bigger, .e-input-group.e-control-wrapper.e-small.e-bigger, .e-bigger .e-input.e-css.e-small, .e-bigger .e-input-group.e-small, .e-bigger .e-input-group.e-control-wrapper.e-small {
  line-height: inherit;
  margin-bottom: 0;
}

.e-input-group .e-input-group-icon, .e-input-group.e-control-wrapper .e-input-group-icon {
  margin-bottom: 0;
  margin-right: 0;
  margin-top: 0;
}

.e-float-input.e-input-group .e-input-group-icon, .e-float-input.e-control-wrapper.e-input-group .e-input-group-icon {
  margin-top: 0;
}

.e-input-group.e-bigger .e-input-group-icon, .e-input-group .e-input-group-icon.e-bigger, .e-input-group.e-control-wrapper.e-bigger .e-input-group-icon, .e-input-group.e-control-wrapper .e-input-group-icon.e-bigger, .e-bigger .e-input-group .e-input-group-icon, .e-bigger .e-input-group.e-control-wrapper .e-input-group-icon {
  margin-bottom: 0;
  margin-right: 0;
  margin-top: 0;
}

.e-float-input.e-input-group.e-bigger .e-input-group-icon, .e-float-input.e-input-group .e-input-group-icon.e-bigger, .e-bigger .e-float-input.e-input-group .e-input-group-icon, .e-float-input.e-control-wrapper.e-input-group.e-bigger .e-input-group-icon, .e-float-input.e-control-wrapper.e-input-group .e-input-group-icon.e-bigger, .e-bigger .e-float-input.e-control-wrapper.e-input-group .e-input-group-icon {
  margin-top: 0;
}

.e-input-group.e-small.e-bigger .e-input-group-icon, .e-input-group.e-small .e-input-group-icon.e-bigger, .e-input-group.e-control-wrapper.e-small.e-bigger .e-input-group-icon, .e-input-group.e-control-wrapper.e-small .e-input-group-icon.e-bigger, .e-bigger .e-input-group.e-small .e-input-group-icon, .e-bigger .e-input-group.e-control-wrapper.e-small .e-input-group-icon {
  margin-bottom: 0;
  margin-right: 0;
  margin-top: 0;
}

.e-float-input.e-input-group.e-small.e-bigger .e-input-group-icon, .e-float-input.e-input-group.e-small .e-input-group-icon.e-bigger, .e-bigger .e-float-input.e-input-group.e-small .e-input-group-icon, .e-float-input.e-control-wrapper.e-input-group.e-small.e-bigger .e-input-group-icon, .e-float-input.e-control-wrapper.e-input-group.e-small .e-input-group-icon.e-bigger, .e-bigger .e-float-input.e-control-wrapper.e-input-group.e-small .e-input-group-icon {
  margin-top: 0;
}

.e-input-group.e-small .e-input-group-icon, .e-input-group.e-control-wrapper.e-small .e-input-group-icon, .e-small .e-input-group .e-input-group-icon, .e-small .e-input-group.e-control-wrapper .e-input-group-icon {
  margin-bottom: 0;
  margin-right: 0;
  margin-top: 0;
}

.e-float-input.e-input-group.e-small .e-input-group-icon, .e-small .e-float-input.e-input-group .e-input-group-icon, .e-float-input.e-control-wrapper.e-input-group.e-small .e-input-group-icon, .e-small .e-float-input.e-control-wrapper.e-input-group .e-input-group-icon {
  margin-top: 0;
}

.e-input-group .e-input-group-icon:last-child, .e-input-group.e-bigger .e-input-group-icon:last-child, .e-input-group .e-input-group-icon.e-bigger:last-child, .e-bigger .e-input-group .e-input-group-icon:last-child, .e-input-group.e-small .e-input-group-icon:last-child, .e-input-group.e-small.e-bigger .e-input-group-icon:last-child, .e-input-group.e-small .e-input-group-icon.e-bigger:last-child, .e-input-group.e-control-wrapper .e-input-group-icon:last-child, .e-input-group.e-control-wrapper.e-bigger .e-input-group-icon:last-child, .e-input-group.e-control-wrapper .e-input-group-icon.e-bigger:last-child, .e-input-group.e-control-wrapper.e-small .e-input-group-icon:last-child, .e-input-group.e-control-wrapper.e-small.e-bigger .e-input-group-icon:last-child, .e-input-group.e-control-wrapper.e-small .e-input-group-icon.e-bigger:last-child, .e-bigger .e-input-group.e-control-wrapper.e-small .e-input-group-icon:last-child, .e-bigger .e-input-group.e-small .e-input-group-icon:last-child {
  margin-right: 0;
}

.e-input-group, .e-input-group.e-control-wrapper {
  border-bottom: 1px solid;
}

.e-input-group, .e-input-group.e-success, .e-input-group.e-warning, .e-input-group.e-error, .e-input-group.e-control-wrapper, .e-input-group.e-control-wrapper.e-success, .e-input-group.e-control-wrapper.e-warning, .e-input-group.e-control-wrapper.e-error {
  border: 1px solid;
  border-width: 1px;
}

.e-input-group.e-rtl.e-input-focus:not(.e-success):not(.e-warning):not(.e-error) .e-input:not(:first-child):focus, .e-input-group.e-control-wrapper.e-rtl.e-input-focus:not(.e-success):not(.e-warning):not(.e-error) .e-input:not(:first-child):focus {
  border-right-width: 0;
}

.e-input-group.e-input-focus.e-corner, .e-input-group.e-control-wrapper.e-input-focus.e-corner {
  border-radius: 0;
}

.e-input-group:not(.e-success):not(.e-warning):not(.e-error):not(.e-float-icon-left).e-disabled, .e-input-group.e-float-icon-left:not(.e-success):not(.e-warning):not(.e-error).e-disabled .e-input-in-wrap, .e-input-group.e-control-wrapper:not(.e-success):not(.e-warning):not(.e-error):not(.e-float-icon-left).e-disabled, .e-input-group.e-control-wrapper.e-float-icon-left:not(.e-success):not(.e-warning):not(.e-error).e-disabled .e-input-in-wrap {
  background: #333232;
  color: #6f6c6c;
}

.e-input-group:not(.e-success):not(.e-warning):not(.e-error).e-disabled, .e-input-group.e-control-wrapper:not(.e-success):not(.e-warning):not(.e-error).e-disabled {
  border-style: solid;
}

.e-float-custom-tag, .e-float-custom-tag.e-control-wrapper {
  display: inline-block;
}

.e-float-custom-tag.e-input-group, .e-float-custom-tag.e-input-group.e-control-wrapper, .e-input-custom-tag, .e-input-custom-tag.e-input-group, .e-input-custom-tag.e-input-group.e-control-wrapper {
  display: -ms-inline-flexbox;
  display: inline-flex;
  vertical-align: middle;
}

.e-input-group .e-input-group-icon, .e-input-group.e-control-wrapper .e-input-group-icon {
  content: '';
}

.e-input-group:not(.e-filled) .e-input-group-icon::after, .e-input-group.e-control-wrapper:not(.e-filled) .e-input-group-icon::after {
  content: '';
}

.e-input-group .e-input-group-icon.e-input-btn-ripple::after, .e-input-group.e-control-wrapper .e-input-group-icon.e-input-btn-ripple::after {
  content: '';
}

input.e-input.e-css::-ms-clear, .e-float-input input::-ms-clear, .e-float-input.e-control-wrapper input::-ms-clear {
  display: none;
}

.e-float-input.e-input-group .e-float-line, .e-float-input.e-input-group.e-control-wrapper .e-float-line, .e-float-input.e-control-wrapper.e-input-group .e-float-line, .e-float-input.e-control-wrapper.e-input-group.e-control-wrapper .e-float-line {
  bottom: -1px;
  position: absolute;
}

.e-float-input.e-input-group input, .e-float-input.e-input-group textarea, .e-float-input.e-input-group.e-control-wrapper input, .e-float-input.e-input-group.e-control-wrapper textarea {
  border: 0;
}

.e-float-input.e-input-group .e-float-line, .e-float-input.e-input-group .e-float-text, .e-float-input.e-input-group.e-control-wrapper .e-float-line, .e-float-input.e-input-group.e-control-wrapper .e-float-text {
  right: 0;
}

input.e-input.e-css::-webkit-input-placeholder, input.e-input.e-css:-moz-placeholder, input.e-input.e-css:-ms-input-placeholder, input.e-input.e-css::-moz-placeholder {
  font-size: 14px;
  font-style: normal;
}

textarea.e-input.e-css::-webkit-input-placeholder, textarea.e-input.e-css:-moz-placeholder, textarea.e-input.e-css:-ms-input-placeholder, textarea.e-input.e-css::-moz-placeholder {
  font-size: 14px;
  font-style: normal;
}

.e-bigger input.e-input.e-css::-webkit-input-placeholder, input.e-bigger.e-input.e-css::-webkit-input-placeholder, .e-bigger input.e-input.e-css:-moz-placeholder, input.e-bigger.e-input.e-css:-moz-placeholder, .e-bigger input.e-input.e-css:-ms-input-placeholder, input.e-bigger.e-input.e-css:-ms-input-placeholder, .e-bigger input.e-input.e-css::-moz-placeholder, input.e-bigger.e-input.e-css::-moz-placeholder {
  font-size: 15px;
  font-style: normal;
}

.e-bigger textarea.e-input.e-css::-webkit-input-placeholder, textarea.e-bigger.e-input.e-css::-webkit-input-placeholder, .e-bigger textarea.e-input.e-css:-moz-placeholder, textarea.e-bigger.e-input.e-css:-moz-placeholder, .e-bigger textarea.e-input.e-css:-ms-input-placeholder, textarea.e-bigger.e-input.e-css:-ms-input-placeholder, .e-bigger textarea.e-input.e-css::-moz-placeholder, textarea.e-bigger.e-input.e-css::-moz-placeholder {
  font-size: 15px;
  font-style: normal;
}

.e-small input.e-input.e-css::-webkit-input-placeholder, input.e-small.e-input.e-css::-webkit-input-placeholder, .e-small input.e-input.e-css:-moz-placeholder, input.e-small.e-input.e-css:-moz-placeholder, .e-small input.e-input.e-css:-ms-input-placeholder, input.e-small.e-input.e-css:-ms-input-placeholder, .e-small input.e-input.e-css::-moz-placeholder, input.e-small.e-input.e-css::-moz-placeholder {
  font-size: 13px;
  font-style: normal;
}

.e-small textarea.e-input.e-css::-webkit-input-placeholder, textarea.e-small.e-input.e-css::-webkit-input-placeholder, .e-small textarea.e-input.e-css:-moz-placeholder, textarea.e-small.e-input.e-css:-moz-placeholder, .e-small textarea.e-input.e-css:-ms-input-placeholder, textarea.e-small.e-input.e-css:-ms-input-placeholder, .e-small textarea.e-input.e-css::-moz-placeholder, textarea.e-small.e-input.e-css::-moz-placeholder {
  font-size: 13px;
  font-style: normal;
}

.e-bigger input.e-small.e-input.e-css::-webkit-input-placeholder, .e-small input.e-bigger.e-input.e-css::-webkit-input-placeholder, .e-bigger input.e-small.e-input.e-css:-moz-placeholder, .e-small input.e-bigger.e-input.e-css:-moz-placeholder, .e-bigger input.e-small.e-input.e-css:-ms-input-placeholder, .e-small input.e-bigger.e-input.e-css:-ms-input-placeholder, .e-bigger input.e-small.e-input.e-css::-moz-placeholder, .e-small input.e-bigger.e-input.e-css::-moz-placeholder {
  font-size: 14px;
  font-style: normal;
}

.e-bigger textarea.e-small.e-input.e-css::-webkit-input-placeholder, .e-small textarea.e-bigger.e-input.e-css::-webkit-input-placeholder, .e-bigger textarea.e-small.e-input.e-css:-moz-placeholder, .e-small textarea.e-bigger.e-input.e-css:-moz-placeholder, .e-bigger intextareaput.e-small.e-input.e-css:-ms-input-placeholder, .e-small textarea.e-bigger.e-input.e-css:-ms-input-placeholder, .e-bigger textarea.e-small.e-input.e-css::-moz-placeholder, .e-small textarea.e-bigger.e-input.e-css::-moz-placeholder {
  font-size: 14px;
  font-style: normal;
}

input.e-input.e-css:-moz-placeholder {
  font-style: normal;
  -moz-user-select: none;
       user-select: none;
}

textarea.e-input.e-css:-moz-placeholder {
  font-style: normal;
  -moz-user-select: none;
       user-select: none;
}

.e-input-group input.e-input:-moz-placeholder {
  font-style: normal;
  -moz-user-select: none;
       user-select: none;
}

.e-input-group textarea.e-input:-moz-placeholder {
  font-style: normal;
  -moz-user-select: none;
       user-select: none;
}

.e-input-group.e-control-wrapper input.e-input:-moz-placeholder {
  font-style: normal;
  -moz-user-select: none;
       user-select: none;
}

.e-input-group.e-control-wrapper textarea.e-input:-moz-placeholder {
  font-style: normal;
  -moz-user-select: none;
       user-select: none;
}

input.e-input.e-css:-moz-placeholder {
  font-style: normal;
  -moz-user-select: none;
       user-select: none;
}

.e-input-group input.e-input:-moz-placeholder {
  font-style: normal;
  -moz-user-select: none;
       user-select: none;
}

.e-input-group.e-control-wrapper input.e-input:-moz-placeholder {
  font-style: normal;
  -moz-user-select: none;
       user-select: none;
}

textarea.e-input.e-css:-moz-placeholder {
  font-style: normal;
  -moz-user-select: none;
       user-select: none;
}

input.e-input.e-css::-moz-placeholder {
  font-style: normal;
  -moz-user-select: none;
       user-select: none;
}

textarea.e-input.e-css::-moz-placeholder {
  font-style: normal;
  -moz-user-select: none;
       user-select: none;
}

input.e-input.e-css:-ms-input-placeholder {
  font-style: normal;
}

textarea.e-input.e-css:-ms-input-placeholder {
  font-style: normal;
}

input.e-input.e-css::-webkit-input-placeholder {
  font-style: normal;
  -webkit-user-select: none;
          user-select: none;
}

textarea.e-input.e-css::-webkit-input-placeholder {
  font-style: normal;
  -webkit-user-select: none;
          user-select: none;
}

input.e-input.e-css, .e-input-group input, .e-input-group.e-control-wrapper input, .e-input-group input.e-input, .e-input-group.e-control-wrapper input.e-input {
  box-sizing: border-box;
  height: 30px;
}

.e-float-input:not(.e-input-group) input, .e-float-input.e-control-wrapper:not(.e-input-group) input {
  box-sizing: border-box;
  height: 32px;
}

input.e-input.e-bigger.e-css, .e-input-group.e-bigger input, .e-input-group.e-bigger input.e-input, .e-input-group.e-control-wrapper.e-bigger input, .e-input-group.e-control-wrapper.e-bigger input.e-input, .e-bigger .e-input-group input, .e-bigger .e-input-group input.e-input, .e-bigger .e-input-group.e-control-wrapper input, .e-bigger .e-input-group.e-control-wrapper input.e-input, .e-float-input.e-bigger input, .e-float-input.e-bigger input.e-input, .e-bigger .e-float-input input, .e-bigger .e-float-input input.e-input, .e-float-input.e-control-wrapper.e-bigger input, .e-float-input.e-control-wrapper.e-bigger input.e-input, .e-bigger .e-float-input.e-control-wrapper input, .e-bigger .e-float-input.e-control-wrapper input.e-input {
  box-sizing: border-box;
  height: 38px;
}

.e-float-input.e-bigger:not(.e-input-group) input, .e-float-input.e-bigger:not(.e-input-group) input.e-input, .e-bigger .e-float-input:not(.e-input-group) input, .e-bigger .e-float-input:not(.e-input-group) input.e-input, .e-float-input.e-control-wrapper.e-bigger:not(.e-input-group) input, .e-float-input.e-control-wrapper.e-bigger:not(.e-input-group) input.e-input, .e-bigger .e-float-input.e-control-wrapper:not(.e-input-group) input, .e-bigger .e-float-input.e-control-wrapper:not(.e-input-group) input.e-input {
  box-sizing: border-box;
  height: 40px;
}

input.e-input.e-small.e-css, .e-input-group.e-small input, .e-input-group.e-small input.e-input, .e-small .e-input-group input, .e-small .e-input-group input.e-input, .e-input-group.e-control-wrapper.e-small input, .e-input-group.e-control-wrapper.e-small input.e-input, .e-small .e-input-group.e-control-wrapper input, .e-small .e-input-group.e-control-wrapper input.e-input, .e-float-input.e-small input, .e-float-input.e-small input.e-input, .e-small .e-float-input input, .e-small .e-float-input input.e-input, .e-float-input.e-control-wrapper.e-small input, .e-float-input.e-control-wrapper.e-small input.e-input, .e-small .e-float-input.e-control-wrapper input, .e-small .e-float-input.e-control-wrapper input.e-input {
  box-sizing: border-box;
  height: 24px;
}

.e-float-input.e-small:not(.e-input-group) input, .e-float-input.e-small:not(.e-input-group) input.e-input, .e-small .e-float-input:not(.e-input-group) input, .e-small .e-float-input:not(.e-input-group) input.e-input
.e-float-input.e-control-wrapper.e-small:not(.e-input-group) input, .e-float-input.e-control-wrapper.e-small:not(.e-input-group) input.e-input, .e-small .e-float-input.e-control-wrapper:not(.e-input-group) input, .e-small .e-float-input.e-control-wrapper:not(.e-input-group) input.e-input {
  box-sizing: border-box;
  height: 26px;
}

input.e-input.e-small.e-bigger.e-css, .e-input-group.e-bigger.e-small input, .e-input-group.e-bigger.e-small input.e-input, .e-bigger.e-small .e-input-group input, .e-bigger.e-small .e-input-group input.e-input, .e-input-group.e-control-wrapper.e-bigger.e-small input, .e-input-group.e-control-wrapper.e-bigger.e-small input.e-input, .e-bigger.e-small .e-input-group.e-control-wrapper input, .e-bigger.e-small .e-input-group.e-control-wrapper input.e-input, .e-float-input.e-bigger.e-small input, .e-float-input.e-bigger.e-small input.e-input, .e-bigger.e-small .e-float-input input, .e-bigger.e-small .e-float-input input.e-input, .e-float-input.e-control-wrapper.e-bigger.e-small input, .e-float-input.e-control-wrapper.e-bigger.e-small input.e-input, .e-bigger.e-small .e-float-input.e-control-wrapper input, .e-bigger.e-small .e-float-input.e-control-wrapper input.e-input {
  box-sizing: border-box;
  height: 34px;
}

.e-float-input.e-bigger.e-small:not(.e-input-group) input, .e-float-input.e-bigger.e-small:not(.e-input-group) input.e-input, .e-bigger.e-small .e-float-input:not(.e-input-group) input, .e-bigger.e-small .e-float-input:not(.e-input-group) input.e-input, .e-float-input.e-control-wrapper.e-bigger.e-small:not(.e-input-group) input, .e-float-input.e-control-wrapper.e-bigger.e-small:not(.e-input-group) input.e-input, .e-bigger.e-small .e-float-input.e-control-wrapper:not(.e-input-group) input, .e-bigger.e-small .e-float-input.e-control-wrapper:not(.e-input-group) input.e-input {
  box-sizing: border-box;
  height: 36px;
}

textarea.e-input.e-css, .e-input-group textarea, .e-input-group.e-control-wrapper textarea, .e-float-input textarea, .e-float-input.e-control-wrapper textarea {
  box-sizing: border-box;
  height: auto;
}

.e-input-group textarea.e-input.e-bigger, .e-input-group.e-control-wrapper textarea.e-input.e-bigger, textarea.e-input.e-bigger.e-css, .e-input-group.e-bigger textarea, .e-input-group.e-bigger textarea.e-input, .e-bigger .e-input-group textarea, .e-bigger .e-input-group textarea.e-input, .e-input-group.e-control-wrapper.e-bigger textarea, .e-input-group.e-control-wrapper.e-bigger textarea.e-input, .e-bigger .e-input-group.e-control-wrapper textarea, .e-bigger .e-input-group.e-control-wrapper textarea.e-input, .e-float-input.e-bigger textarea, .e-float-input.e-bigger textarea.e-input, .e-bigger .e-float-input textarea, .e-bigger .e-float-input textarea.e-input, .e-float-input.e-control-wrapper.e-bigger textarea, .e-float-input.e-control-wrapper.e-bigger textarea.e-input, .e-bigger .e-float-input.e-control-wrapper textarea, .e-bigger .e-float-input.e-control-wrapper textarea.e-input {
  box-sizing: border-box;
  height: auto;
}

textarea.e-input.e-small.e-css, .e-input-group.e-small textarea, .e-input-group.e-small textarea.e-input, .e-small .e-input-group textarea, .e-small .e-input-group textarea.e-input, .e-input-group.e-control-wrapper.e-small textarea, .e-input-group.e-control-wrapper.e-small textarea.e-input, .e-small .e-input-group.e-control-wrapper textarea, .e-small .e-input-group.e-control-wrapper textarea.e-input, .e-float-input.e-small textarea, .e-float-input.e-small textarea.e-input, .e-small .e-float-input textarea, .e-small .e-float-input textarea.e-input, .e-float-input.e-control-wrapper.e-small textarea, .e-float-input.e-control-wrapper.e-small textarea.e-input, .e-small .e-float-input.e-control-wrapper textarea, .e-small .e-float-input.e-control-wrapper textarea.e-input {
  box-sizing: border-box;
  height: auto;
}

textarea.e-input.e-small.e-bigger.e-css, .e-input-group.e-bigger.e-small textarea, .e-input-group.e-bigger.e-small textarea.e-input, .e-bigger.e-small .e-input-group textarea, .e-bigger.e-small .e-input-group textarea.e-input, .e-input-group.e-control-wrapper.e-bigger.e-small textarea, .e-input-group.e-control-wrapper.e-bigger.e-small textarea.e-input, .e-bigger.e-small .e-input-group.e-control-wrapper textarea, .e-bigger.e-small .e-input-group.e-control-wrapper textarea.e-input, .e-float-input.e-bigger.e-small textarea, .e-float-input.e-bigger.e-small textarea.e-input, .e-bigger.e-small .e-float-input textarea, .e-bigger.e-small .e-float-input textarea.e-input, .e-float-input.e-control-wrapper.e-bigger.e-small textarea, .e-float-input.e-control-wrapper.e-bigger.e-small textarea.e-input, .e-bigger.e-small .e-float-input.e-control-wrapper textarea, .e-bigger.e-small .e-float-input.e-control-wrapper textarea.e-input {
  box-sizing: border-box;
  height: auto;
}

input.e-input.e-bigger.e-css, .e-input-group input.e-input.e-bigger, .e-input-group input.e-input.e-bigger, .e-input-group.e-control-wrapper input.e-input.e-bigger, .e-input-group.e-control-wrapper input.e-input.e-bigger, .e-input-group.e-bigger .e-input, .e-input-group.e-control-wrapper.e-bigger .e-input, .e-bigger input.e-input.e-css, .e-bigger .e-input-group .e-input, .e-bigger .e-input-group.e-control-wrapper .e-input, .e-float-input.e-bigger input, .e-float-input input.e-bigger, .e-bigger .e-float-input input, .e-float-input.e-control-wrapper.e-bigger input, .e-float-input.e-control-wrapper input.e-bigger, .e-bigger .e-float-input.e-control-wrapper input {
  text-indent: 12px;
}

input.e-input.e-css.e-small, .e-input-group input.e-input.e-small, .e-input-group.e-control-wrapper input.e-input.e-small, .e-input-group.e-small .e-input, .e-input-group.e-control-wrapper.e-small .e-input, .e-small input.e-input.e-css, .e-small .e-input-group .e-input, .e-small .e-input-group.e-control-wrapper .e-input, .e-float-input.e-small input, .e-float-input input.e-small, .e-small .e-float-input input, .e-float-input.e-control-wrapper.e-small input, .e-float-input.e-control-wrapper input.e-small, .e-small .e-float-input.e-control-wrapper input {
  text-indent: 10px;
}

textarea.e-input.e-css.e-small, .e-input-group textarea.e-input.e-small, .e-input-group.e-control-wrapper input.e-input-group textarea.e-input.e-small, .e-small input.e-input.e-css, .e-float-input.e-small textarea, .e-float-input textarea.e-small, .e-small .e-float-input textarea, .e-float-input.e-control-wrapper.e-small textarea, .e-float-input.e-control-wrapper textarea.e-small, .e-small .e-float-input.e-control-wrapper textarea {
  text-indent: 10px;
}

input.e-input.e-css, .e-input-group input.e-input, .e-input-group input, .e-input-group.e-control-wrapper input.e-input, .e-input-group.e-control-wrapper input, .e-float-input input.e-input, .e-float-input input, .e-float-input.e-control-wrapper input.e-input, .e-float-input.e-control-wrapper input, .e-input-group input.e-input:focus, .e-input-group.e-control-wrapper input.e-input:focus, .e-float-input.e-control-wrapper input:focus, .e-float-input input:focus {
  padding-left: 10px;
  text-indent: 0;
}

textarea.e-input.e-css, .e-input-group textarea.e-input, .e-input-group textarea, .e-input-group.e-control-wrapper textarea.e-input, .e-input-group.e-control-wrapper textarea, .e-float-input textarea.e-input, .e-float-input textarea, .e-float-input.e-control-wrapper textarea.e-input, .e-float-input.e-control-wrapper textarea, .e-input-group textarea.e-input:focus, .e-input-group.e-control-wrapper textarea.e-input:focus, .e-float-input.e-control-wrapper textarea:focus, .e-float-input textarea:focus {
  padding-left: 10px;
  text-indent: 0;
}

input.e-input.e-rtl.e-css, .e-input-group.e-rtl input.e-input, .e-input-group.e-control-wrapper.e-rtl input.e-input, .e-float-input.e-rtl input, .e-float-input.e-control-wrapper.e-rtl input, .e-rtl .e-input-group input.e-input, .e-rtl .e-input-group.e-control-wrapper input.e-input, .e-rtl .e-float-input input, .e-rtl .e-float-input.e-control-wrapper input, .e-input-group.e-rtl input.e-input, .e-input-group.e-control-wrapper.e-rtl input.e-input, .e-float-input.e-rtl input, .e-float-input.e-control-wrapper.e-rtl input, .e-rtl .e-input-group input.e-input, .e-rtl .e-input-group.e-control-wrapper input.e-input, .e-rtl .e-float-input input, .e-rtl .e-float-input.e-control-wrapper input, .e-input-group.e-rtl input.e-input:focus, .e-input-group.e-control-wrapper.e-rtl input.e-input:focus, .e-float-input.e-rtl input:focus, .e-float-input.e-control-wrapper.e-rtl input:focus, .e-rtl .e-input-group input.e-input:focus, .e-rtl .e-input-group.e-control-wrapper input.e-input:focus, .e-rtl .e-float-input input:focus, .e-rtl .e-float-input.e-control-wrapper input:focus {
  padding-left: 0;
  padding-right: 10px;
  text-indent: 0;
}

textarea.e-input.e-rtl.e-css, .e-input-group.e-rtl textarea.e-input, .e-input-group.e-control-wrapper.e-rtl textarea.e-input, .e-float-input.e-rtl textarea, .e-float-input.e-control-wrapper.e-rtl textarea, .e-rtl .e-input-group textarea.e-input, .e-rtl .e-input-group.e-control-wrapper textarea.e-input, .e-rtl .e-float-input textarea, .e-rtl .e-float-input.e-control-wrapper textarea, .e-input-group.e-rtl textarea.e-input, .e-input-group.e-control-wrapper.e-rtl textarea.e-input, .e-float-input.e-rtl textarea, .e-float-input.e-control-wrapper.e-rtl textarea, .e-rtl .e-input-group textarea.e-input, .e-rtl .e-input-group.e-control-wrapper textarea.e-input, .e-rtl .e-float-input textarea, .e-rtl .e-float-input.e-control-wrapper textarea, .e-input-group.e-rtl textarea.e-input:focus, .e-input-group.e-control-wrapper.e-rtl textarea.e-input:focus, .e-float-input.e-rtl textarea:focus, .e-float-input.e-control-wrapper.e-rtl textarea:focus, .e-rtl .e-input-group textarea.e-input:focus, .e-rtl .e-input-group.e-control-wrapper textarea.e-input:focus, .e-rtl .e-float-input textarea:focus, .e-rtl .e-float-input.e-control-wrapper textarea:focus {
  padding-right: 10px;
  text-indent: 0;
}

input.e-input.e-small.e-css, .e-small input.e-input.e-css, .e-input-group.e-small input.e-input, .e-input-group.e-control-wrapper.e-small input.e-input, .e-float-input.e-small input, .e-float-input.e-control-wrapper input.e-small, .e-float-input.e-small input, .e-float-input.e-control-wrapper input.e-small, .e-input-group input.e-input.e-small, .e-input-group.e-control-wrapper input.e-input.e-small, .e-small .e-float-input input, .e-small .e-float-input.e-control-wrapper input, .e-small .e-input-group input.e-input, .e-small .e-input-group.e-control-wrapper input.e-input, .e-input-group.e-small input.e-input:focus, .e-input-group.e-control-wrapper.e-small input.e-input:focus, .e-float-input.e-small input:focus, .e-float-input.e-control-wrapper.e-small input:focus, .e-small .e-input-group.e-control-wrapper input.e-input:focus, .e-small .e-input-group input.e-input:focus, .e-small .e-float-input input:focus, .e-small .e-float-input.e-control-wrapper input:focus {
  padding-left: 10px;
  text-indent: 0;
}

textarea.e-input.e-small.e-css, .e-small textarea.e-input.e-css, .e-input-group.e-small textarea.e-input, .e-input-group.e-control-wrapper.e-small textarea.e-input, .e-float-input.e-control-wrapper.e-small textarea, .e-float-input.e-control-wrapper textarea.e-small, .e-float-input.e-small textarea, .e-float-input textarea.e-small, .e-input-group textarea.e-input.e-small, .e-input-group.e-control-wrapper textarea.e-input.e-small, .e-small .e-float-input.e-control-wrapper textarea, .e-small .e-float-input textarea, .e-small .e-input-group textarea.e-input, .e-small .e-input-group.e-control-wrapper textarea.e-input, .e-input-group.e-small textarea.e-input:focus, .e-input-group.e-control-wrapper.e-small textarea.e-input:focus, .e-float-input.e-small textarea:focus, .e-float-input.e-control-wrapper.e-small textarea:focus, .e-small .e-input-group textarea.e-input:focus, .e-small .e-input-group.e-control-wrapper textarea.e-input:focus, .e-small .e-float-input.e-control-wrapper textarea:focus, .e-small .e-float-input textarea:focus {
  padding-left: 10px;
  text-indent: 0;
}

.e-rtl input.e-input.e-small.e-css, input.e-input.e-css.e-small.e-rtl, .e-small.e-rtl input.e-input.e-css, .e-small input.e-input.e-rtl.e-css, .e-float-input.e-control-wrapper.e-small.e-rtl input, .e-float-input.e-small.e-rtl input, .e-input-group.e-small.e-rtl input.e-input, .e-input-group.e-control-wrapper.e-small.e-rtl input.e-input, .e-rtl .e-float-input.e-small input, .e-rtl .e-float-input.e-control-wrapper.e-small input, .e-rtl .e-input-group.e-small input.e-input, .e-rtl .e-input-group.e-control-wrapper.e-small input.e-input, .e-float-input.e-rtl input.e-small, .e-float-input.e-control-wrapper.e-rtl input.e-small, .e-input-group.e-rtl input.e-input.e-small, .e-input-group.e-control-wrapper.e-rtl input.e-input.e-small, .e-rtl .e-float-input input.e-small, .e-rtl .e-float-input.e-control-wrapper input.e-small, .e-rtl .e-input-group input.e-input.e-small, .e-rtl .e-input-group.e-control-wrapper input.e-input.e-small, .e-small .e-float-input.e-rtl input, .e-small .e-float-input.e-control-wrapper.e-rtl input, .e-small .e-input-group.e-rtl input.e-input, .e-small .e-input-group.e-control-wrapper.e-rtl input.e-input, .e-small.e-rtl .e-float-input.e-control-wrapper input, .e-small.e-rtl .e-float-input input, .e-small.e-rtl .e-input-group.e-control-wrapper input.e-input, .e-small.e-rtl .e-input-group input.e-input, .e-small.e-rtl .e-input-group.e-control-wrapper input.e-input:focus, .e-small.e-rtl .e-input-group input.e-input:focus, .e-small.e-rtl .e-float-input.e-control-wrapper input:focus, .e-small.e-rtl .e-float-input input:focus, .e-small .e-input-group.e-control-wrapper.e-rtl input.e-input:focus, .e-small .e-input-group.e-rtl input.e-input:focus, .e-small .e-float-input.e-control-wrapper.e-rtl input:focus, .e-small .e-float-input.e-rtl input:focus {
  padding-left: 0;
  padding-right: 10px;
  text-indent: 0;
}

.e-rtl textarea.e-input.e-small.e-css, textarea.e-input.e-small.e-rtl.e-css, .e-small.e-rtl textarea.e-input.e-css, .e-small textarea.e-input.e-rtl.e-css, .e-float-input.e-small.e-rtl textarea, .e-float-input.e-control-wrapper.e-small.e-rtl textarea, .e-input-group.e-small.e-rtl textarea.e-input, .e-input-group.e-control-wrapper.e-small.e-rtl textarea.e-input, .e-rtl .e-float-input.e-control-wrapper.e-small textarea, .e-rtl .e-float-input.e-small textarea, .e-rtl .e-input-group.e-small textarea.e-input, .e-rtl .e-input-group.e-control-wrapper.e-small textarea.e-input, .e-float-input.e-control-wrapper.e-rtl textarea.e-small, .e-float-input.e-rtl textarea.e-small, .e-input-group.e-rtl textarea.e-input.e-small, .e-input-group.e-control-wrapper.e-rtl textarea.e-input.e-small, .e-rtl .e-float-input.e-control-wrapper textarea.e-small, .e-rtl .e-float-input textarea.e-small, .e-rtl .e-input-group textarea.e-input.e-small, .e-rtl .e-input-group.e-control-wrapper textarea.e-input.e-small, .e-small .e-float-input.e-control-wrapper.e-rtl textarea, .e-small .e-float-input.e-rtl textarea, .e-small .e-input-group.e-rtl textarea.e-input, .e-small .e-input-group.e-control-wrapper.e-rtl textarea.e-input, .e-small.e-rtl .e-float-input.e-control-wrapper textarea, .e-small.e-rtl .e-float-input textarea, .e-small.e-rtl .e-input-group textarea.e-input, .e-small.e-rtl .e-input-group.e-control-wrapper textarea.e-input, .e-small.e-rtl .e-input-group textarea.e-input:focus, .e-small.e-rtl .e-input-group.e-control-wrapper textarea.e-input:focus, .e-small.e-rtl .e-float-input.e-control-wrapper textarea:focus, .e-small.e-rtl .e-float-input textarea:focus, .e-small .e-input-group.e-rtl textarea.e-input:focus, .e-small .e-input-group.e-control-wrapper.e-rtl textarea.e-input:focus, .e-small .e-float-input.e-control-wrapper.e-rtl textarea:focus, .e-small .e-float-input.e-rtl textarea:focus {
  padding-right: 10px;
  text-indent: 0;
}

input.e-input.e-bigger.e-css, .e-bigger input.e-input.e-css, .e-input-group.e-bigger input.e-input, .e-input-group.e-control-wrapper.e-bigger input.e-input, .e-float-input.e-control-wrapper.e-bigger input, .e-float-input.e-control-wrapper input.e-bigger, .e-float-input.e-bigger input, .e-float-input input.e-bigger, .e-input-group input.e-input.e-bigger, .e-input-group.e-control-wrapper input.e-input.e-bigger, .e-bigger .e-float-input.e-control-wrapper input, .e-bigger .e-float-input input, .e-bigger .e-input-group.e-control-wrapper input.e-input, .e-bigger .e-input-group input.e-input, .e-input-group.e-bigger input.e-input:focus, .e-input-group.e-control-wrapper.e-bigger input.e-input:focus, .e-float-input.e-control-wrapper.e-bigger input:focus, .e-float-input.e-bigger input:focus, .e-bigger .e-input-group input.e-input:focus, .e-bigger .e-input-group.e-control-wrapper input.e-input:focus, .e-bigger .e-float-input.e-control-wrapper input:focus, .e-bigger .e-float-input input:focus {
  padding-left: 12px;
  text-indent: 0;
}

.e-rtl input.e-input.e-bigger.e-css, input.e-input.e-bigger.e-rtl.e-css, .e-bigger.e-rtl input.e-input.e-css, .e-bigger input.e-input.e-rtl.e-css, .e-float-input.e-control-wrapper.e-bigger.e-rtl input, .e-float-input.e-bigger.e-rtl input, .e-input-group.e-bigger.e-rtl input.e-input, .e-input-group.e-control-wrapper.e-bigger.e-rtl input.e-input, .e-rtl .e-float-input.e-control-wrapper.e-bigger input, .e-rtl .e-float-input.e-bigger input, .e-rtl .e-input-group.e-bigger input.e-input, .e-rtl .e-input-group.e-control-wrapper.e-bigger input.e-input, .e-float-input.e-control-wrapper.e-rtl input.e-bigger, .e-float-input.e-rtl input.e-bigger, .e-input-group.e-rtl input.e-input.e-bigger, .e-input-group.e-control-wrapper.e-rtl input.e-input.e-bigger, .e-rtl .e-float-input.e-control-wrapper input.e-bigger, .e-rtl .e-float-input input.e-bigger, .e-rtl .e-input-group input.e-input.e-bigger, .e-rtl .e-input-group.e-control-wrapper input.e-input.e-bigger, .e-bigger .e-float-input.e-control-wrapper.e-rtl input, .e-bigger .e-float-input.e-rtl input, .e-bigger .e-input-group.e-rtl input.e-input, .e-bigger .e-input-group.e-control-wrapper.e-rtl input.e-input, .e-bigger.e-rtl .e-float-input.e-control-wrapper input, .e-bigger.e-rtl .e-float-input input, .e-bigger.e-rtl .e-input-group input.e-input, .e-bigger.e-rtl .e-input-group.e-control-wrapper input.e-input, .e-bigger.e-rtl .e-input-group input.e-input:focus, .e-bigger.e-rtl .e-input-group.e-control-wrapper input.e-input:focus, .e-bigger.e-rtl .e-float-input.e-control-wrapper input:focus, .e-bigger.e-rtl .e-float-input input:focus, .e-bigger .e-input-group.e-rtl input.e-input:focus, .e-bigger .e-input-group.e-control-wrapper.e-rtl input.e-input:focus, .e-bigger .e-float-input.e-rtl input:focus, .e-bigger .e-float-input.e-control-wrapper.e-rtl input:focus {
  padding-left: 0;
  padding-right: 12px;
  text-indent: 0;
}

textarea.e-input.e-bigger.e-css, .e-bigger textarea.e-input.e-css, .e-input-group.e-bigger textarea.e-input, .e-input-group.e-control-wrapper.e-bigger textarea.e-input, .e-float-input.e-control-wrapper.e-bigger textarea, .e-float-input.e-control-wrapper textarea.e-bigger, .e-float-input.e-bigger textarea, .e-float-input textarea.e-bigger, .e-input-group textarea.e-input.e-bigger, .e-input-group.e-control-wrapper textarea.e-input.e-bigger, .e-bigger .e-float-input.e-control-wrapper textarea, .e-bigger .e-float-input textarea, .e-bigger .e-input-group textarea.e-input, .e-bigger .e-input-group.e-control-wrapper textarea.e-input, .e-input-group.e-bigger textarea.e-input:focus, .e-input-group.e-control-wrapper.e-bigger textarea.e-input:focus, .e-float-input.e-control-wrapper.e-bigger textarea:focus, .e-float-input.e-bigger textarea:focus, .e-bigger .e-input-group textarea.e-input:focus, .e-bigger .e-input-group.e-control-wrapper textarea.e-input:focus, .e-bigger .e-float-input.e-control-wrapper textarea:focus, .e-bigger .e-float-input textarea:focus {
  padding-left: 12px;
  text-indent: 0;
}

.e-rtl textarea.e-input.e-bigger.e-css, textarea.e-input.e-bigger.e-rtl.e-css, .e-bigger.e-rtl textarea.e-input.e-css, .e-bigger textarea.e-input.e-rtl.e-css, .e-float-input.e-control-wrapper.e-bigger.e-rtl textarea, .e-float-input.e-bigger.e-rtl textarea, .e-input-group.e-bigger.e-rtl textarea.e-input, .e-input-group.e-control-wrapper.e-bigger.e-rtl textarea.e-input, .e-rtl .e-float-input.e-control-wrapper.e-bigger textarea, .e-rtl .e-float-input.e-bigger textarea, .e-rtl .e-input-group.e-bigger textarea.e-input, .e-rtl .e-input-group.e-control-wrapper.e-bigger textarea.e-input, .e-float-input.e-rtl textarea.e-bigger, .e-float-input.e-control-wrapper.e-rtl textarea.e-bigger, .e-input-group.e-rtl textarea.e-input.e-bigger, .e-input-group.e-control-wrapper.e-rtl textarea.e-input.e-bigger, .e-rtl .e-float-input textarea.e-bigger, .e-rtl .e-float-input.e-control-wrapper textarea.e-bigger, .e-rtl .e-input-group textarea.e-input.e-bigger, .e-rtl .e-input-group.e-control-wrapper textarea.e-input.e-bigger, .e-bigger .e-float-input.e-rtl textarea, .e-bigger .e-float-input.e-control-wrapper.e-rtl textarea, .e-bigger .e-input-group.e-rtl textarea.e-input, .e-bigger .e-input-group.e-control-wrapper.e-rtl textarea.e-input, .e-bigger.e-rtl .e-float-input textarea, .e-bigger.e-rtl .e-float-input.e-control-wrapper textarea, .e-bigger.e-rtl .e-input-group textarea.e-input, .e-bigger.e-rtl .e-input-group.e-control-wrapper textarea.e-input, .e-bigger.e-rtl .e-input-group textarea.e-input:focus, .e-bigger.e-rtl .e-input-group.e-control-wrapper textarea.e-input:focus, .e-bigger.e-rtl .e-float-input textarea:focus, .e-bigger.e-rtl .e-float-input.e-control-wrapper textarea:focus, .e-bigger .e-input-group.e-rtl textarea.e-input:focus, .e-bigger .e-input-group.e-control-wrapper.e-rtl textarea.e-input:focus, .e-bigger .e-float-input.e-rtl textarea:focus, .e-bigger .e-float-input.e-control-wrapper.e-rtl textarea:focus {
  padding-right: 12px;
  text-indent: 0;
}

input.e-input.e-bigger.e-small.e-css, .e-bigger input.e-input.e-small.e-css, .e-input-group.e-small.e-bigger input.e-input, .e-input-group.e-control-wrapper.e-small.e-bigger input.e-input, .e-input-group.e-small input.e-input.e-bigger, .e-input-group.e-control-wrapper.e-small input.e-input.e-bigger, .e-bigger .e-input-group.e-small input.e-input, .e-bigger .e-input-group.e-control-wrapper.e-small input.e-input, .e-float-input.e-small.e-bigger input, .e-float-input.e-small input.e-bigger, .e-bigger .e-float-input.e-small input, .e-float-input.e-control-wrapper.e-small.e-bigger input, .e-float-input.e-control-wrapper.e-small input.e-bigger, .e-bigger .e-float-input.e-control-wrapper.e-small input, .e-bigger .e-input-group.e-small input.e-input:focus, .e-bigger .e-input-group.e-control-wrapper.e-small input.e-input:focus, .e-bigger .e-float-input.e-control-wrapper.e-small input:focus, .e-bigger .e-float-input.e-small input:focus, .e-small .e-input-group.e-bigger input.e-input:focus, .e-small .e-input-group.e-control-wrapper.e-bigger input.e-input:focus, .e-small .e-float-input.e-control-wrapper.e-bigger input:focus, .e-small .e-float-input.e-bigger input:focus, .e-input-group.e-bigger.e-small input.e-input:focus, .e-input-group.e-control-wrapper.e-bigger.e-small input.e-input:focus, .e-float-input.e-control-wrapper.e-bigger.e-small input:focus, .e-float-input.e-bigger.e-small input:focus {
  padding-left: 12px;
  text-indent: 0;
}

.e-bigger input.e-input.e-small.e-rtl.e-css, .e-input-group.e-small.e-bigger.e-rtl input.e-input, .e-input-group.e-control-wrapper.e-small.e-bigger.e-rtl input.e-input, .e-input-group.e-small.e-rtl input.e-input.e-bigger, .e-input-group.e-control-wrapper.e-small.e-rtl input.e-input.e-bigger, .e-bigger .e-input-group.e-small.e-rtl input.e-input, .e-bigger .e-input-group.e-control-wrapper.e-small.e-rtl input.e-input, .e-float-input.e-small.e-bigger.e-rtl input, .e-float-input.e-small.e-rtl input.e-bigger, .e-bigger .e-float-input.e-small.e-rtl input, .e-float-input.e-control-wrapper.e-small.e-bigger.e-rtl input, .e-float-input.e-control-wrapper.e-small.e-rtl input.e-bigger, .e-bigger .e-float-input.e-control-wrapper.e-small.e-rtl input, .e-bigger.e-rtl input.e-input.e-small, .e-rtl .e-input-group.e-small.e-bigger input.e-input, .e-rtl .e-input-group.e-control-wrapper.e-small.e-bigger input.e-input, .e-rtl .e-input-group.e-small input.e-input.e-bigger, .e-rtl .e-input-group.e-control-wrapper.e-small input.e-input.e-bigger, .e-bigger.e-rtl .e-input-group.e-small input.e-input, .e-bigger.e-rtl .e-input-group.e-control-wrapper.e-small input.e-input, .e-rtl .e-float-input.e-control-wrapper.e-small.e-bigger input, .e-rtl .e-float-input.e-control-wrapper.e-small input.e-bigger, .e-bigger.e-rtl .e-float-input.e-control-wrapper.e-small input, .e-rtl .e-float-input.e-small.e-bigger input, .e-rtl .e-float-input.e-small input.e-bigger, .e-bigger.e-rtl .e-float-input.e-small input, .e-bigger .e-input-group.e-small.e-rtl input.e-input:focus, .e-bigger .e-input-group.e-control-wrapper.e-small.e-rtl input.e-input:focus, .e-bigger .e-float-input.e-control-wrapper.e-small.e-rtl input:focus, .e-bigger .e-float-input.e-small.e-rtl input:focus, .e-small .e-input-group.e-bigger.e-rtl input.e-input:focus, .e-small .e-input-group.e-control-wrapper.e-bigger.e-rtl input.e-input:focus, .e-small .e-float-input.e-control-wrapper.e-bigger.e-rtl input:focus, .e-small .e-float-input.e-bigger.e-rtl input:focus, .e-input-group.e-bigger.e-small.e-rtl input.e-input:focus, .e-input-group.e-control-wrapper.e-bigger.e-small.e-rtl input.e-input:focus, .e-float-input.e-control-wrapper.e-bigger.e-small.e-rtl input:focus, .e-float-input.e-bigger.e-small.e-rtl input:focus, .e-bigger.e-rtl .e-input-group.e-small input.e-input:focus, .e-bigger.e-rtl .e-input-group.e-control-wrapper.e-small input.e-input:focus, .e-bigger.e-rtl .e-float-input.e-control-wrapper.e-small input:focus, .e-bigger.e-rtl .e-float-input.e-small input:focus, .e-small.e-rtl .e-input-group.e-bigger input.e-input:focus, .e-small.e-rtl .e-input-group.e-control-wrapper.e-bigger input.e-input:focus, .e-small.e-rtl .e-float-input.e-control-wrapper.e-bigger input:focus, .e-small.e-rtl .e-float-input.e-bigger input:focus, .e-rtl .e-input-group.e-bigger.e-small input.e-input:focus, .e-rtl .e-input-group.e-control-wrapper.e-bigger.e-small input.e-input:focus, .e-rtl .e-float-input.e-control-wrapper.e-bigger.e-small input:focus, .e-rtl .e-float-input.e-bigger.e-small input:focus {
  padding-left: 0;
  padding-right: 12px;
  text-indent: 0;
}

textarea.e-input.e-bigger.e-small.e-css, .e-bigger textarea.e-input.e-small.e-css, .e-input-group.e-small.e-bigger textarea.e-input, .e-input-group.e-control-wrapper.e-small.e-bigger textarea.e-input, .e-input-group.e-small textarea.e-input.e-bigger, .e-input-group.e-control-wrapper.e-small textarea.e-input.e-bigger, .e-bigger .e-input-group.e-small textarea.e-input, .e-bigger .e-input-group.e-control-wrapper.e-small textarea.e-input, .e-float-input.e-small.e-bigger textarea, .e-float-input.e-small textarea.e-bigger, .e-bigger .e-float-input.e-small textarea, .e-float-input.e-control-wrapper.e-small.e-bigger textarea, .e-float-input.e-control-wrapper.e-small textarea.e-bigger, .e-bigger .e-float-input.e-control-wrapper.e-small textarea, .e-bigger .e-input-group.e-small textarea.e-input:focus, .e-bigger .e-input-group.e-control-wrapper.e-small textarea.e-input:focus, .e-bigger .e-float-input.e-control-wrapper.e-small textarea:focus, .e-bigger .e-float-input.e-small textarea:focus, .e-small .e-input-group.e-bigger textarea.e-input:focus, .e-small .e-input-group.e-control-wrapper.e-bigger textarea.e-input:focus, .e-small .e-float-input.e-control-wrapper.e-bigger textarea:focus, .e-small .e-float-input.e-bigger textarea:focus, .e-input-group.e-bigger.e-small textarea.e-input:focus, .e-input-group.e-control-wrapper.e-bigger.e-small textarea.e-input:focus, .e-float-input.e-control-wrapper.e-bigger.e-small textarea:focus, .e-float-input.e-bigger.e-small textarea:focus {
  padding-left: 12px;
  text-indent: 0;
}

.e-bigger textarea.e-input.e-small.e-rtl.e-css, .e-input-group.e-small.e-bigger.e-rtl textarea.e-input, .e-input-group.e-control-wrapper.e-small.e-bigger.e-rtl textarea.e-input, .e-input-group.e-small.e-rtl textarea.e-input.e-bigger, .e-input-group.e-control-wrapper.e-small.e-rtl textarea.e-input.e-bigger, .e-bigger .e-input-group.e-small.e-rtl textarea.e-input, .e-bigger .e-input-group.e-control-wrapper.e-small.e-rtl textarea.e-input, .e-float-input.e-small.e-bigger.e-rtl textarea, .e-float-input.e-small.e-rtl textarea.e-bigger, .e-bigger .e-float-input.e-small.e-rtl textarea, .e-float-input.e-control-wrapper.e-small.e-bigger.e-rtl textarea, .e-float-input.e-control-wrapper.e-small.e-rtl textarea.e-bigger, .e-bigger .e-float-input.e-control-wrapper.e-small.e-rtl textarea, .e-bigger.e-rtl textarea.e-input.e-small.e-css, .e-rtl .e-input-group.e-small.e-bigger textarea.e-input, .e-rtl .e-input-group.e-control-wrapper.e-small.e-bigger textarea.e-input, .e-rtl .e-input-group.e-small textarea.e-input.e-bigger, .e-rtl .e-input-group.e-control-wrapper.e-small textarea.e-input.e-bigger, .e-bigger.e-rtl .e-input-group.e-small textarea.e-input, .e-bigger.e-rtl .e-input-group.e-control-wrapper.e-small textarea.e-input, .e-rtl .e-float-input.e-control-wrapper.e-small.e-bigger textarea, .e-rtl .e-float-input.e-small textarea.e-bigger, .e-bigger.e-rtl .e-float-input.e-control-wrapper.e-small textarea, .e-rtl .e-float-input.e-control-wrapper.e-small.e-bigger textarea, .e-rtl .e-float-input.e-small textarea.e-bigger, .e-bigger.e-rtl .e-float-input.e-small textarea, .e-bigger.e-rtl .e-float-input.e-small.e-control-wrapper textarea, .e-bigger .e-input-group.e-small.e-rtl textarea.e-input:focus, .e-bigger .e-input-group.e-control-wrapper.e-small.e-rtl textarea.e-input:focus, .e-bigger .e-float-input.e-control-wrapper.e-small.e-rtl textarea:focus, .e-bigger .e-float-input.e-small.e-rtl textarea:focus, .e-small .e-input-group.e-bigger.e-rtl textarea.e-input:focus, .e-small .e-input-group.e-control-wrapper.e-bigger.e-rtl textarea.e-input:focus, .e-small .e-float-input.e-control-wrapper.e-bigger.e-rtl textarea:focus, .e-small .e-float-input.e-bigger.e-rtl textarea:focus, .e-input-group.e-bigger.e-small.e-rtl textarea.e-input:focus, .e-input-group.e-control-wrapper.e-bigger.e-small.e-rtl textarea.e-input:focus, .e-float-input.e-control-wrapper.e-bigger.e-small.e-rtl textarea:focus, .e-float-input.e-bigger.e-small.e-rtl textarea:focus, .e-bigger.e-rtl .e-input-group.e-small textarea.e-input:focus, .e-bigger.e-rtl .e-input-group.e-control-wrapper.e-small textarea.e-input:focus, .e-bigger.e-rtl .e-float-input.e-control-wrapper.e-small textarea:focus, .e-bigger.e-rtl .e-float-input.e-small textarea:focus, .e-small.e-rtl .e-input-group.e-bigger textarea.e-input:focus, .e-small.e-rtl .e-input-group.e-control-wrapper.e-bigger textarea.e-input:focus, .e-small.e-rtl .e-float-input.e-control-wrapper.e-bigger textarea:focus, .e-small.e-rtl .e-float-input.e-bigger textarea:focus, .e-rtl .e-input-group.e-bigger.e-small textarea.e-input:focus, .e-rtl .e-input-group.e-control-wrapper.e-bigger.e-small textarea.e-input:focus, .e-rtl .e-float-input.e-control-wrapper.e-bigger.e-small textarea:focus, .e-rtl .e-float-input.e-bigger.e-small textarea:focus {
  padding-right: 12px;
  text-indent: 0;
}

.e-float-input .e-clear-icon, .e-float-input.e-control-wrapper .e-clear-icon, .e-input-group .e-clear-icon, .e-input-group.e-control-wrapper .e-clear-icon {
  background: transparent;
  border: 0;
  cursor: pointer;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-direction: column;
      flex-direction: column;
  -ms-flex-pack: center;
      justify-content: center;
  line-height: 1;
  min-width: 24px;
  outline: none;
  padding: 0;
  text-align: center;
}

.e-float-input .e-clear-icon::before, .e-float-input.e-control-wrapper .e-clear-icon::before, .e-input-group .e-clear-icon::before, .e-input-group.e-control-wrapper .e-clear-icon::before {
  font-size: 12px;
  padding: 0;
  text-align: center;
  vertical-align: middle;
}

.e-float-input.e-static-clear .e-clear-icon.e-clear-icon-hide, .e-float-input.e-control-wrapper.e-static-clear .e-clear-icon.e-clear-icon-hide, .e-input-group.e-static-clear .e-clear-icon.e-clear-icon-hide, .e-input-group.e-control-wrapper.e-static-clear .e-clear-icon.e-clear-icon-hide {
  cursor: pointer;
  display: -ms-flexbox;
  display: flex;
}

.e-float-input.e-small.e-bigger .e-clear-icon, .e-float-input.e-control-wrapper.e-small.e-bigger .e-clear-icon, .e-input-group.e-small.e-bigger .e-clear-icon, .e-input-group.e-control-wrapper.e-small.e-bigger .e-clear-icon, .e-small .e-input-group.e-bigger input:first-child ~ .e-clear-icon, .e-small .e-input-group.e-control-wrapper.e-bigger input:first-child ~ .e-clear-icon, .e-bigger .e-input-group.e-small input:first-child ~ .e-clear-icon, .e-bigger .e-input-group.e-control-wrapper.e-small input:first-child ~ .e-clear-icon, .e-small .e-float-input.e-control-wrapper.e-bigger input:first-child ~ .e-clear-icon, .e-small .e-float-input.e-bigger input:first-child ~ .e-clear-icon, .e-bigger .e-float-input.e-control-wrapper.e-small input:first-child ~ .e-clear-icon, .e-bigger .e-float-input.e-small input:first-child ~ .e-clear-icon {
  padding-bottom: 0;
}

.e-float-input.e-small .e-clear-icon::before, .e-float-input.e-control-wrapper.e-small .e-clear-icon::before, .e-input-group.e-small .e-clear-icon::before, .e-input-group.e-control-wrapper.e-small .e-clear-icon::before, .e-float-input.e-control-wrapper input.e-small:first-child ~ .e-clear-icon::before, .e-small .e-float-input.e-control-wrapper .e-clear-icon::before, .e-float-input input.e-small:first-child ~ .e-clear-icon::before, .e-small .e-float-input .e-clear-icon::before, .e-small .e-input-group .e-clear-icon::before, .e-small .e-input-group.e-control-wrapper .e-clear-icon::before {
  font-size: 10px;
}

.e-float-input.e-bigger .e-clear-icon::before, .e-float-input.e-control-wrapper.e-bigger .e-clear-icon::before, .e-input-group.e-bigger .e-clear-icon::before, .e-input-group.e-control-wrapper.e-bigger .e-clear-icon::before, .e-float-input.e-control-wrapper input.e-bigger:first-child ~ .e-clear-icon::before, .e-bigger .e-float-input.e-control-wrapper .e-clear-icon::before, .e-float-input input.e-bigger:first-child ~ .e-clear-icon::before, .e-bigger .e-float-input .e-clear-icon::before, .e-bigger .e-input-group .e-clear-icon::before, .e-bigger .e-input-group.e-control-wrapper .e-clear-icon::before {
  font-size: 12px;
}

.e-float-input.e-small.e-bigger .e-clear-icon::before, .e-float-input.e-control-wrapper.e-small.e-bigger .e-clear-icon::before, .e-input-group.e-small.e-bigger .e-clear-icon::before, .e-input-group.e-control-wrapper.e-small.e-bigger .e-clear-icon::before, .e-small .e-input-group.e-bigger input:first-child ~ .e-clear-icon::before, .e-small .e-input-group.e-control-wrapper.e-bigger input:first-child ~ .e-clear-icon::before, .e-bigger .e-input-group.e-small input:first-child ~ .e-clear-icon::before, .e-bigger .e-input-group.e-control-wrapper.e-small input:first-child ~ .e-clear-icon::before, .e-small .e-float-input.e-control-wrapper.e-bigger input:first-child ~ .e-clear-icon::before, .e-small .e-float-input.e-bigger input:first-child ~ .e-clear-icon::before, .e-bigger .e-float-input.e-control-wrapper.e-small input:first-child ~ .e-clear-icon::before, .e-bigger .e-float-input.e-small input:first-child ~ .e-clear-icon::before {
  font-size: 12px;
}

.e-input:not(:valid):first-child ~ .e-clear-icon, .e-input-group input.e-input:not(:valid):first-child ~ .e-clear-icon, .e-input-group.e-control-wrapper input.e-input:not(:valid):first-child ~ .e-clear-icon, .e-float-input input:not(:valid):first-child ~ .e-clear-icon, .e-float-input.e-control-wrapper input:not(:valid):first-child ~ .e-clear-icon, .e-float-input.e-input-group input:not(:valid):first-child ~ .e-clear-icon, .e-float-input.e-input-group.e-control-wrapper input:not(:valid):first-child ~ .e-clear-icon {
  display: none;
}

.e-input-group .e-clear-icon.e-clear-icon-hide, .e-input-group.e-control-wrapper .e-clear-icon.e-clear-icon-hide {
  display: none;
}

.e-input-group.e-static-clear .e-clear-icon.e-clear-icon-hide, .e-input-group.e-control-wrapper.e-static-clear .e-clear-icon.e-clear-icon-hide {
  cursor: pointer;
}

.e-input-group.e-disabled input.e-input:not(:valid):first-child ~ .e-clear-icon, .e-input-group.e-control-wrapper.e-disabled input.e-input:not(:valid):first-child ~ .e-clear-icon, .e-float-input.e-disabled input:not(:valid):first-child ~ .e-clear-icon, .e-float-input.e-input-group.e-disabled input:not(:valid):first-child ~ .e-clear-icon, .e-float-input.e-input-group.e-control-wrapper.e-disabled input:not(:valid):first-child ~ .e-clear-icon, .e-float-input.e-control-wrapper.e-disabled input:not(:valid):first-child ~ .e-clear-icon, .e-input-group.e-disabled .e-clear-icon.e-clear-icon-hide, .e-input-group.e-control-wrapper.e-disabled .e-clear-icon.e-clear-icon-hide {
  cursor: not-allowed;
}

.e-float-input.e-control-wrapper input[readonly]:first-child ~ .e-clear-icon, .e-float-input.e-control-wrapper.e-input-group input[readonly]:first-child ~ .e-clear-icon, .e-float-input input[readonly]:first-child ~ .e-clear-icon, .e-float-input.e-input-group input[readonly]:first-child ~ .e-clear-icon, .e-input-group input[readonly]:first-child ~ .e-clear-icon.e-clear-icon-hide, .e-float-input.e-control-wrapper.e-input-group input[readonly]:first-child ~ .e-clear-icon, .e-float-input.e-input-group.e-control-wrapper input[readonly]:first-child ~ .e-clear-icon, .e-input-group.e-control-wrapper input[readonly]:first-child .e-clear-icon.e-clear-icon-hide {
  cursor: auto;
}

.e-input-group .e-clear-icon, .e-input-group.e-control-wrapper .e-clear-icon {
  min-height: 0;
  min-width: 30px;
  padding-bottom: 0;
  padding-left: 0;
  padding-right: 0;
  padding-top: 0;
}

.e-float-input.e-input-group .e-clear-icon, .e-float-input.e-input-group.e-control-wrapper .e-clear-icon {
  padding-right: 0;
  padding-top: 0;
}

.e-input-group.e-bigger .e-clear-icon, .e-input-group.e-control-wrapper.e-bigger .e-clear-icon, .e-input-group .e-clear-icon.e-bigger, .e-input-group.e-control-wrapper .e-clear-icon.e-bigger, .e-bigger .e-input-group .e-clear-icon, .e-bigger .e-input-group.e-control-wrapper .e-clear-icon {
  min-height: 0;
  min-width: 38px;
  padding-bottom: 0;
  padding-right: 0;
  padding-top: 0;
}

.e-input-group.e-float-input.e-bigger .e-clear-icon, .e-input-group.e-float-input .e-clear-icon.e-bigger, .e-bigger .e-input-group.e-float-input .e-clear-icon, .e-input-group.e-control-wrapper.e-float-input.e-bigger .e-clear-icon, .e-input-group.e-control-wrapper.e-float-input .e-clear-icon.e-bigger, .e-bigger .e-input-group.e-control-wrapper.e-float-input .e-clear-icon {
  padding-right: 0;
  padding-top: 0;
}

.e-input-group.e-small.e-bigger .e-clear-icon, .e-input-group.e-small .e-clear-icon.e-bigger, .e-bigger .e-input-group.e-small .e-clear-icon, .e-input-group.e-control-wrapper.e-small.e-bigger .e-clear-icon, .e-input-group.e-control-wrapper.e-small .e-clear-icon.e-bigger, .e-bigger .e-input-group.e-control-wrapper.e-small .e-clear-icon {
  min-height: 0;
  min-width: 34px;
  padding-bottom: 0;
  padding-right: 0;
  padding-top: 0;
}

.e-input-group.e-float-input.e-small.e-bigger .e-clear-icon, .e-input-group.e-float-input.e-small .e-clear-icon.e-bigger, .e-bigger .e-input-group.e-float-input.e-small .e-clear-icon, .e-input-group.e-control-wrapper.e-float-input.e-small.e-bigger .e-clear-icon, .e-input-group.e-control-wrapper.e-float-input.e-small .e-clear-icon.e-bigger, .e-bigger .e-input-group.e-control-wrapper.e-float-input.e-small .e-clear-icon, .e-input-group.e-float-input.e-control-wrapper.e-small.e-bigger .e-clear-icon, .e-input-group.e-float-input.e-control-wrapper.e-small .e-clear-icon.e-bigger, .e-bigger .e-input-group.e-float-input.e-control-wrapper.e-small .e-clear-icon, .e-input-group.e-control-wrapper.e-float-input.e-small.e-bigger .e-clear-icon, .e-input-group.e-control-wrapper.e-float-input.e-small .e-clear-icon.e-bigger, .e-bigger .e-input-group.e-control-wrapper.e-float-input.e-small .e-clear-icon {
  padding-right: 0;
  padding-top: 0;
}

.e-input-group.e-small .e-clear-icon, .e-input-group .e-clear-icon.e-small, .e-small .e-input-group .e-clear-icon, .e-input-group.e-control-wrapper.e-small .e-clear-icon, .e-input-group.e-control-wrapper .e-clear-icon.e-small, .e-small .e-input-group.e-control-wrapper .e-clear-icon {
  min-height: 0;
  min-width: 24px;
  padding-bottom: 0;
  padding-right: 0;
  padding-top: 0;
}

.e-input-group.e-float-input.e-small .e-clear-icon, .e-input-group.e-float-input .e-clear-icon.e-small, .e-small .e-input-group.e-float-input .e-clear-icon, .e-input-group.e-control-wrapper.e-float-input.e-small .e-clear-icon, .e-input-group.e-control-wrapper.e-float-input .e-clear-icon.e-small, .e-small .e-input-group.e-control-wrapper.e-float-input .e-clear-icon, .e-input-group.e-float-input.e-control-wrapper.e-small .e-clear-icon, .e-input-group.e-float-input.e-control-wrapper .e-clear-icon.e-small, .e-small .e-input-group.e-float-input.e-control-wrapper .e-clear-icon, .e-input-group.e-control-wrapper.e-float-input.e-small .e-clear-icon, .e-input-group.e-control-wrapper.e-float-input .e-clear-icon.e-small, .e-small .e-input-group.e-control-wrapper.e-float-input .e-clear-icon {
  padding-right: 0;
  padding-top: 0;
}

.e-input.e-css:not(:valid), .e-input.e-css:valid, .e-float-input.e-control-wrapper input:not(:valid), .e-float-input.e-control-wrapper input:valid, .e-float-input input:not(:valid), .e-float-input input:valid, .e-input-group input:not(:valid), .e-input-group input:valid, .e-input-group.e-control-wrapper input:not(:valid), .e-input-group.e-control-wrapper input:valid, .e-float-input.e-control-wrapper textarea:not(:valid), .e-float-input.e-control-wrapper textarea:valid, .e-float-input textarea:not(:valid), .e-float-input textarea:valid, .e-input-group.e-control-wrapper textarea:not(:valid), .e-input-group.e-control-wrapper textarea:valid, .e-input-group textarea:not(:valid), .e-input-group textarea:valid {
  box-shadow: none;
}

.e-input-group .e-input-in-wrap, .e-input-group.e-control-wrapper .e-input-in-wrap, .e-float-input .e-input-in-wrap, .e-float-input.e-control-wrapper .e-input-in-wrap {
  width: 100%;
  display: -ms-flexbox;
  display: flex;
  position: relative;
}

.e-float-input .e-input-in-wrap label.e-float-text, .e-float-input.e-control-wrapper .e-input-in-wrap label.e-float-text {
  right: 0;
  overflow: hidden;
  text-overflow: ellipsis;
  vertical-align: middle;
  white-space: nowrap;
}

.e-float-input input.e-input, .e-float-input.e-bigger .e-input, .e-bigger .e-float-input .e-input, .e-float-input.e-control-wrapper input.e-input, .e-float-input.e-control-wrapper.e-bigger .e-input, .e-bigger .e-float-input.e-control-wrapper .e-input {
  margin-bottom: 0;
}

.e-float-input textarea:focus ~ label.e-float-text, .e-float-input textarea:valid ~ label.e-float-text, .e-float-input textarea ~ label.e-label-top.e-float-text, .e-float-input textarea[readonly] ~ label.e-label-top.e-float-text, .e-float-input textarea[disabled] ~ label.e-label-top.e-float-text, .e-float-input textarea label.e-float-text.e-label-top, .e-float-input.e-control-wrapper textarea:focus ~ label.e-float-text, .e-float-input.e-control-wrapper textarea:valid ~ label.e-float-text, .e-float-input.e-control-wrapper textarea ~ label.e-label-top.e-float-text, .e-float-input.e-control-wrapper textarea[readonly] ~ label.e-label-top.e-float-text, .e-float-input.e-control-wrapper textarea[disabled] ~ label.e-label-top.e-float-text, .e-float-input.e-control-wrapper textarea label.e-float-text.e-label-top {
  font-size: 12px;
  font-style: normal;
  top: 15px;
  transform: translate3d(-10px, -40px, 0) scale(1);
}

.e-float-input.e-bigger textarea:focus ~ label.e-float-text, .e-float-input.e-bigger textarea:valid ~ label.e-float-text, .e-float-input.e-bigger textarea ~ label.e-label-top.e-float-text, .e-bigger .e-float-input textarea:focus ~ label.e-float-text, .e-bigger .e-float-input textarea:valid ~ label.e-float-text, .e-bigger .e-float-input textarea ~ label.e-label-top.e-float-text, .e-float-input.e-control-wrapper.e-bigger textarea:focus ~ label.e-float-text, .e-float-input.e-control-wrapper.e-bigger textarea:valid ~ label.e-float-text, .e-float-input.e-control-wrapper.e-bigger textarea ~ label.e-label-top.e-float-text, .e-bigger .e-float-input.e-control-wrapper textarea:focus ~ label.e-float-text, .e-bigger .e-float-input.e-control-wrapper textarea:valid ~ label.e-float-text, .e-bigger .e-float-input.e-control-wrapper textarea ~ label.e-label-top.e-float-text {
  font-size: 13px;
  top: 14px;
}

.e-float-input.e-small textarea:focus ~ label.e-float-text, .e-float-input.e-small textarea:valid ~ label.e-float-text, .e-float-input.e-small textarea ~ label.e-label-top.e-float-text, .e-small .e-float-input textarea ~ label.e-label-top.e-float-text, .e-float-input.e-control-wrapper.e-small textarea:focus ~ label.e-float-text, .e-float-input.e-control-wrapper.e-small textarea:valid ~ label.e-float-text, .e-float-input.e-control-wrapper.e-small textarea ~ label.e-label-top.e-float-text, .e-small .e-float-input.e-control-wrapper textarea ~ label.e-label-top.e-float-text {
  font-size: 11px;
  top: 16px;
}

.e-float-input.e-small.e-bigger textarea:focus ~ label.e-float-text, .e-float-input.e-small.e-bigger textarea:valid ~ label.e-float-text, .e-float-input.e-small.e-bigger textarea ~ label.e-label-top.e-float-text, .e-bigger .e-float-input.e-small textarea:focus ~ label.e-float-text, .e-bigger .e-float-input.e-small textarea:valid ~ label.e-float-text, .e-bigger .e-float-input.e-small textarea ~ label.e-label-top.e-float-text, .e-float-input.e-control-wrapper.e-small.e-bigger textarea:focus ~ label.e-float-text, .e-float-input.e-control-wrapper.e-small.e-bigger textarea:valid ~ label.e-float-text, .e-float-input.e-control-wrapper.e-small.e-bigger textarea ~ label.e-label-top.e-float-text, .e-bigger .e-float-input.e-control-wrapper.e-small textarea:focus ~ label.e-float-text, .e-bigger .e-float-input.e-control-wrapper.e-small textarea:valid ~ label.e-float-text, .e-bigger .e-float-input.e-control-wrapper.e-small textarea ~ label.e-label-top.e-float-text {
  font-size: 12px;
  top: 16px;
}

.e-float-input textarea ~ .e-float-text, .e-float-input.e-control-wrapper textarea ~ .e-float-text {
  top: 15px;
}

.e-float-input.e-small textarea:not(:focus):valid ~ label.e-float-text.e-label-bottom, .e-small .e-float-input textarea:not(:focus):valid ~ label.e-float-text.e-label-bottom, .e-float-input.e-control-wrapper.e-small textarea:not(:focus):valid ~ label.e-float-text.e-label-bottom, .e-small .e-float-input.e-control-wrapper textarea:not(:focus):valid ~ label.e-float-text.e-label-bottom {
  font-size: 13px;
}

.e-float-input.e-bigger textarea ~ label.e-float-text, .e-float-input textarea ~ label.e-float-text.e-bigger, .e-float-input textarea.e-bigger ~ label.e-float-text, .e-bigger .e-float-input textarea ~ label.e-float-text, .e-float-input.e-bigger textarea:not(:focus):valid ~ label.e-float-text.e-label-bottom, .e-bigger .e-float-input textarea:not(:focus):valid ~ label.e-float-text.e-label-bottom, .e-float-input.e-control-wrapper.e-bigger textarea ~ label.e-float-text, .e-float-input.e-control-wrapper textarea ~ label.e-float-text.e-bigger, .e-float-input.e-control-wrapper textarea.e-bigger ~ label.e-float-text, .e-bigger .e-float-input.e-control-wrapper textarea ~ label.e-float-text, .e-float-input.e-control-wrapper.e-bigger textarea:not(:focus):valid ~ label.e-float-text.e-label-bottom, .e-bigger .e-float-input.e-control-wrapper textarea:not(:focus):valid ~ label.e-float-text.e-label-bottom {
  font-size: 15px;
  top: 18 px;
}

.e-float-input.e-small textarea ~ label.e-float-text, .e-float-input textarea ~ label.e-float-text.e-small, .e-float-input textarea.e-small ~ label.e-float-text, .e-small .e-float-input textarea ~ label.e-float-text, .e-float-input.e-control-wrapper.e-small textarea ~ label.e-float-text, .e-float-input.e-control-wrapper textarea ~ label.e-float-text.e-small, .e-float-input.e-control-wrapper textarea.e-small ~ label.e-float-text, .e-small .e-float-input.e-control-wrapper textarea ~ label.e-float-text {
  font-size: 13px;
  top: 15px;
}

.e-input-group:hover:not(.e-disabled), .e-input-group.e-control-wrapper:hover:not(.e-disabled), .e-float-input:hover:not(.e-disabled), .e-float-input:hover:not(.e-input-group):not(.e-disabled) input:not([disabled]), .e-float-input:hover:not(.e-input-group):not(.e-disabled) textarea:not([disabled]), .e-float-input:hover:not(.e-input-group):not(.e-disabled) input:not([disabled]), .e-float-input:hover:not(.e-input-group):not(.e-disabled) textarea:not([disabled]), .e-float-input.e-control-wrapper:hover:not(.e-disabled), .e-float-input.e-control-wrapper:hover:not(.e-input-group):not(.e-disabled) input:not([disabled]), .e-float-input.e-control-wrapper:hover:not(.e-input-group):not(.e-disabled) textarea:not([disabled]), .e-float-input.e-control-wrapper:hover:not(.e-input-group):not(.e-disabled) input:not([disabled]), .e-float-input.e-control-wrapper:hover:not(.e-input-group):not(.e-disabled) textarea:not([disabled]) {
  border-bottom-width: 1px;
}

.e-float-input input:-webkit-autofill ~ label.e-float-text, .e-float-input.e-control-wrapper.e-autofill input:-webkit-autofill ~ label.e-float-text, .e-float-input.e-control-wrapper.e-autofill input:not(:focus):valid:-webkit-autofill ~ label.e-float-text.e-label-bottom {
  font-size: 12px;
  font-style: normal;
  padding-right: 0;
  transform: translate3d(-10px, -43px, 0) scale(1);
  -webkit-user-select: text;
          user-select: text;
}

.e-bigger .e-float-input input:-webkit-autofill ~ label.e-float-text, .e-bigger.e-float-input input:-webkit-autofill ~ label.e-float-text, .e-bigger .e-float-input.e-control-wrapper.e-autofill input:-webkit-autofill ~ label.e-float-text, .e-bigger.e-float-input.e-control-wrapper.e-autofill input:-webkit-autofill ~ label.e-float-text, .e-bigger .e-float-input.e-control-wrapper.e-autofill input:not(:focus):valid:-webkit-autofill ~ label.e-float-text.e-label-bottom, .e-bigger.e-float-input.e-control-wrapper.e-autofill input:not(:focus):valid:-webkit-autofill ~ label.e-float-text.e-label-bottom {
  font-size: 13px;
  font-style: normal;
  padding-right: 0;
  top: 14px;
  transform: translate3d(-10px, -43px, 0) scale(1);
  -webkit-user-select: text;
          user-select: text;
}

.e-small .e-float-input input:-webkit-autofill ~ label.e-float-text, .e-small.e-float-input input:-webkit-autofill ~ label.e-float-text, .e-small .e-float-input.e-control-wrapper.e-autofill input:-webkit-autofill ~ label.e-float-text, .e-small.e-float-input.e-control-wrapper.e-autofill input:-webkit-autofill ~ label.e-float-text, .e-small .e-float-input.e-control-wrapper.e-autofill input:not(:focus):valid:-webkit-autofill ~ label.e-float-text.e-label-bottom, .e-small.e-float-input.e-control-wrapper.e-autofill input:not(:focus):valid:-webkit-autofill ~ label.e-float-text.e-label-bottom {
  font-size: 11px;
  font-style: normal;
  padding-right: 0;
  top: 17px;
  transform: translate3d(-10px, -43px, 0) scale(1);
  -webkit-user-select: text;
          user-select: text;
}

.e-bigger .e-float-input.e-small input:-webkit-autofill ~ label.e-float-text, .e-bigger.e-small .e-float-input input:-webkit-autofill ~ label.e-float-text, .e-float-input.e-bigger.e-small input:-webkit-autofill ~ label.e-float-text, .e-bigger .e-float-input.e-small.e-control-wrapper.e-autofill input:-webkit-autofill ~ label.e-float-text, .e-bigger.e-small .e-float-input.e-control-wrapper.e-autofill input:-webkit-autofill ~ label.e-float-text, .e-float-input.e-bigger.e-small.e-control-wrapper.e-autofill input:-webkit-autofill ~ label.e-float-text, .e-small.e-bigger .e-float-input.e-control-wrapper.e-autofill input:not(:focus):valid:-webkit-autofill ~ label.e-float-text.e-label-bottom, .e-small.e-bigger.e-float-input.e-control-wrapper.e-autofill input:not(:focus):valid:-webkit-autofill ~ label.e-float-text.e-label-bottom, .e-bigger .e-small.e-float-input.e-control-wrapper.e-autofill input:not(:focus):valid:-webkit-autofill ~ label.e-float-text.e-label-bottom, .e-small .e-bigger.e-float-input.e-control-wrapper.e-autofill input:not(:focus):valid:-webkit-autofill ~ label.e-float-text.e-label-bottom {
  font-size: 12px;
  font-style: normal;
  padding-right: 0;
  top: 16px;
  transform: translate3d(-10px, -43px, 0) scale(1);
  -webkit-user-select: text;
          user-select: text;
}

.e-float-input textarea:not(:focus):valid ~ label.e-float-text.e-label-bottom, .e-float-input.e-bigger textarea:not(:focus):valid ~ label.e-float-text.e-label-bottom, .e-bigger .e-float-input textarea:not(:focus):valid ~ label.e-float-text.e-label-bottom, .e-float-input.e-small textarea:not(:focus):valid ~ label.e-float-text.e-label-bottom, .e-small .e-float-input textarea:not(:focus):valid ~ label.e-float-text.e-label-bottom, .e-bigger .e-float-input.e-small textarea:not(:focus):valid ~ label.e-float-text.e-label-bottom, .e-small .e-float-input.e-bigger textarea:not(:focus):valid ~ label.e-float-text.e-label-bottom, .e-float-input.e-bigger.e-small textarea:not(:focus):valid ~ label.e-float-text.e-label-bottom, .e-bigger.e-small .e-float-input textarea:not(:focus):valid ~ label.e-float-text.e-label-bottom, .e-float-input.e-bigger.e-small textarea:not(:focus):valid ~ label.e-float-text.e-label-bottom, .e-float-input.e-control-wrapper textarea:not(:focus):valid ~ label.e-float-text.e-label-bottom, .e-float-input.e-control-wrapper.e-bigger textarea:not(:focus):valid ~ label.e-float-text.e-label-bottom, .e-bigger .e-float-input.e-control-wrapper textarea:not(:focus):valid ~ label.e-float-text.e-label-bottom, .e-float-input.e-control-wrapper.e-small textarea:not(:focus):valid ~ label.e-float-text.e-label-bottom, .e-small .e-float-input.e-control-wrapper textarea:not(:focus):valid ~ label.e-float-text.e-label-bottom, .e-bigger .e-float-input.e-control-wrapper.e-small textarea:not(:focus):valid ~ label.e-float-text.e-label-bottom, .e-small .e-float-input.e-control-wrapper.e-bigger textarea:not(:focus):valid ~ label.e-float-text.e-label-bottom, .e-float-input.e-control-wrapper.e-bigger.e-small textarea:not(:focus):valid ~ label.e-float-text.e-label-bottom, .e-bigger.e-small .e-float-input.e-control-wrapper textarea:not(:focus):valid ~ label.e-float-text.e-label-bottom, .e-float-input.e-control-wrapper.e-bigger.e-small textarea:not(:focus):valid ~ label.e-float-text.e-label-bottom {
  -moz-transform: translate(0%, -50%);
  -ms-transform: translate(0%, -50%);
  -o-transform: translate(0%, -50%);
  -webkit-transform: translate(0%, -50%);
  box-sizing: border-box;
  left: 0%;
  top: 15px;
  transform: translate(0%, -50%);
  width: 100%;
  font-style: normal;
}

.e-bigger .e-float-input.e-small textarea:not(:focus):valid ~ label.e-float-text.e-label-bottom, .e-small .e-float-input.e-bigger textarea:not(:focus):valid ~ label.e-float-text.e-label-bottom, .e-float-input.e-bigger.e-small textarea:not(:focus):valid ~ label.e-float-text.e-label-bottom, .e-bigger.e-small .e-float-input textarea:not(:focus):valid ~ label.e-float-text.e-label-bottom, .e-float-input.e-bigger.e-small textarea:not(:focus):valid ~ label.e-float-text.e-label-bottom, .e-bigger .e-float-input.e-control-wrapper.e-small textarea:not(:focus):valid ~ label.e-float-text.e-label-bottom, .e-small .e-float-input.e-control-wrapper.e-bigger textarea:not(:focus):valid ~ label.e-float-text.e-label-bottom, .e-float-input.e-control-wrapper.e-bigger.e-small textarea:not(:focus):valid ~ label.e-float-text.e-label-bottom, .e-bigger.e-small .e-float-input.e-control-wrapper textarea:not(:focus):valid ~ label.e-float-text.e-label-bottom, .e-float-input.e-control-wrapper.e-bigger.e-small textarea:not(:focus):valid ~ label.e-float-text.e-label-bottom {
  font-size: 14px;
  font-style: normal;
}

.e-float-input.e-bigger.e-small textarea:focus ~ label.e-float-text, .e-float-input.e-bigger.e-small textarea:valid ~ label.e-float-text, .e-float-input.e-bigger.e-small textarea ~ label.e-label-top.e-float-text, .e-float-input.e-bigger.e-small textarea[readonly] ~ label.e-label-top.e-float-text, .e-float-input.e-bigger.e-small textarea[disabled] ~ label.e-label-top.e-float-text, .e-float-input.e-bigger.e-small textarea label.e-float-text.e-label-top, .e-bigger .e-float-input.e-small textarea:focus ~ label.e-float-text, .e-bigger .e-float-input.e-small textarea:valid ~ label.e-float-text, .e-bigger .e-float-input.e-small textarea ~ label.e-label-top.e-float-text, .e-bigger .e-float-input.e-small textarea[readonly] ~ label.e-label-top.e-float-text, .e-bigger .e-float-input.e-small textarea[disabled] ~ label.e-label-top.e-float-text, .e-bigger .e-float-input.e-small textarea label.e-float-text.e-label-top, .e-small .e-float-input.e-bigger textarea:focus ~ label.e-float-text, .e-small .e-float-input.e-bigger textarea:valid ~ label.e-float-text, .e-small .e-float-input.e-bigger textarea ~ label.e-label-top.e-float-text, .e-small .e-float-input.e-bigger textarea[readonly] ~ label.e-label-top.e-float-text, .e-small .e-float-input.e-bigger textarea[disabled] ~ label.e-label-top.e-float-text, .e-small .e-float-input.e-bigger textarea label.e-float-text.e-label-top, .e-float-input.e-control-wrapper.e-bigger.e-small textarea:focus ~ label.e-float-text, .e-float-input.e-control-wrapper.e-bigger.e-small textarea:valid ~ label.e-float-text, .e-float-input.e-control-wrapper.e-bigger.e-small textarea ~ label.e-label-top.e-float-text, .e-float-input.e-control-wrapper.e-bigger.e-small textarea[readonly] ~ label.e-label-top.e-float-text, .e-float-input.e-control-wrapper.e-bigger.e-small textarea[disabled] ~ label.e-label-top.e-float-text, .e-float-input.e-control-wrapper.e-bigger.e-small textarea label.e-float-text.e-label-top, .e-bigger .e-float-input.e-control-wrapper.e-small textarea:focus ~ label.e-float-text, .e-bigger .e-float-input.e-control-wrapper.e-small textarea:valid ~ label.e-float-text, .e-bigger .e-float-input.e-control-wrapper.e-small textarea ~ label.e-label-top.e-float-text, .e-bigger .e-float-input.e-control-wrapper.e-small textarea[readonly] ~ label.e-label-top.e-float-text, .e-bigger .e-float-input.e-control-wrapper.e-small textarea[disabled] ~ label.e-label-top.e-float-text, .e-bigger .e-float-input.e-control-wrapper.e-small textarea label.e-float-text.e-label-top, .e-small .e-float-input.e-control-wrapper.e-bigger textarea:focus ~ label.e-float-text, .e-small .e-float-input.e-control-wrapper.e-bigger textarea:valid ~ label.e-float-text, .e-small .e-float-input.e-control-wrapper.e-bigger textarea ~ label.e-label-top.e-float-text, .e-small .e-float-input.e-control-wrapper.e-bigger textarea[readonly] ~ label.e-label-top.e-float-text, .e-small .e-float-input.e-control-wrapper.e-bigger textarea[disabled] ~ label.e-label-top.e-float-text, .e-small .e-float-input.e-control-wrapper.e-bigger textarea label.e-float-text.e-label-top {
  font-style: normal;
  padding-right: 0;
  transform: translate3d(-10px, -43px, 0) scale(1);
}

.e-float-input textarea:not(:focus):valid ~ label.e-float-text.e-label-bottom, .e-float-input.e-control-wrapper textarea:not(:focus):valid ~ label.e-float-text.e-label-bottom {
  font-style: normal;
  transform: translate3d(0, 16px, 0) scale(1);
}

.e-float-input textarea:not(:focus):valid ~ label.e-float-text.e-label-bottom, .e-float-input.e-control-wrapper textarea:not(:focus):valid ~ label.e-float-text.e-label-bottom {
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  left: 0;
  overflow: hidden;
  padding-left: 10px;
  pointer-events: none;
  position: absolute;
  text-overflow: ellipsis;
  top: -11px;
  transform: translate3d(0, 16px, 0) scale(1);
  transform-origin: left top;
  transition: 0.25s cubic-bezier(0.25, 0.8, 0.25, 1);
  -webkit-user-select: none;
     -moz-user-select: none;
      -ms-user-select: none;
          user-select: none;
  white-space: nowrap;
  width: 100%;
}

textarea.e-input.e-css::-webkit-textarea-placeholder, textarea.e-input.e-css:-moz-placeholder, textarea.e-input.e-css:-ms-textarea-placeholder, textarea.e-input.e-css::-moz-placeholder {
  font-size: 14px;
  font-style: normal;
}

.e-small textarea.e-input.e-css::-webkit-textarea-placeholder, textarea.e-small.e-input.e-css::-webkit-textarea-placeholder, .e-small textarea.e-input.e-css:-moz-placeholder, textarea.e-small.e-input.e-css:-moz-placeholder, .e-small textarea.e-input.e-css:-ms-input-placeholder, textarea.e-small.e-input.e-css:-ms-input-placeholder, .e-small textarea.e-input.e-css::-moz-placeholder, textarea.e-small.e-input.e-css::-moz-placeholder {
  font-size: 13px;
  font-style: normal;
}

.e-bigger textarea.e-small.e-input.e-css::-webkit-textarea-placeholder, .e-small textarea.e-bigger.e-input.e-css::-webkit-textarea-placeholder, .e-bigger textarea.e-small.e-input.e-css:-moz-placeholder, .e-small textarea.e-bigger.e-input.e-css:-moz-placeholder, .e-bigger textarea.e-small.e-input.e-css:-ms-input-placeholder, .e-small textarea.e-bigger.e-input.e-css:-ms-input-placeholder, .e-bigger textarea.e-small.e-input.e-css::-moz-placeholder, .e-small textarea.e-bigger.e-input.e-css::-moz-placeholder {
  font-size: 14px;
  font-style: normal;
}

.e-bigger textarea.e-input.e-css::-webkit-textarea-placeholder, textarea.e-bigger.e-input.e-css::-webkit-textarea-placeholder, .e-bigger textarea.e-input.e-css:-moz-placeholder, textarea.e-bigger.e-input.e-css:-moz-placeholder, .e-bigger textarea.e-input.e-css:-ms-textarea-placeholder, textarea.e-bigger.e-input.e-css:-ms-textarea-placeholder, .e-bigger textarea.e-input.e-css::-moz-placeholder, textarea.e-bigger.e-input.e-css::-moz-placeholder {
  font-size: 15px;
  font-style: normal;
}

.e-float-input textarea:-webkit-autofill ~ label.e-float-text, .e-float-input.e-control-wrapper.e-autofill textarea:-webkit-autofill ~ label.e-float-text, .e-float-input.e-control-wrapper.e-autofill textarea:not(:focus):valid:-webkit-autofill ~ label.e-float-text.e-label-bottom {
  font-size: 12px;
  font-style: normal;
  padding-right: 0;
  transform: translate3d(-10px, -43px, 0) scale(1);
  -webkit-user-select: text;
          user-select: text;
}

.e-small .e-float-input textarea:-webkit-autofill ~ label.e-float-text, .e-small.e-float-input textarea:-webkit-autofill ~ label.e-float-text, .e-small .e-float-input.e-control-wrapper.e-autofill textarea:-webkit-autofill ~ label.e-float-text, .e-small.e-float-input.e-control-wrapper.e-autofill textarea:-webkit-autofill ~ label.e-float-text, .e-small .e-float-input.e-control-wrapper.e-autofill textarea:not(:focus):valid:-webkit-autofill ~ label.e-float-text.e-label-bottom, .e-small.e-float-input.e-control-wrapper.e-autofill textarea:not(:focus):valid:-webkit-autofill ~ label.e-float-text.e-label-bottom {
  font-size: 11px;
  font-style: normal;
  padding-right: 0;
  top: 17px;
  transform: translate3d(-10px, -43px, 0) scale(1);
  -webkit-user-select: text;
          user-select: text;
}

.e-bigger .e-float-input.e-small textarea:-webkit-autofill ~ label.e-float-text, .e-bigger.e-small .e-float-input textarea:-webkit-autofill ~ label.e-float-text, .e-float-input.e-bigger.e-small textarea:-webkit-autofill ~ label.e-float-text, .e-bigger .e-float-input.e-small.e-control-wrapper.e-autofill textarea:-webkit-autofill ~ label.e-float-text, .e-bigger.e-small .e-float-input.e-control-wrapper.e-autofill textarea:-webkit-autofill ~ label.e-float-text, .e-float-input.e-bigger.e-small.e-control-wrapper.e-autofill textarea:-webkit-autofill ~ label.e-float-text, .e-small.e-bigger .e-float-input.e-control-wrapper.e-autofill textarea:not(:focus):valid:-webkit-autofill ~ label.e-float-text.e-label-bottom, .e-small.e-bigger.e-float-input.e-control-wrapper.e-autofill textarea:not(:focus):valid:-webkit-autofill ~ label.e-float-text.e-label-bottom, .e-bigger .e-small.e-float-input.e-control-wrapper.e-autofill textarea:not(:focus):valid:-webkit-autofill ~ label.e-float-text.e-label-bottom, .e-small .e-bigger.e-float-input.e-control-wrapper.e-autofill textarea:not(:focus):valid:-webkit-autofill ~ label.e-float-text.e-label-bottom {
  font-size: 12px;
  font-style: normal;
  padding-right: 0;
  top: 16px;
  transform: translate3d(-10px, -43px, 0) scale(1);
  -webkit-user-select: text;
          user-select: text;
}

.e-bigger .e-float-input textarea:-webkit-autofill ~ label.e-float-text, .e-bigger.e-float-input textarea:-webkit-autofill ~ label.e-float-text, .e-bigger .e-float-input.e-control-wrapper.e-autofill textarea:-webkit-autofill ~ label.e-float-text, .e-bigger.e-float-input.e-control-wrapper.e-autofill textarea:-webkit-autofill ~ label.e-float-text, .e-bigger .e-float-input.e-control-wrapper.e-autofill textarea:not(:focus):valid:-webkit-autofill ~ label.e-float-text.e-label-bottom, .e-bigger.e-float-input.e-control-wrapper.e-autofill textarea:not(:focus):valid:-webkit-autofill ~ label.e-float-text.e-label-bottom {
  font-size: 13px;
  font-style: normal;
  padding-right: 0;
  top: 14px;
  transform: translate3d(-10px, -43px, 0) scale(1);
  -webkit-user-select: text;
          user-select: text;
}

.e-float-input.e-bigger textarea:focus ~ label.e-float-text, .e-float-input.e-bigger textarea:valid ~ label.e-float-text, .e-float-input.e-bigger textarea ~ label.e-label-top.e-float-text, .e-float-input.e-bigger textarea[readonly] ~ label.e-label-top.e-float-text, .e-float-input.e-bigger textarea[disabled] ~ label.e-label-top.e-float-text, .e-float-input.e-bigger textarea label.e-float-text.e-label-top, .e-bigger .e-float-input textarea:focus ~ label.e-float-text, .e-bigger .e-float-input textarea:valid ~ label.e-float-text, .e-bigger .e-float-input textarea ~ label.e-label-top.e-float-text, .e-bigger .e-float-input textarea[readonly] ~ label.e-label-top.e-float-text, .e-bigger .e-float-input textarea[disabled] ~ label.e-label-top.e-float-text, .e-bigger .e-float-input textarea label.e-float-text.e-label-top, .e-float-input.e-control-wrapper.e-bigger textarea:focus ~ label.e-float-text, .e-float-input.e-control-wrapper.e-bigger textarea:valid ~ label.e-float-text, .e-float-input.e-control-wrapper.e-bigger textarea ~ label.e-label-top.e-float-text, .e-float-input.e-control-wrapper.e-bigger textarea[readonly] ~ label.e-label-top.e-float-text, .e-float-input.e-control-wrapper.e-bigger textarea[disabled] ~ label.e-label-top.e-float-text, .e-float-input.e-control-wrapper.e-bigger textarea label.e-float-text.e-label-top, .e-bigger .e-float-input.e-control-wrapper textarea:focus ~ label.e-float-text, .e-bigger .e-float-input.e-control-wrapper textarea:valid ~ label.e-float-text, .e-bigger .e-float-input.e-control-wrapper textarea ~ label.e-label-top.e-float-text, .e-bigger .e-float-input.e-control-wrapper textarea[readonly] ~ label.e-label-top.e-float-text, .e-bigger .e-float-input.e-control-wrapper textarea[disabled] ~ label.e-label-top.e-float-text, .e-bigger .e-float-input.e-control-wrapper textarea label.e-float-text.e-label-top {
  font-style: normal;
  padding-right: 0;
  transform: translate3d(-10px, -43px, 0) scale(1);
}

.e-float-input textarea[disabled], .e-float-input textarea.e-disabled, .e-float-input.e-control-wrapper textarea[disabled], .e-float-input.e-control-wrapper textarea.e-disabled {
  border-color: #333232;
  border-style: solid;
}

.e-float-input textarea[disabled], .e-float-input textarea.e-disabled, .e-float-input.e-disabled textarea, .e-float-input.e-control-wrapper textarea[disabled], .e-float-input.e-control-wrapper textarea.e-disabled, .e-float-input.e-control-wrapper.e-disabled textarea {
  filter: alpha(opacity=100);
  opacity: 1;
}

.e-float-input textarea[disabled], .e-float-input textarea.e-disabled, .e-float-input.e-control-wrapper textarea[disabled], .e-float-input.e-control-wrapper textarea.e-disabled {
  background: transparent;
  background-image: none;
  cursor: not-allowed;
}

textarea.e-input.e-css, .e-input-group textarea.e-input, .e-input-group textarea, .e-input-group.e-control-wrapper textarea.e-input, .e-input-group.e-control-wrapper textarea, .e-float-input textarea, .e-float-input.e-input-group textarea, .e-float-input.e-control-wrapper textarea, .e-float-input.e-control-wrapper.e-input-group textarea {
  border-radius: 0;
}

textarea.e-input.e-css, .e-input-group textarea, .e-input-group textarea.e-input, .e-input-group.e-input-focus textarea, .e-input-group.e-input-focus textarea.e-input, .e-input-group.e-control-wrapper textarea, .e-input-group.e-control-wrapper.e-input-focus textarea, .e-input-group.e-control-wrapper textarea.e-input, .e-input-group.e-control-wrapper.e-input-focus textarea.e-input, .e-float-input textarea, .e-float-input.e-control-wrapper textarea {
  line-height: 1.5;
  min-height: 30px;
  min-width: 0;
  padding: 4px 10px 0 10px;
  resize: vertical;
}

.e-input-group.e-bigger textarea, .e-input-group.e-bigger textarea.e-input, .e-input-group textarea.e-bigger, .e-input-group textarea.e-input.e-bigger, .e-input-group.e-control-wrapper.e-bigger textarea, .e-input-group.e-control-wrapper.e-bigger textarea.e-input, .e-input-group.e-control-wrapper textarea.e-bigger, .e-input-group.e-control-wrapper textarea.e-input.e-bigger, .e-bigger .e-input-group textarea, .e-bigger .e-input-group textarea.e-input, .e-bigger .e-input-group.e-control-wrapper textarea, .e-bigger .e-input-group.e-control-wrapper textarea.e-input, .e-float-input.e-bigger textarea, .e-float-input textarea.e-bigger, .e-float-input.e-control-wrapper.e-bigger textarea, .e-float-input.e-control-wrapper textarea.e-bigger, .e-bigger .e-float-input textarea, .e-bigger .e-float-input.e-control-wrapper textarea {
  font: inherit;
  line-height: 1.5;
  min-height: 36px;
  padding: 6px 12px 0 12px;
}

.e-input-group.e-input-focus.e-bigger textarea, .e-input-group.e-input-focus.e-bigger textarea.e-input, .e-input-group.e-input-focus textarea.e-bigger, .e-input-group.e-input-focus textarea.e-input.e-bigger, .e-input-group.e-control-wrapper.e-input-focus.e-bigger textarea, .e-input-group.e-control-wrapper.e-input-focus.e-bigger textarea.e-input, .e-input-group.e-control-wrapper.e-input-focus textarea.e-bigger, .e-input-group.e-control-wrapper.e-input-focus textarea.e-input.e-bigger, .e-bigger .e-input-group.e-input-focus textarea, .e-bigger .e-input-group.e-control-wrapper.e-input-focus textarea, .e-bigger .e-input-group.e-control-wrapper.e-input-focus textarea.e-input {
  font: inherit;
  line-height: 1.5;
  min-height: 36px;
  padding: 6px 12px 0 12px;
}

.e-input-group.e-small textarea, .e-input-group.e-small textarea.e-input, .e-input-group textarea.e-small, .e-input-group textarea.e-input.e-small, .e-input-group.e-control-wrapper.e-small textarea, .e-input-group.e-control-wrapper.e-small textarea.e-input, .e-input-group.e-control-wrapper textarea.e-bigger, .e-input-group.e-control-wrapper textarea.e-input.e-bigger, .e-small .e-input-group textarea, .e-small .e-input-group textarea.e-input, .e-bigger .e-input-group.e-control-wrapper textarea, .e-bigger .e-input-group.e-control-wrapper.e-input-focus textarea, .e-bigger .e-input-group.e-control-wrapper textarea.e-input, .e-float-input.e-small textarea, .e-float-input textarea.e-small, .e-float-input.e-control-wrapper.e-small textarea, .e-float-input.e-control-wrapper textarea.e-small, .e-small .e-float-input textarea, .e-small .e-float-input.e-control-wrapper textarea {
  font: inherit;
  line-height: 1.5;
  min-height: 26px;
  padding: 4px 10px 0 10px;
}

.e-input-group.e-input-focus.e-small textarea, .e-input-group.e-input-focus.e-small textarea.e-input, .e-input-group.e-input-focus textarea.e-small, .e-input-group.e-input-focus textarea.e-input.e-small, .e-input-group.e-input-focus textarea.e-input.e-small, .e-input-group.e-control-wrapper.e-input-focus.e-small textarea, .e-input-group.e-control-wrapper.e-input-focus.e-small textarea.e-input, .e-input-group.e-control-wrapper.e-input-focus textarea.e-bigger, .e-input-group.e-control-wrapper.e-input-focus textarea.e-input.e-bigger, .e-small .e-input-group.e-input-focus textarea, .e-small .e-input-group.e-input-focus textarea.e-input, .e-bigger .e-input-group.e-control-wrapper.e-input-focus textarea.e-input {
  font: inherit;
  line-height: 1.5;
  min-height: 26px;
  padding: 4px 10px 0 10px;
}

.e-input-group.e-small.e-bigger textarea.e-input, .e-input-group.e-small textarea.e-input.e-bigger, .e-input-group textarea.e-input.e-bigger.e-small, .e-input-group.e-bigger textarea.e-small, .e-input-group.e-control-wrapper.e-small.e-bigger textarea.e-input, .e-input-group.e-control-wrapper.e-small textarea.e-input.e-bigger, .e-bigger.e-small .e-input-group textarea.e-input, .e-bigger.e-small .e-input-group.e-control-wrapper textarea.e-input, .e-bigger .input-group textarea.e-small, .e-bigger .e-input-group.e-small textarea.e-input, .e-bigger .e-input-group.e-control-wrapper.e-small textarea.e-input, .e-bigger .e-small.e-input-group.e-control-wrapper textarea.e-input, .e-small .input-group textarea.e-bigger, .e-small .e-input-group.e-bigger textarea.e-input, .e-small .e-input-group.e-control-wrapper textarea.e-input.e-bigger, .e-small .e-input-group.e-control-wrapper.e-input-focus textarea.e-input.e-bigger, .e-float-input textarea.e-bigger.e-small, .e-float-input.e-small.e-bigger textarea, .e-float-input.e-small textarea.e-bigger, .e-float-input.e-bigger textarea.e-small, .e-bigger .e-float-input.e-small textarea, .e-bigger.e-small .e-float-input textarea, .e-bigger .float-input textarea.e-small, .e-small .e-float-input.e-bigger textarea, .e-small .float-input textarea.e-bigger, .e-small .e-float-input.e-control-wrapper textarea.e-bigger, .e-float-input.e-control-wrapper.e-small.e-bigger textarea, .e-float-input.e-control-wrapper.e-small textarea.e-bigger, .e-bigger .e-float-input.e-control-wrapper.e-small textarea, .e-bigger.e-small .e-float-input.e-control-wrapper textarea, .e-bigger .e-small.e-float-input.e-control-wrapper textarea {
  font: inherit;
  line-height: 1.5;
  min-height: 32px;
  padding: 4px 12px 0 12px;
}

.e-input-group.e-input-focus textarea.e-input.e-bigger.e-small, .e-input-group.e-input-focus.e-small.e-bigger textarea.e-input, .e-input-group.e-input-focus.e-small textarea.e-input.e-bigger, .e-input-group.e-input-focus.e-bigger textarea.e-small, .e-input-group.e-control-wrapper.e-input-focus.e-small.e-bigger textarea.e-input, .e-input-group.e-control-wrapper.e-input-focus.e-small textarea.e-input.e-bigger, .e-bigger.e-small .e-input-group.e-input-focus textarea.e-input, .e-bigger .e-input-group.e-control-wrapper.e-input-focus.e-small textarea.e-input, .e-bigger.e-small .e-input-group.e-control-wrapper.e-input-focus textarea.e-input, .e-bigger .e-small.e-input-group.e-control-wrapper.e-input-focus textarea.e-input {
  font: inherit;
  line-height: 1.5;
  min-height: 32px;
  padding: 4px 12px 0 12px;
}

.e-filled.e-input-group, .e-filled.e-input-group.e-control-wrapper, .e-filled.e-float-input, .e-filled.e-float-input.e-control-wrapper {
  line-height: 1;
}

.e-filled textarea.e-input.e-css, .e-filled.e-input-group.e-multi-line-input textarea, .e-filled.e-input-group.e-control-wrapper.e-multi-line-input textarea, .e-filled.e-float-input.e-multi-line-input textarea, .e-filled.e-float-input.e-control-wrapper.e-multi-line-input textarea {
  box-sizing: border-box;
}

/*! input theme */
input.e-input.e-css, .e-input-group input.e-input, .e-input-group.e-control-wrapper input.e-input, .e-input-group textarea.e-input, .e-input-group.e-control-wrapper textarea.e-input, textarea.e-input.e-css, .e-float-input input, .e-float-input.e-control-wrapper input, .e-float-input textarea, .e-float-input.e-control-wrapper textarea {
  background: #201f1f;
  border-color: #9a9a9a;
  color: #dadada;
}

.e-input-group, .e-input-group.e-control-wrapper, .e-float-input, .e-float-input.e-input-group, .e-float-input.e-control-wrapper, .e-float-input.e-input-group.e-control-wrapper {
  background: #201f1f;
  color: #dadada;
}

.e-input-group .e-input-group-icon, .e-input-group.e-control-wrapper .e-input-group-icon {
  background: #201f1f;
  border-color: #9a9a9a;
  color: #dadada;
}

.e-input-group.e-disabled .e-input-group-icon, .e-input-group.e-control-wrapper.e-disabled .e-input-group-icon {
  color: #6f6c6c;
}

.e-input.e-css[disabled], .e-input-group .e-input[disabled], .e-input-group.e-control-wrapper .e-input[disabled], .e-input-group.e-disabled, .e-input-group.e-control-wrapper.e-disabled, .e-float-input input[disabled], .e-float-input.e-control-wrapper input[disabled], .e-float-input textarea[disabled], .e-float-input.e-control-wrapper textarea[disabled], .e-float-input.e-disabled, .e-float-input.e-control-wrapper.e-disabled {
  background: #333232;
  color: #6f6c6c;
  border-color: #333232;
}

.e-input-group input.e-input, .e-input-group.e-control-wrapper input.e-input, .e-float-input input, .e-float-input.e-control-wrapper input, .e-input-group textarea.e-input, .e-input-group.e-control-wrapper textarea.e-input, .e-float-input textarea, .e-float-input.e-control-wrapper textarea, .e-input-group .e-input[disabled], .e-input-group.e-control-wrapper .e-input[disabled], .e-input-group.e-disabled input.e-input, .e-input-group.e-control-wrapper.e-disabled input.e-input, .e-input-group.e-disabled textarea.e-input, .e-input-group.e-control-wrapper.e-disabled textarea.e-input {
  background: none;
  color: inherit;
}

.e-input-group .e-input[readonly], .e-input-group.e-control-wrapper .e-input[readonly], .e-float-input input[readonly], .e-float-input.e-control-wrapper input[readonly], .e-float-input textarea[readonly], .e-float-input.e-control-wrapper textarea[readonly] {
  background: none;
  color: inherit;
}

.e-float-input.e-disabled input, .e-float-input.e-control-wrapper.e-disabled input, .e-float-input.e-disabled textarea, .e-float-input.e-control-wrapper.e-disabled textarea, .e-float-input input[disabled], .e-float-input.e-control-wrapper input[disabled], .e-float-input textarea[disabled], .e-float-input.e-control-wrapper textarea[disabled] {
  color: inherit;
}

/*! Added color to textbox for disbaled state */
.e-float-input:not(.e-disabled) input[disabled], .e-float-input.e-control-wrapper:not(.e-disabled) input[disabled], .e-float-input:not(.e-disabled) textarea[disabled], .e-float-input.e-control-wrapper:not(.e-disabled) textarea[disabled] {
  color: #6f6c6c;
}

.e-input-group.e-disabled .e-input-group-icon, .e-input-group.e-control-wrapper.e-disabled .e-input-group-icon {
  background: #333232;
  border-color: #333232;
}

.e-input-group:not(.e-disabled) .e-input-group-icon:hover, .e-input-group.e-control-wrapper:not(.e-disabled) .e-input-group-icon:hover {
  background: #201f1f;
  color: #fff;
}

.e-input.e-css.e-success, .e-input.e-css.e-success:focus, .e-input-group.e-success input.e-input, .e-input-group.e-control-wrapper.e-success input.e-input, .e-input-group.e-success .e-input-group-icon, .e-input-group.e-control-wrapper.e-success .e-input-group-icon, .e-input-group.e-success textarea.e-input, .e-input-group.e-control-wrapper.e-success textarea.e-input {
  border-color: #8eff8d;
}

.e-input.e-css.e-warning, .e-input.e-css.e-warning:focus, .e-input-group.e-warning input.e-input, .e-input-group.e-control-wrapper.e-warning input.e-input, .e-input-group.e-warning .e-input-group-icon, .e-input-group.e-control-wrapper.e-warning .e-input-group-icon, .e-input-group.e-warning textarea.e-input, .e-input-group.e-control-wrapper.e-warning textarea.e-input {
  border-color: #ff9d48;
}

.e-input.e-css.e-error, .e-input.e-css.e-error:focus, .e-input-group.e-error input.e-input, .e-input-group.e-control-wrapper.e-error input.e-input, .e-input-group.e-control-wrapper.e-error .e-input-group-icon, .e-input-group.e-error .e-input-group-icon, .e-input-group.e-error textarea.e-input, .e-input-group.e-control-wrapper.e-error textarea.e-input {
  border-color: #ff5f5f;
}

label.e-float-text, .e-float-input:not(.e-error) input:not(:focus):valid ~ label.e-float-text.e-label-bottom, .e-float-input.e-control-wrapper:not(.e-error) input:not(:focus):valid ~ label.e-float-text.e-label-bottom, .e-float-input.e-bigger:not(.e-error) input:not(:focus):valid ~ label.e-float-text.e-label-bottom, .e-float-input.e-control-wrapper.e-bigger:not(.e-error) input:not(:focus):valid ~ label.e-float-text.e-label-bottom, .e-bigger .e-float-input:not(.e-error) input:not(:focus):valid ~ label.e-float-text.e-label-bottom, .e-bigger .e-float-input.e-control-wrapper:not(.e-error) input:not(:focus):valid ~ label.e-float-text.e-label-bottom, .e-float-input.e-small:not(.e-error) input:not(:focus):valid ~ label.e-float-text.e-label-bottom, .e-float-input.e-control-wrapper.e-small:not(.e-error) input:not(:focus):valid ~ label.e-float-text.e-label-bottom, .e-small .e-float-input:not(.e-error) input:not(:focus):valid ~ label.e-float-text.e-label-bottom, .e-small .e-float-input.e-control-wrapper:not(.e-error) input:not(:focus):valid ~ label.e-float-text.e-label-bottom, .e-float-input.e-small.e-bigger:not(.e-error) input:not(:focus):valid ~ label.e-float-text.e-label-bottom, .e-float-input.e-control-wrapper.e-small.e-bigger:not(.e-error) input:not(:focus):valid ~ label.e-float-text.e-label-bottom, .e-bigger .e-float-input.e-small:not(.e-error) input:not(:focus):valid ~ label.e-float-text.e-label-bottom, .e-bigger .e-float-input.e-control-wrapper.e-small:not(.e-error) input:not(:focus):valid ~ label.e-float-text.e-label-bottom {
  color: #9a9a9a;
}

.e-float-input.e-error label.e-float-text, .e-float-input.e-control-wrapper.e-error label.e-float-text, .e-float-input.e-error input:focus ~ label.e-float-text, .e-float-input.e-control-wrapper.e-error input:focus ~ label.e-float-text, .e-float-input.e-error textarea:focus ~ label.e-float-text, .e-float-input.e-control-wrapper.e-error textarea:focus ~ label.e-float-text {
  color: #ff5f5f;
}

.e-float-input input, .e-float-input textarea, .e-float-input.e-control-wrapper input, .e-float-input.e-control-wrapper textarea {
  border-bottom-color: #9a9a9a;
}

.e-float-input.e-success input, .e-float-input.e-success textarea, .e-float-input.e-control-wrapper.e-success input, .e-float-input.e-control-wrapper.e-success textarea {
  border-color: #8eff8d;
}

.e-float-input.e-warning input, .e-float-input.e-control-wrapper.e-warning input, .e-float-input.e-warning textarea, .e-float-input.e-control-wrapper.e-warning textarea {
  border-color: #ff9d48;
}

.e-float-input.e-error input, .e-float-input.e-control-wrapper.e-error input, .e-float-input.e-error textarea, .e-float-input.e-control-wrapper.e-error textarea {
  border-color: #ff5f5f;
}

.e-float-input:not(.e-input-focus):not(.e-disabled) input:not(:focus):not(:valid) ~ label.e-float-text:not(.e-label-top), .e-float-input:not(.e-input-focus) textarea:not(:focus):not(:valid) ~ label.e-float-text:not(.e-label-top), .e-float-input.e-control-wrapper:not(.e-input-focus):not(.e-disabled) input:not(:focus):not(:valid) ~ label.e-float-text:not(.e-label-top) {
  color: #6f6c6c;
}

.e-input-group:not(.e-disabled) .e-input-group-icon:active, .e-input-group.e-control-wrapper:not(.e-disabled) .e-input-group-icon:active {
  background: #201f1f;
  color: #dadada;
}

input.e-input.e-css::selection, textarea.e-input.e-css::selection, .e-input-group input.e-input::selection, .e-input-group.e-control-wrapper input.e-input::selection, .e-float-input input::selection, .e-float-input.e-control-wrapper input::selection, .e-input-group textarea.e-input::selection, .e-input-group.e-control-wrapper textarea.e-input::selection, .e-float-input textarea::selection, .e-float-input.e-control-wrapper textarea::selection {
  background: #0074cc;
  color: #fff;
}

.e-float-input.e-small textarea::selection, .e-float-input textarea::selection {
  background: #0074cc;
  color: #fff;
}

input.e-input.e-css:-moz-placeholder {
  color: #6f6c6c;
}

.e-input-group input.e-input:-moz-placeholder {
  color: #6f6c6c;
}

.e-input-group input.e-input:-moz-placeholder, .e-input-group.e-control-wrapper input.e-input:-moz-placeholder {
  color: #6f6c6c;
}

input.e-input.e-css:-moz-placeholder {
  color: #6f6c6c;
}

.e-input-group input.e-input:-moz-placeholder {
  color: #6f6c6c;
}

.e-input-group.e-control-wrapper input.e-input:-moz-placeholder {
  color: #6f6c6c;
}

textarea.e-input.e-css:-moz-placeholder {
  color: #6f6c6c;
}

.e-input-group textarea.e-input:-moz-placeholder {
  color: #6f6c6c;
}

.e-input-group.e-control-wrapper textarea.e-input:-moz-placeholder {
  color: #6f6c6c;
}

input.e-input.e-css::-moz-placeholder {
  color: #6f6c6c;
}

.e-input-group input.e-input::-moz-placeholder {
  color: #6f6c6c;
}

.e-input-group.e-control-wrapper input.e-input::-moz-placeholder {
  color: #6f6c6c;
}

textarea.e-input.e-css::-moz-placeholder {
  color: #6f6c6c;
}

.e-input-group textarea.e-input::-moz-placeholder {
  color: #6f6c6c;
}

.e-input-group.e-control-wrapper textarea.e-input::-moz-placeholder {
  color: #6f6c6c;
}

input.e-input.e-css:-ms-input-placeholder {
  color: #6f6c6c;
}

.e-input-group input.e-input:-ms-input-placeholder {
  color: #6f6c6c;
}

.e-input-group.e-control-wrapper input.e-input:-ms-input-placeholder {
  color: #6f6c6c;
}

textarea.e-input.e-css:-ms-input-placeholder {
  color: #6f6c6c;
}

.e-input-group.e-control-wrapper textarea.e-input:-ms-input-placeholder {
  color: #6f6c6c;
}

.e-input-group textarea.e-input:-ms-input-placeholder {
  color: #6f6c6c;
}

input.e-input.e-css::-webkit-input-placeholder {
  color: #6f6c6c;
}

.e-input-group.e-control-wrapper input.e-input::-webkit-input-placeholder {
  color: #6f6c6c;
}

.e-input-group input.e-input::-webkit-input-placeholder {
  color: #6f6c6c;
}

textarea.e-input.e-css::-webkit-input-placeholder {
  color: #6f6c6c;
}

.e-input-group textarea.e-input::-webkit-input-placeholder {
  color: #6f6c6c;
}

.e-input-group.e-control-wrapper textarea.e-input::-webkit-input-placeholder {
  color: #6f6c6c;
}

.e-input-group::before, .e-input-group::after, .e-input-group.e-control-wrapper::before, .e-input-group.e-control-wrapper::after {
  content: '';
}

.e-input-group.e-success::before, .e-input-group.e-success::after, .e-input-group.e-control-wrapper.e-success::before, .e-input-group.e-control-wrapper.e-success::after {
  content: '';
}

.e-input-group.e-warning::before, .e-input-group.e-warning::after, .e-input-group.e-control-wrapper.e-warning::before, .e-input-group.e-control-wrapper.e-warning::after {
  content: '';
}

.e-input-group.e-error::before, .e-input-group.e-error::after, .e-input-group.e-control-wrapper.e-error::before, .e-input-group.e-control-wrapper.e-error::after {
  content: '';
}

.e-input-group.e-success .e-input-group-icon, .e-input-group.e-success:not(.e-disabled):not(:active) .e-input-group-icon:hover, .e-input-group.e-control-wrapper.e-success .e-input-group-icon, .e-input-group.e-control-wrapper.e-success:not(.e-disabled):not(:active) .e-input-group-icon:hover {
  color: #dadada;
}

.e-input-group.e-warning .e-input-group-icon, .e-input-group.e-warning:not(.e-disabled):not(:active) .e-input-group-icon:hover, .e-input-group.e-control-wrapper.e-warning .e-input-group-icon, .e-input-group.e-control-wrapper.e-warning:not(.e-disabled):not(:active) .e-input-group-icon:hover {
  color: #dadada;
}

.e-input-group.e-error .e-input-group-icon, .e-input-group.e-error:not(.e-disabled):not(:active) .e-input-group-icon:hover, .e-input-group.e-control-wrapper.e-error .e-input-group-icon, .e-input-group.e-control-wrapper.e-error:not(.e-disabled):not(:active) .e-input-group-icon:hover {
  color: #dadada;
}

.e-input-group input.e-input, .e-input-group.e-control-wrapper input.e-input, .e-input-group textarea.e-input, .e-input-group.e-control-wrapper textarea.e-input {
  border-color: #9a9a9a;
}

.e-input.e-css:focus:not(.e-success):not(.e-warning):not(.e-error), .e-float-input:not(.e-success):not(.e-warning):not(.e-error):not(.e-input-group) input:focus, .e-float-input:not(.e-success):not(.e-warning):not(.e-error):not(.e-input-group) textarea:focus, .e-float-input.e-control-wrapper:not(.e-success):not(.e-warning):not(.e-error):not(.e-input-group) input:focus, .e-float-input.e-control-wrapper:not(.e-success):not(.e-warning):not(.e-error):not(.e-input-group) textarea:focus {
  border-color: #38a9ff;
}

.e-input-group:not(.e-success):not(.e-warning):not(.e-error) input.e-input:focus, .e-input-group.e-control-wrapper:not(.e-success):not(.e-warning):not(.e-error) input.e-input:focus, .e-input-group:not(.e-success):not(.e-warning):not(.e-error) textarea.e-input:focus, .e-input-group.e-control-wrapper:not(.e-success):not(.e-warning):not(.e-error) textarea.e-input:focus {
  border-color: #38a9ff;
}

.e-input-group.e-input-focus:not(.e-success):not(.e-warning):not(.e-error), .e-input-group.e-control-wrapper.e-input-focus:not(.e-success):not(.e-warning):not(.e-error) {
  border-color: #38a9ff;
}

.e-input-group.e-input-focus:not(.e-success):not(.e-warning):not(.e-error) .e-input:focus, .e-input-group.e-control-wrapper.e-input-focus:not(.e-success):not(.e-warning):not(.e-error) .e-input:focus {
  border-color: #9a9a9a;
}

.e-input-group.e-input-focus:not(.e-success):not(.e-warning):not(.e-error) .e-input:first-child:focus, .e-input-group.e-input-focus.e-rtl:not(.e-success):not(.e-warning):not(.e-error) span:last-child.e-input-group-icon, .e-input-group.e-control-wrapper.e-input-focus:not(.e-success):not(.e-warning):not(.e-error) .e-input:first-child:focus, .e-input-group.e-control-wrapper.e-input-focus.e-rtl:not(.e-success):not(.e-warning):not(.e-error) span:last-child.e-input-group-icon {
  border-color: transparent;
}

.e-input-group.e-input-focus.e-rtl:not(.e-success):not(.e-warning):not(.e-error) span.e-input-group-icon, .e-input-group.e-input-focus.e-rtl:not(.e-success):not(.e-warning):not(.e-error) span:first-child.e-input-group-icon, .e-input-focus.e-rtl:not(.e-success):not(.e-warning):not(.e-error) .e-input:first-child:focus, .e-input-focus:not(.e-success):not(.e-warning):not(.e-error) .e-input:last-child:focus, .e-input-group.e-input-focus:not(.e-success):not(.e-warning):not(.e-error) span.e-input-group-icon, .e-input-group.e-control-wrapper.e-input-focus.e-rtl:not(.e-success):not(.e-warning):not(.e-error) span.e-input-group-icon, .e-input-group.e-control-wrapper.e-input-focus.e-rtl:not(.e-success):not(.e-warning):not(.e-error) span:first-child.e-input-group-icon, .e-input-focus.e-control-wrapper.e-rtl:not(.e-success):not(.e-warning):not(.e-error) .e-input:first-child:focus, .e-input-focus.e-control-wrapper:not(.e-success):not(.e-warning):not(.e-error) .e-input:last-child:focus, .e-input-group.e-control-wrapper.e-input-focus:not(.e-success):not(.e-warning):not(.e-error) span.e-input-group-icon {
  border-color: transparent;
}

.e-input-group.e-input-focus:not(.e-success):not(.e-warning):not(.e-error) span:first-child.e-input-group-icon, .e-input-group.e-input-focus.e-rtl:not(.e-success):not(.e-warning):not(.e-error) .e-input:last-child:focus, .e-input-group.e-input-focus:not(.e-success):not(.e-warning):not(.e-error) span:first-child.e-input-group-icon, .e-input-group.e-control-wrapper.e-input-focus:not(.e-success):not(.e-warning):not(.e-error) span:first-child.e-input-group-icon, .e-input-group.e-control-wrapper.e-input-focus.e-rtl:not(.e-success):not(.e-warning):not(.e-error) .e-input:last-child:focus, .e-input-group.e-control-wrapper.e-input-focus:not(.e-success):not(.e-warning):not(.e-error) span:first-child.e-input-group-icon {
  border-color: transparent;
}

.e-input-group:not(.e-success):not(.e-warning):not(.e-error), .e-input-group.e-control-wrapper:not(.e-success):not(.e-warning):not(.e-error) {
  border-color: #9a9a9a;
}

.e-input-group:not(.e-disabled):active:not(.e-success):not(.e-warning):not(.e-error), .e-input-group.e-control-wrapper:not(.e-disabled):active:not(.e-success):not(.e-warning):not(.e-error) {
  border-color: #38a9ff;
}

.e-input-group.e-disabled:not(.e-success):not(.e-warning):not(.e-error), .e-input-group.e-control-wrapper.e-disabled:not(.e-success):not(.e-warning):not(.e-error) {
  border-color: #333232;
}

.e-input-group, .e-input-group.e-control-wrapper {
  border-bottom-color: #9a9a9a;
}

.e-input-group.e-input-focus:not(.e-success):not(.e-warning):not(.e-error) .e-input:focus, .e-input-group.e-control-wrapper.e-input-focus:not(.e-success):not(.e-warning):not(.e-error) .e-input:focus {
  border-bottom-color: transparent;
  border-top-color: transparent;
}

.e-input-group.e-success, .e-input-group.e-control-wrapper.e-success, .e-input-group.e-success:not(.e-float-icon-left), .e-input-group.e-control-wrapper.e-success:not(.e-float-icon-left) {
  border-color: #8eff8d;
}

.e-input-group.e-warning, .e-input-group.e-control-wrapper.e-warning, .e-input-group.e-warning:not(.e-float-icon-left), .e-input-group.e-control-wrapper.e-warning:not(.e-float-icon-left) {
  border-color: #ff9d48;
}

.e-input-group.e-error, .e-input-group.e-control-wrapper.e-error, .e-input-group.e-error:not(.e-float-icon-left), .e-input-group.e-control-wrapper.e-error:not(.e-float-icon-left) {
  border-color: #ff5f5f;
}

.e-float-input .e-clear-icon, .e-float-input.e-control-wrapper .e-clear-icon, .e-input-group .e-clear-icon, .e-input-group.e-control-wrapper .e-clear-icon {
  color: #dadada;
}

.e-float-input.e-disabled .e-clear-icon, .e-float-input.e-control-wrapper.e-disabled .e-clear-icon, .e-input-group.e-disabled .e-clear-icon, .e-input-group.e-control-wrapper.e-disabled .e-clear-icon {
  color: #6f6c6c;
}

.e-float-input.e-input-focus .e-input:focus, .e-float-input.e-control-wrapper.e-input-focus .e-input:focus {
  border-bottom-color: transparent;
  border-top-color: transparent;
}

.e-float-input:not(.e-error) input:focus ~ label.e-float-text, .e-float-input:not(.e-error) input:valid ~ label.e-float-text, .e-float-input:not(.e-error) input ~ label.e-label-top.e-float-text, .e-float-input:not(.e-error) input[readonly] ~ label.e-label-top.e-float-text, .e-float-input:not(.e-error) input[disabled] ~ label.e-label-top.e-float-text, .e-float-input:not(.e-error) input label.e-float-text.e-label-top, .e-float-input.e-bigger:not(.e-error) input:focus ~ label.e-float-text, .e-float-input.e-bigger:not(.e-error) input:valid ~ label.e-float-text, .e-float-input.e-bigger:not(.e-error) input ~ label.e-label-top.e-float-text, .e-float-input.e-bigger:not(.e-error) input[readonly] ~ label.e-label-top.e-float-text, .e-float-input.e-bigger:not(.e-error) input[disabled] ~ label.e-label-top.e-float-text, .e-bigger .e-float-input:not(.e-error) input:focus ~ label.e-float-text, .e-bigger .e-float-input:not(.e-error) input:valid ~ label.e-float-text, .e-bigger .e-float-input:not(.e-error) input ~ label.e-label-top.e-float-text, .e-bigger .e-float-input:not(.e-error) input[readonly] ~ label.e-label-top.e-float-text, .e-bigger .e-float-input:not(.e-error) input[disabled] ~ label.e-label-top.e-float-text {
  color: #dadada;
}

.e-float-input.e-control-wrapper:not(.e-error) input:focus ~ label.e-float-text, .e-float-input.e-control-wrapper:not(.e-error) input:valid ~ label.e-float-text, .e-float-input.e-control-wrapper:not(.e-error) input ~ label.e-label-top.e-float-text, .e-float-input.e-control-wrapper:not(.e-error) input[readonly] ~ label.e-label-top.e-float-text, .e-float-input.e-control-wrapper:not(.e-error) input[disabled] ~ label.e-label-top.e-float-text, .e-float-input .e-control-wrapper:not(.e-error) input label.e-float-text.e-label-top, .e-float-input.e-control-wrapper.e-bigger:not(.e-error) input:focus ~ label.e-float-text, .e-float-input.e-control-wrapper.e-bigger:not(.e-error) input:valid ~ label.e-float-text, .e-float-input.e-control-wrapper.e-bigger:not(.e-error) input ~ label.e-label-top.e-float-text, .e-float-input.e-control-wrapper.e-bigger:not(.e-error) input[readonly] ~ label.e-label-top.e-float-text, .e-float-input.e-control-wrapper.e-bigger:not(.e-error) input[disabled] ~ label.e-label-top.e-float-text, .e-bigger .e-float-input.e-control-wrapper:not(.e-error) input:focus ~ label.e-float-text, .e-bigger .e-float-input.e-control-wrapper:not(.e-error) input:valid ~ label.e-float-text, .e-bigger .e-float-input.e-control-wrapper:not(.e-error) input ~ label.e-label-top.e-float-text, .e-bigger .e-float-input.e-control-wrapper:not(.e-error) input[readonly] ~ label.e-label-top.e-float-text, .e-bigger .e-float-input.e-control-wrapper:not(.e-error) input[disabled] ~ label.e-label-top.e-float-text {
  color: #dadada;
}

.e-float-input.e-small:not(.e-error) input:focus ~ label.e-float-text, .e-float-input.e-small:not(.e-error) input:valid ~ label.e-float-text, .e-float-input.e-small:not(.e-error) input ~ label.e-label-top.e-float-text, .e-float-input.e-small:not(.e-error) input[readonly] ~ label.e-float-text, .e-float-input.e-small:not(.e-error) input[disabled] ~ label.e-float-text, .e-small .e-float-input:not(.e-error) input:focus ~ label.e-float-text, .e-small .e-float-input:not(.e-error) input:valid ~ label.e-float-text, .e-small .e-float-input:not(.e-error) input ~ label.e-label-top.e-float-text, .e-small .e-float-input:not(.e-error) input[readonly] ~ label.e-float-text.e-label-top, .e-small .e-float-input:not(.e-error) input[disabled] ~ label.e-float-text.e-label-top, .e-float-input.e-small.e-bigger:not(.e-error) input:focus ~ label.e-float-text, .e-float-input.e-small.e-bigger:not(.e-error) input:valid ~ label.e-float-text, .e-float-input.e-small.e-bigger:not(.e-error) input ~ label.e-label-top.e-float-text, .e-float-input.e-small.e-bigger:not(.e-error) input[readonly] ~ label.e-label-top.e-float-text, .e-float-input.e-small.e-bigger:not(.e-error) input[disabled] ~ label.e-label-top.e-float-text, .e-bigger .e-float-input.e-small:not(.e-error) input:focus ~ label.e-float-text, .e-bigger .e-float-input.e-small:not(.e-error) input:valid ~ label.e-float-text, .e-bigger .e-float-input.e-small:not(.e-error) input ~ label.e-label-top.e-float-text, .e-bigger .e-float-input.e-small:not(.e-error) input[readonly] ~ label.e-label-top.e-float-text, .e-bigger .e-float-input.e-small:not(.e-error) input[disabled] ~ label.e-label-top.e-float-text {
  color: #dadada;
}

.e-float-input.e-control-wrapper.e-small:not(.e-error) input:focus ~ label.e-float-text, .e-float-input.e-control-wrapper.e-small:not(.e-error) input:valid ~ label.e-float-text, .e-float-input.e-control-wrapper.e-small:not(.e-error) input ~ label.e-label-top.e-float-text, .e-float-input.e-control-wrapper.e-small:not(.e-error) input[readonly] ~ label.e-float-text, .e-float-input.e-control-wrapper.e-small:not(.e-error) input[disabled] ~ label.e-float-text, .e-small .e-float-input.e-control-wrapper:not(.e-error) input:focus ~ label.e-float-text, .e-small .e-float-input.e-control-wrapper:not(.e-error) input:valid ~ label.e-float-text, .e-small .e-float-input.e-control-wrapper:not(.e-error) input ~ label.e-label-top.e-float-text, .e-small .e-float-input.e-control-wrapper:not(.e-error) input[readonly] ~ label.e-float-text.e-label-top, .e-small .e-float-input.e-control-wrapper:not(.e-error) input[disabled] ~ label.e-float-text.e-label-top, .e-float-input.e-control-wrapper.e-small.e-bigger:not(.e-error) input:focus ~ label.e-float-text, .e-float-input.e-control-wrapper.e-small.e-bigger:not(.e-error) input:valid ~ label.e-float-text, .e-float-input.e-control-wrapper.e-small.e-bigger:not(.e-error) input ~ label.e-label-top.e-float-text, .e-float-input.e-control-wrapper.e-small.e-bigger:not(.e-error) input[readonly] ~ label.e-label-top.e-float-text, .e-float-input.e-control-wrapper.e-small.e-bigger:not(.e-error) input[disabled] ~ label.e-label-top.e-float-text, .e-bigger .e-float-input.e-control-wrapper.e-small:not(.e-error) input:focus ~ label.e-float-text, .e-bigger .e-float-input.e-control-wrapper.e-small:not(.e-error) input:valid ~ label.e-float-text, .e-bigger .e-float-input.e-control-wrapper.e-small:not(.e-error) input ~ label.e-label-top.e-float-text, .e-bigger .e-float-input.e-control-wrapper.e-small:not(.e-error) input[readonly] ~ label.e-label-top.e-float-text, .e-bigger .e-float-input.e-control-wrapper.e-small:not(.e-error) input[disabled] ~ label.e-label-top.e-float-text {
  color: #dadada;
}

.e-float-input:not(.e-error) textarea:not(:focus):valid ~ label.e-float-text.e-label-bottom, .e-float-input.e-control-wrapper:not(.e-error) textarea:not(:focus):valid ~ label.e-float-text.e-label-bottom, .e-float-input.e-bigger:not(.e-error) textarea:not(:focus):valid ~ label.e-float-text.e-label-bottom, .e-float-input.e-control-wrapper.e-bigger:not(.e-error) textarea:not(:focus):valid ~ label.e-float-text.e-label-bottom, .e-bigger .e-float-input:not(.e-error) textarea:not(:focus):valid ~ label.e-float-text.e-label-bottom, .e-bigger .e-float-input.e-control-wrapper:not(.e-error) textarea:not(:focus):valid ~ label.e-float-text.e-label-bottom, .e-float-input.e-small:not(.e-error) textarea:not(:focus):valid ~ label.e-float-text.e-label-bottom, .e-float-input.e-control-wrapper.e-small:not(.e-error) textarea:not(:focus):valid ~ label.e-float-text.e-label-bottom, .e-small .e-float-input:not(.e-error) textarea:not(:focus):valid ~ label.e-float-text.e-label-bottom, .e-small .e-float-input.e-control-wrapper:not(.e-error) textarea:not(:focus):valid ~ label.e-float-text.e-label-bottom, .e-float-input.e-small.e-bigger:not(.e-error) textarea:not(:focus):valid ~ label.e-float-text.e-label-bottom, .e-float-input.e-control-wrapper.e-small.e-bigger:not(.e-error) textarea:not(:focus):valid ~ label.e-float-text.e-label-bottom, .e-bigger .e-float-input.e-small:not(.e-error) textarea:not(:focus):valid ~ label.e-float-text.e-label-bottom, .e-bigger .e-float-input.e-control-wrapper.e-small:not(.e-error) textarea:not(:focus):valid ~ label.e-float-text.e-label-bottom {
  color: #9a9a9a;
}

.e-float-input:not(.e-input-focus):not(.e-disabled) textarea:not(:focus):not(:valid) ~ label.e-float-text:not(.e-label-top), .e-float-input:not(.e-input-focus) textarea:not(:focus):not(:valid) ~ label.e-float-text:not(.e-label-top), .e-float-input.e-control-wrapper:not(.e-input-focus):not(.e-disabled) textarea:not(:focus):not(:valid) ~ label.e-float-text:not(.e-label-top) {
  color: #6f6c6c;
}

.e-float-input:not(.e-error) textarea:focus ~ label.e-float-text, .e-float-input:not(.e-error) textarea:valid ~ label.e-float-text, .e-float-input:not(.e-error) textarea ~ label.e-label-top.e-float-text, .e-float-input:not(.e-error) textarea[readonly] ~ label.e-label-top.e-float-text, .e-float-input:not(.e-error) textarea[disabled] ~ label.e-label-top.e-float-text, .e-float-input:not(.e-error) textarea label.e-float-text.e-label-top, .e-float-input.e-bigger:not(.e-error) textarea:focus ~ label.e-float-text, .e-float-input.e-bigger:not(.e-error) textarea:valid ~ label.e-float-text, .e-float-input.e-bigger:not(.e-error) textarea ~ label.e-label-top.e-float-text, .e-float-input.e-bigger:not(.e-error) textarea[readonly] ~ label.e-label-top.e-float-text, .e-float-input.e-bigger:not(.e-error) textarea[disabled] ~ label.e-label-top.e-float-text, .e-bigger .e-float-input:not(.e-error) textarea:focus ~ label.e-float-text, .e-bigger .e-float-input:not(.e-error) textarea:valid ~ label.e-float-text, .e-bigger .e-float-input:not(.e-error) textarea ~ label.e-label-top.e-float-text, .e-bigger .e-float-input:not(.e-error) textarea[readonly] ~ label.e-label-top.e-float-text, .e-bigger .e-float-input:not(.e-error) textarea[disabled] ~ label.e-label-top.e-float-text {
  color: #dadada;
}

.e-float-input.e-control-wrapper:not(.e-error) textarea:focus ~ label.e-float-text, .e-float-input.e-control-wrapper:not(.e-error) textarea:valid ~ label.e-float-text, .e-float-input.e-control-wrapper:not(.e-error) textarea ~ label.e-label-top.e-float-text, .e-float-input.e-control-wrapper:not(.e-error) textarea[readonly] ~ label.e-label-top.e-float-text, .e-float-input.e-control-wrapper:not(.e-error) textarea[disabled] ~ label.e-label-top.e-float-text, .e-float-input.e-control-wrapper:not(.e-error) textarea label.e-float-text.e-label-top, .e-float-input.e-control-wrapper.e-bigger:not(.e-error) textarea:focus ~ label.e-float-text, .e-float-input.e-control-wrapper.e-bigger:not(.e-error) textarea:valid ~ label.e-float-text, .e-float-input.e-control-wrapper.e-bigger:not(.e-error) textarea ~ label.e-label-top.e-float-text, .e-float-input.e-control-wrapper.e-bigger:not(.e-error) textarea[readonly] ~ label.e-label-top.e-float-text, .e-float-input.e-control-wrapper.e-bigger:not(.e-error) textarea[disabled] ~ label.e-label-top.e-float-text, .e-bigger .e-float-input.e-control-wrapper:not(.e-error) textarea:focus ~ label.e-float-text, .e-bigger .e-float-input.e-control-wrapper:not(.e-error) textarea:valid ~ label.e-float-text, .e-bigger .e-float-input.e-control-wrapper:not(.e-error) textarea ~ label.e-label-top.e-float-text, .e-bigger .e-float-input.e-control-wrapper:not(.e-error) textarea[readonly] ~ label.e-label-top.e-float-text, .e-bigger .e-float-input.e-control-wrapper:not(.e-error) textarea[disabled] ~ label.e-label-top.e-float-text {
  color: #dadada;
}

.e-float-input.e-small:not(.e-error) textarea:focus ~ label.e-float-text, .e-float-input.e-small:not(.e-error) textarea:valid ~ label.e-float-text, .e-float-input.e-small:not(.e-error) textarea ~ label.e-label-top.e-float-text, .e-float-input.e-small:not(.e-error) textarea[readonly] ~ label.e-float-text, .e-float-input.e-small:not(.e-error) textarea[disabled] ~ label.e-float-text, .e-small .e-float-input:not(.e-error) textarea:focus ~ label.e-float-text, .e-small .e-float-input:not(.e-error) textarea:valid ~ label.e-float-text, .e-small .e-float-input:not(.e-error) textarea ~ label.e-label-top.e-float-text, .e-small .e-float-input:not(.e-error) textarea[readonly] ~ label.e-float-text.e-label-top, .e-small .e-float-input:not(.e-error) textarea[disabled] ~ label.e-float-text.e-label-top, .e-float-input.e-small.e-bigger:not(.e-error) textarea:focus ~ label.e-float-text, .e-float-input.e-small.e-bigger:not(.e-error) textarea:valid ~ label.e-float-text, .e-float-input.e-small.e-bigger:not(.e-error) textarea ~ label.e-label-top.e-float-text, .e-float-input.e-small.e-bigger:not(.e-error) textarea[readonly] ~ label.e-label-top.e-float-text, .e-float-input.e-small.e-bigger:not(.e-error) textarea[disabled] ~ label.e-label-top.e-float-text, .e-bigger .e-float-input.e-small:not(.e-error) textarea:focus ~ label.e-float-text, .e-bigger .e-float-input.e-small:not(.e-error) textarea:valid ~ label.e-float-text, .e-bigger .e-float-input.e-small:not(.e-error) textarea ~ label.e-label-top.e-float-text, .e-bigger .e-float-input.e-small:not(.e-error) textarea[readonly] ~ label.e-label-top.e-float-text, .e-bigger .e-float-input.e-small:not(.e-error) textarea[disabled] ~ label.e-label-top.e-float-text {
  color: #dadada;
}

.e-float-input.e-control-wrapper.e-small:not(.e-error) textarea:focus ~ label.e-float-text, .e-float-input.e-control-wrapper.e-small:not(.e-error) textarea:valid ~ label.e-float-text, .e-float-input.e-control-wrapper.e-small:not(.e-error) textarea ~ label.e-label-top.e-float-text, .e-float-input.e-control-wrapper.e-small:not(.e-error) textarea[readonly] ~ label.e-float-text, .e-float-input.e-control-wrapper.e-small:not(.e-error) textarea[disabled] ~ label.e-float-text, .e-small .e-float-input.e-control-wrapper:not(.e-error) textarea:focus ~ label.e-float-text, .e-small .e-float-input.e-control-wrapper:not(.e-error) textarea:valid ~ label.e-float-text, .e-small .e-float-input.e-control-wrapper:not(.e-error) textarea ~ label.e-label-top.e-float-text, .e-small .e-float-input.e-control-wrapper:not(.e-error) textarea[readonly] ~ label.e-float-text.e-label-top, .e-small .e-float-input.e-control-wrapper:not(.e-error) textarea[disabled] ~ label.e-float-text.e-label-top, .e-float-input.e-control-wrapper.e-small.e-bigger:not(.e-error) textarea:focus ~ label.e-float-text, .e-float-input.e-control-wrapper.e-small.e-bigger:not(.e-error) textarea:valid ~ label.e-float-text, .e-float-input.e-control-wrapper.e-small.e-bigger:not(.e-error) textarea ~ label.e-label-top.e-float-text, .e-float-input.e-control-wrapper.e-small.e-bigger:not(.e-error) textarea[readonly] ~ label.e-label-top.e-float-text, .e-float-input.e-control-wrapper.e-small.e-bigger:not(.e-error) textarea[disabled] ~ label.e-label-top.e-float-text, .e-bigger .e-float-input.e-control-wrapper.e-small:not(.e-error) textarea:focus ~ label.e-float-text, .e-bigger .e-float-input.e-control-wrapper.e-small:not(.e-error) textarea:valid ~ label.e-float-text, .e-bigger .e-float-input.e-control-wrapper.e-small:not(.e-error) textarea ~ label.e-label-top.e-float-text, .e-bigger .e-float-input.e-control-wrapper.e-small:not(.e-error) textarea[readonly] ~ label.e-label-top.e-float-text, .e-bigger .e-float-input.e-control-wrapper.e-small:not(.e-error) textarea[disabled] ~ label.e-label-top.e-float-text {
  color: #dadada;
}

.e-float-input.e-input-group.e-disabled .e-float-text, .e-float-input.e-input-group.e-disabled .e-float-text.e-label-top, .e-float-input input[disabled] ~ label.e-float-text, .e-float-input input[disabled] ~ label.e-label-top.e-float-text, .e-float-input.e-disabled label.e-float-text, .e-float-input.e-disabled label.e-float-text.e-label-top, .e-float-input:not(.e-error) input[disabled] ~ label.e-float-text, .e-float-input:not(.e-error) input[disabled] ~ label.e-label-top.e-float-text, .e-float-input.e-bigger:not(.e-error) input[disabled] ~ label.e-float-text, .e-float-input.e-bigger:not(.e-error) input[disabled] ~ label.e-float-text.e-label-top, .e-float-input.e-small:not(.e-error) input[disabled] ~ label.e-float-text, .e-float-input.e-small:not(.e-error) input[disabled] ~ label.e-float-text.e-label-top, .e-float-input.e-small.e-bigger:not(.e-error) input[disabled] ~ label.e-float-text, .e-float-input.e-small.e-bigger:not(.e-error) input[disabled] ~ label.e-float-text.e-label-top {
  color: #6f6c6c;
}

.e-float-input textarea[disabled] ~ label.e-float-text, .e-float-input textarea[disabled] ~ label.e-label-top.e-float-text, .e-float-input:not(.e-error) textarea[disabled] ~ label.e-float-text, .e-float-input:not(.e-error) textarea[disabled] ~ label.e-label-top.e-float-text, .e-float-input.e-bigger:not(.e-error) textarea[disabled] ~ label.e-float-text, .e-float-input.e-bigger:not(.e-error) textarea[disabled] ~ label.e-float-text.e-label-top, .e-float-input.e-small:not(.e-error) textarea[disabled] ~ label.e-float-text, .e-float-input.e-small:not(.e-error) textarea[disabled] ~ label.e-float-text.e-label-top, .e-float-input.e-small.e-bigger:not(.e-error) textarea[disabled] ~ label.e-float-text, .e-float-input.e-small.e-bigger:not(.e-error) textarea[disabled] ~ label.e-float-text.e-label-top {
  color: #6f6c6c;
}

.e-float-input.e-control-wrapper.e-input-group.e-disabled .e-float-text, .e-float-input.e-control-wrapper.e-input-group.e-disabled .e-float-text.e-label-top, .e-float-input.e-control-wrapper.e-disabled input[disabled] ~ label.e-float-text, .e-float-input.e-control-wrapper.e-disabled input[disabled] ~ label.e-label-top.e-float-text, .e-float-input.e-control-wrapper input[disabled] ~ label.e-float-text, .e-float-input.e-control-wrapper input[disabled] ~ label.e-label-top.e-float-text, .e-float-input.e-control-wrapper.e-disabled label.e-float-text, .e-float-input.e-control-wrapper.e-disabled label.e-float-text.e-label-top, .e-float-input.e-control-wrapper:not(.e-error) input[disabled] ~ label.e-float-text, .e-float-input.e-control-wrapper:not(.e-error) input[disabled] ~ label.e-label-top.e-float-text, .e-float-input.e-control-wrapper.e-bigger:not(.e-error) input[disabled] ~ label.e-float-text, .e-float-input.e-control-wrapper.e-bigger:not(.e-error) input[disabled] ~ label.e-float-text.e-label-top, .e-float-input.e-control-wrapper.e-small:not(.e-error) input[disabled] ~ label.e-float-text, .e-float-input.e-control-wrapper.e-small:not(.e-error) input[disabled] ~ label.e-float-text.e-label-top, .e-float-input.e-control-wrapper.e-small.e-bigger:not(.e-error) input[disabled] ~ label.e-float-text, .e-float-input.e-control-wrapper.e-small.e-bigger:not(.e-error) input[disabled] ~ label.e-float-text.e-label-top, .e-float-input.e-control-wrapper.e-disabled:not(.e-error) input[disabled] ~ label.e-float-text, .e-float-input.e-control-wrapper.e-disabled:not(.e-error) input[disabled] ~ label.e-label-top.e-float-text, .e-float-input.e-control-wrapper.e-disabled.e-bigger:not(.e-error) input[disabled] ~ label.e-float-text, .e-float-input.e-control-wrapper.e-disabled.e-bigger:not(.e-error) input[disabled] ~ label.e-float-text.e-label-top, .e-float-input.e-control-wrapper.e-disabled.e-small:not(.e-error) input[disabled] ~ label.e-float-text, .e-float-input.e-control-wrapper.e-disabled.e-small:not(.e-error) input[disabled] ~ label.e-float-text.e-label-top, .e-float-input.e-control-wrapper.e-disabled.e-small.e-bigger:not(.e-error) input[disabled] ~ label.e-float-text, .e-float-input.e-control-wrapper.e-disabled.e-small.e-bigger:not(.e-error) input[disabled] ~ label.e-float-text.e-label-top {
  color: #6f6c6c;
}

.e-float-input.e-control-wrapper.e-disabled textarea[disabled] ~ label.e-float-text, .e-float-input.e-control-wrapper.e-disabled textarea[disabled] ~ label.e-label-top.e-float-text, .e-float-input.e-control-wrapper textarea[disabled] ~ label.e-float-text, .e-float-input.e-control-wrapper textarea[disabled] ~ label.e-label-top.e-float-text, .e-float-input.e-control-wrapper:not(.e-error) textarea[disabled] ~ label.e-float-text, .e-float-input.e-control-wrapper:not(.e-error) textarea[disabled] ~ label.e-label-top.e-float-text, .e-float-input.e-control-wrapper.e-bigger:not(.e-error) textarea[disabled] ~ label.e-float-text, .e-float-input.e-control-wrapper.e-bigger:not(.e-error) textarea[disabled] ~ label.e-float-text.e-label-top, .e-float-input.e-control-wrapper.e-small:not(.e-error) textarea[disabled] ~ label.e-float-text, .e-float-input.e-control-wrapper.e-small:not(.e-error) textarea[disabled] ~ label.e-float-text.e-label-top, .e-float-input.e-control-wrapper.e-small.e-bigger:not(.e-error) textarea[disabled] ~ label.e-float-text, .e-float-input.e-control-wrapper.e-small.e-bigger:not(.e-error) textarea[disabled] ~ label.e-float-text.e-label-top, .e-float-input.e-control-wrapper.e-disabled:not(.e-error) textarea[disabled] ~ label.e-float-text, .e-float-input.e-control-wrapper.e-disabled:not(.e-error) textarea[disabled] ~ label.e-label-top.e-float-text, .e-float-input.e-control-wrapper.e-disabled.e-bigger:not(.e-error) textarea[disabled] ~ label.e-float-text, .e-float-input.e-control-wrapper.e-disabled.e-bigger:not(.e-error) textarea[disabled] ~ label.e-float-text.e-label-top, .e-float-input.e-control-wrapper.e-disabled.e-small:not(.e-error) textarea[disabled] ~ label.e-float-text, .e-float-input.e-control-wrapper.e-disabled.e-small:not(.e-error) textarea[disabled] ~ label.e-float-text.e-label-top, .e-float-input.e-control-wrapper.e-disabled.e-small.e-bigger:not(.e-error) textarea[disabled] ~ label.e-float-text, .e-float-input.e-control-wrapper.e-disabled.e-small.e-bigger:not(.e-error) textarea[disabled] ~ label.e-float-text.e-label-top {
  color: #6f6c6c;
}

input.e-input.e-css:hover:not(.e-success):not(.e-warning):not(.e-error):not([disabled]):not(:focus), textarea.e-input.e-css:hover:not(.e-success):not(.e-warning):not(.e-error):not([disabled]) {
  border-color: #fff;
}

.e-input-group:hover:not(.e-success):not(.e-warning):not(.e-error):not(.e-disabled), .e-input-group.e-control-wrapper:hover:not(.e-success):not(.e-warning):not(.e-error):not(.e-disabled), .e-float-input:hover:not(.e-success):not(.e-warning):not(.e-error):not(.e-disabled), .e-float-input:hover:not(.e-input-group):not(.e-success):not(.e-warning):not(.e-error):not(.e-disabled) input:not([disabled]), .e-float-input:hover:not(.e-input-group):not(.e-success):not(.e-warning):not(.e-error):not(.e-disabled) textarea:not([disabled]), .e-float-input.e-control-wrapper:hover:not(.e-success):not(.e-warning):not(.e-error):not(.e-disabled), .e-float-input.e-control-wrapper:hover:not(.e-input-group):not(.e-success):not(.e-warning):not(.e-error):not(.e-disabled) input:not([disabled]), .e-float-input.e-control-wrapper:hover:not(.e-input-group):not(.e-success):not(.e-warning):not(.e-error):not(.e-disabled) textarea:not([disabled]) {
  border-color: #fff;
}

.e-input-group.e-input-focus:hover:not(.e-success):not(.e-warning):not(.e-error):not(.e-disabled), .e-input-group.e-control-wrapper.e-input-focus:hover:not(.e-success):not(.e-warning):not(.e-error):not(.e-disabled), .e-float-input.e-input-focus:hover:not(.e-success):not(.e-warning):not(.e-error):not(.e-disabled), .e-float-input.e-control-wrapper.e-input-focus:hover:not(.e-success):not(.e-warning):not(.e-error):not(.e-disabled), .e-float-input.e-input-focus:hover:not(.e-input-group):not(.e-success):not(.e-warning):not(.e-error):not(.e-disabled) input:not([disabled]), .e-float-input.e-control-wrapper.e-input-focus:hover:not(.e-input-group):not(.e-success):not(.e-warning):not(.e-error):not(.e-disabled) input:not([disabled]), .e-float-input.e-input-focus:hover:not(.e-input-group):not(.e-success):not(.e-warning):not(.e-error):not(.e-disabled) textarea:not([disabled]), .e-float-input.e-control-wrapper.e-input-focus:hover:not(.e-input-group):not(.e-success):not(.e-warning):not(.e-error):not(.e-disabled) textarea:not([disabled]) {
  border-color: #38a9ff;
}

.e-input-group:active:hover:not(.e-success):not(.e-warning):not(.e-error):not(.e-disabled), .e-input-group.e-control-wrapper:active:hover:not(.e-success):not(.e-warning):not(.e-error):not(.e-disabled), .e-float-input:active:hover:not(.e-success):not(.e-warning):not(.e-error):not(.e-disabled), .e-float-input.e-control-wrapper:active:hover:not(.e-success):not(.e-warning):not(.e-error):not(.e-disabled), .e-float-input:active:hover:not(.e-input-group):not(.e-success):not(.e-warning):not(.e-error):not(.e-disabled) input:not([disabled]), .e-float-input.e-control-wrapper:active:hover:not(.e-input-group):not(.e-success):not(.e-warning):not(.e-error):not(.e-disabled) input:not([disabled]), .e-float-input:active:hover:not(.e-input-group):not(.e-success):not(.e-warning):not(.e-error):not(.e-disabled) textarea:not([disabled]), .e-float-input.e-control-wrapper:active:hover:not(.e-input-group):not(.e-success):not(.e-warning):not(.e-error):not(.e-disabled) textarea:not([disabled]) {
  border-color: #38a9ff;
}

.e-float-input:not(.e-error) input:not(:focus):-webkit-autofill ~ label.e-float-text, .e-bigger .e-float-input:not(.e-error) input:not(:focus):-webkit-autofill ~ label.e-float-text, .e-bigger.e-float-input:not(.e-error) input:not(:focus):-webkit-autofill ~ label.e-float-text, .e-small .e-float-input:not(.e-error) input:not(:focus):-webkit-autofill ~ label.e-float-text, .e-small.e-float-input:not(.e-error) input:not(:focus):-webkit-autofill ~ label.e-float-text, .e-bigger .e-float-input.e-small:not(.e-error) input:not(:focus):-webkit-autofill ~ label.e-float-text, .e-bigger.e-small .e-float-input:not(.e-error) input:not(:focus):-webkit-autofill ~ label.e-float-text, .e-float-input.e-bigger.e-small:not(.e-error) input:not(:focus):-webkit-autofill ~ label.e-float-text, .e-float-input.e-control-wrapper.e-autofill:not(.e-error) input:not(:focus):-webkit-autofill ~ label.e-float-text, .e-bigger .e-float-input.e-control-wrapper.e-autofill:not(.e-error) input:not(:focus):-webkit-autofill ~ label.e-float-text, .e-bigger.e-float-input.e-control-wrapper.e-autofill:not(.e-error) input:not(:focus):-webkit-autofill ~ label.e-float-text, .e-small .e-float-input.e-control-wrapper.e-autofill:not(.e-error) input:not(:focus):-webkit-autofill ~ label.e-float-text, .e-small.e-float-input.e-control-wrapper.e-autofill:not(.e-error) input:not(:focus):-webkit-autofill ~ label.e-float-text, .e-bigger .e-float-input.e-small.e-control-wrapper.e-autofill:not(.e-error) input:not(:focus):-webkit-autofill ~ label.e-float-text, .e-bigger.e-small .e-float-input.e-control-wrapper.e-autofill:not(.e-error) input:not(:focus):-webkit-autofill ~ label.e-float-text, .e-float-input.e-bigger.e-small.e-control-wrapper.e-autofill:not(.e-error) input:not(:focus):-webkit-autofill ~ label.e-float-text {
  color: #dadada;
}

.e-float-input:not(.e-error) textarea:not(:focus):-webkit-autofill ~ label.e-float-text, .e-bigger .e-float-input:not(.e-error) textarea:not(:focus):-webkit-autofill ~ label.e-float-text, .e-bigger.e-float-input:not(.e-error) textarea:not(:focus):-webkit-autofill ~ label.e-float-text, .e-small .e-float-input:not(.e-error) textarea:not(:focus):-webkit-autofill ~ label.e-float-text, .e-small.e-float-input:not(.e-error) textarea:not(:focus):-webkit-autofill ~ label.e-float-text, .e-bigger .e-float-input.e-small:not(.e-error) textarea:not(:focus):-webkit-autofill ~ label.e-float-text, .e-bigger.e-small .e-float-input:not(.e-error) textarea:not(:focus):-webkit-autofill ~ label.e-float-text, .e-float-input.e-bigger.e-small:not(.e-error) textarea:not(:focus):-webkit-autofill ~ label.e-float-text, .e-float-input.e-control-wrapper.e-autofill:not(.e-error) textarea:not(:focus):-webkit-autofill ~ label.e-float-text, .e-bigger .e-float-input.e-control-wrapper.e-autofill:not(.e-error) textarea:not(:focus):-webkit-autofill ~ label.e-float-text, .e-bigger.e-float-input.e-control-wrapper.e-autofill:not(.e-error) textarea:not(:focus):-webkit-autofill ~ label.e-float-text, .e-small .e-float-input.e-control-wrapper.e-autofill:not(.e-error) textarea:not(:focus):-webkit-autofill ~ label.e-float-text, .e-small.e-float-input.e-control-wrapper.e-autofill:not(.e-error) textarea:not(:focus):-webkit-autofill ~ label.e-float-text, .e-bigger .e-float-input.e-small.e-control-wrapper.e-autofill:not(.e-error) textarea:not(:focus):-webkit-autofill ~ label.e-float-text, .e-bigger.e-small .e-float-input.e-control-wrapper.e-autofill:not(.e-error) textarea:not(:focus):-webkit-autofill ~ label.e-float-text, .e-float-input.e-bigger.e-small.e-control-wrapper.e-autofill:not(.e-error) textarea:not(:focus):-webkit-autofill ~ label.e-float-text {
  color: #dadada;
}

.e-control .e-js [class^='e-'], .e-control .e-js [class*=' e-'] {
  box-sizing: content-box;
}
