@charset "UTF-8";
.e-bigger .e-ddl.e-popup .e-input-group .e-clear-icon {
  height: 36px;
}

.e-ddl.e-control.e-popup {
  border: 0;
  box-shadow: 0 0 5px 0 rgba(0, 0, 0, 0.4);
  margin-top: 1px;
}

.e-ddl.e-control.e-popup .e-content.e-nodata {
  background-color: #fff;
}

.e-ddl.e-control.e-popup .e-dropdownbase .e-list-item .e-highlight {
  color: #23726c;
}

.e-ddl.e-control.e-popup .e-input-group {
  display: -ms-flexbox;
  display: flex;
  width: auto;
}

.e-ddl.e-control.e-popup .e-input-group input {
  line-height: 15px;
}

.e-ddl.e-control.e-popup .e-input-group .e-clear-icon {
  border-radius: 20px;
  height: 20px;
  margin: 5px;
  min-width: 20px;
}

.e-ddl.e-control.e-popup .e-input-group .e-clear-icon::before {
  font-size: 10px;
}

.e-ddl.e-control.e-popup .e-filter-parent {
  border-left-width: 0;
  border-right-width: 0;
}

.e-ddl.e-control.e-popup .e-filter-parent .e-input-group.e-control-wrapper:hover:active {
  border-color: #000;
}

.e-bigger .e-ddl.e-control.e-popup.e-ddl-device .e-input-group {
  margin: 0 0 0 55px;
}

.e-bigger .e-ddl.e-control.e-popup.e-ddl-device-filter {
  margin-top: 0;
}

.e-bigger .e-ddl.e-control.e-popup .e-list-item, .e-bigger .e-ddl.e-control.e-popup .e-list-group-item, .e-bigger .e-ddl.e-control.e-popup .e-fixed-head {
  font-size: 15px;
  line-height: 45px;
  padding-left: 0;
  text-indent: 16px;
}

.e-bigger .e-ddl.e-control.e-popup .e-dd-group .e-list-item {
  padding-left: 4px;
}

.e-bigger .e-ddl.e-control.e-popup .e-input-group {
  padding: 4px 0;
}

.e-bigger .e-ddl.e-control.e-popup .e-input-group input {
  height: 30px;
}

.e-bigger.e-small .e-ddl.e-control.e-popup .e-list-item, .e-bigger.e-small .e-ddl.e-control.e-popup .e-list-group-item, .e-bigger.e-small .e-ddl.e-control.e-popup .e-fixed-head {
  font-size: 14px;
  line-height: 40px;
  padding-left: 0;
  text-indent: 16px;
}

.e-bigger.e-small .e-ddl.e-control.e-popup .e-dd-group .e-list-item {
  padding-left: 4px;
}

.e-bigger.e-small .e-ddl.e-control.e-popup .e-input-group {
  padding: 0;
}

.e-bigger.e-small .e-ddl.e-control.e-popup .e-input-group input {
  height: 34px;
}

.e-multi-select-wrapper .e-chips-close.e-close-hooker::before {
  color: #000;
  top: 12px;
}

.e-multiselect .e-multi-select-wrapper input.e-dropdownbase:-moz-placeholder {
  color: #4f4f4f;
  font-family: 'Segoe UI';
  font-size: 14px;
  font-style: italic;
}

.e-multiselect .e-multi-select-wrapper input.e-dropdownbase::-moz-placeholder {
  color: #4f4f4f;
  font-family: 'Segoe UI';
  font-size: 14px;
  font-style: italic;
}

.e-multiselect .e-multi-select-wrapper input.e-dropdownbase:-ms-input-placeholder {
  color: #4f4f4f;
  font-family: 'Segoe UI';
  font-size: 14px;
  font-style: italic;
}

.e-multiselect .e-multi-select-wrapper input.e-dropdownbase::-webkit-input-placeholder {
  color: #4f4f4f;
  font-family: 'Segoe UI';
  font-size: 14px;
  font-style: italic;
}

.e-multi-select-wrapper .e-chips-collection .e-chips:hover .e-chipcontent {
  color: #000;
}

.e-multi-select-wrapper .e-chips-collection .e-chips:hover .e-chips-close.e-icon::before {
  color: #000;
}

.e-multi-select-wrapper .e-chips-collection .e-chips .e-chips-close.e-icon::before {
  line-height: 26px;
  top: 0;
}

.e-multi-select-wrapper .e-delim-values.e-delim-view {
  color: #000;
}

.e-multi-select-wrapper.e-delimiter .e-delim-values {
  color: #000;
}

.e-multi-select-wrapper .e-searcher input[type='text'] {
  color: #000;
  height: 100%;
  min-height: 28px;
}

.e-multi-select-wrapper .e-searcher input[type='text']::selection {
  background-color: #400074;
}

.e-control.e-popup.e-multi-select-list-wrapper.e-ddl.e-checkbox .e-filter-parent .e-clear-icon, .e-bigger .e-control.e-popup.e-multi-select-list-wrapper.e-ddl.e-checkbox .e-filter-parent .e-clear-icon {
  padding-left: 0;
}

.e-control.e-popup.e-multi-select-list-wrapper.e-checkbox .e-selectall-parent {
  border-bottom: 1px solid #000;
}

.e-control.e-popup.e-multi-select-list-wrapper.e-checkbox .e-list-parent .e-list-item.e-item-focus.e-active.e-hover {
  background-color: #400074;
}

.e-control.e-popup.e-multi-select-list-wrapper.e-checkbox .e-list-parent .e-list-item.e-item-focus.e-active {
  border: 2px solid #400074;
}

.e-control.e-popup.e-multi-select-list-wrapper.e-checkbox .e-list-parent .e-list-item.e-active.e-hover:not(.e-item-focus) {
  border-bottom: 2px solid #000;
  border-left: 2px solid #000;
  border-right: 2px solid #000;
  border-top: 2px solid #000;
}

.e-control.e-popup.e-multi-select-list-wrapper .e-list-parent .e-list-item.e-item-focus {
  border: 2px solid #ecf;
}

.e-control.e-popup.e-multi-select-list-wrapper .e-list-parent .e-list-item.e-active.e-hover {
  background-color: #000;
  border-bottom: 1px solid #000;
  border-left: 1px solid #000;
  border-right: 1px solid #000;
  border-top: 1px solid #000;
}

.e-control.e-popup.e-multi-select-list-wrapper .e-list-parent .e-list-item.e-hover:not(.e-active) {
  border: 2px solid #000;
}

.e-control.e-popup.e-multi-select-list-wrapper .e-list-parent .e-list-item:not(.e-hover):not(.e-item-focus) {
  border: 2px solid #fff;
}

.e-control.e-popup.e-multi-select-list-wrapper .e-list-parent .e-list-item.e-active {
  border: 2px solid #400074;
}

.e-multi-select-wrapper.e-down-icon .e-input-group-icon.e-ddl-icon {
  height: 30px;
  width: 30px;
}

@keyframes material-spinner-rotate {
  0% {
    transform: rotate(0);
  }
  100% {
    transform: rotate(360deg);
  }
}

@keyframes fabric-spinner-rotate {
  0% {
    transform: rotate(0);
  }
  100% {
    transform: rotate(360deg);
  }
}

.e-query-builder .e-collapse-rule::before {
  content: '\e906';
}

.e-query-builder {
  border: 1px solid;
  border-radius: 0;
  height: auto;
  width: auto;
}

.e-query-builder .e-multiselect .e-qb-spinner .e-spinner-inner {
  left: auto;
  right: 5px;
}

.e-query-builder.e-rtl .e-multiselect .e-qb-spinner .e-spinner-inner {
  left: 5px;
  right: auto;
}

.e-query-builder.e-rtl.e-bigger.e-device .e-group-body .e-rule-container .e-rule-delete, .e-query-builder.e-rtl.e-bigger .e-group-body .e-rule-container.e-vertical-mode .e-rule-delete {
  padding-left: 22px;
}

.e-query-builder.e-rtl.e-device .e-group-body .e-rule-container .e-rule-value-delete, .e-query-builder.e-rtl .e-group-body .e-rule-container.e-vertical-mode .e-rule-value-delete {
  text-align: left;
}

.e-query-builder.e-rtl.e-device .e-group-body .e-rule-container .e-rule-delete, .e-query-builder.e-rtl .e-group-body .e-rule-container.e-vertical-mode .e-rule-delete {
  padding-left: 16px;
}

.e-query-builder.e-rtl .e-group-header .e-group-action .e-btn {
  margin-left: 0;
  margin-right: 12px;
}

.e-query-builder.e-rtl .e-horizontal-mode .e-rule-delete {
  margin-left: 0;
  margin-right: 12px;
}

.e-query-builder.e-rtl .e-group-body {
  padding-left: 0;
  padding-right: 20px;
}

.e-query-builder.e-rtl .e-rule-list > ::before {
  border-width: 0 2px 2px 0;
}

.e-query-builder.e-rtl .e-rule-list > .e-group-container:first-child {
  margin-top: 0;
}

.e-query-builder.e-rtl .e-rule-list > ::after, .e-query-builder.e-rtl .e-rule-list > ::before {
  right: -12px;
}

.e-query-builder.e-rtl .e-rule-list > ::after {
  border-width: 0 2px 0 0;
}

.e-query-builder.e-rtl .e-rule-list .e-group-container::before {
  right: -11px;
}

.e-query-builder.e-rtl .e-rule-list > .e-group-container {
  padding-right: 0;
}

.e-query-builder .e-group-container, .e-query-builder .e-rule-container {
  position: relative;
}

.e-query-builder .e-rule-list > :first-child::before {
  top: -12px;
}

.e-query-builder .e-rule-list > :last-child::after {
  display: none;
}

.e-query-builder .e-rule-list > ::before {
  border-width: 0 0 2px 2px;
  height: 25px;
  top: -10px;
}

.e-query-builder .e-rule-list > ::after, .e-query-builder .e-rule-list > ::before {
  border-style: dotted;
  content: '';
  left: -12px;
  position: absolute;
  width: 10px;
}

.e-query-builder .e-rule-list > ::after {
  border-width: 0 0 0 2px;
  height: calc(100% - 17px);
  top: 17px;
}

.e-query-builder .e-rule-list > .e-rule-container::before {
  height: calc(50% + 8px);
}

.e-query-builder .e-rule-list > .e-rule-container:not(:first-child)::before {
  height: calc(50% + 10px);
}

.e-query-builder .e-rule-list > .e-rule-container::after {
  height: calc(50% + 6px);
  top: calc(50% - 3px);
}

.e-query-builder .e-rule-list > .e-group-container:first-child {
  margin-top: 0;
}

.e-query-builder .e-rule-list .e-group-container::before {
  left: -11px;
}

.e-query-builder .e-rule-list .e-group-container::after {
  left: -11px;
}

.e-query-builder .e-group-header .e-group-action .e-btn {
  margin-left: 12px;
}

.e-query-builder .e-group-header .e-btn-group {
  border: none;
  box-shadow: none;
  display: inline-block;
}

.e-query-builder .e-group-header .e-group-action {
  display: inline-block;
  margin-top: 5px;
}

.e-query-builder .e-group-header .e-dropdown-btn.e-add-btn, .e-query-builder .e-group-header .e-deletegroup {
  margin-bottom: 2px;
}

.e-query-builder .e-group-header button.e-button-hide {
  display: none;
}

.e-query-builder .e-group-header.e-btn.e-small.e-round {
  box-shadow: none;
}

.e-query-builder .e-rule-list .e-group-container {
  margin-left: 0;
  margin-top: 12px;
  width: 100%;
}

.e-query-builder .e-rule-list {
  padding: 12px 0 12px 0;
  padding-bottom: 0;
}

.e-query-builder .e-group-container {
  padding: 12px;
}

.e-query-builder .e-rule-container {
  border: 1px solid;
  border-radius: 0;
}

.e-query-builder .e-rule-list > .e-group-container {
  padding: 0;
  padding-bottom: 12px;
}

.e-query-builder .e-group-container .e-rule-list > .e-group-container {
  padding-bottom: 0;
}

.e-query-builder .e-group-body {
  padding-left: 20px;
}

.e-query-builder .e-group-body .e-rule-container {
  box-shadow: none;
  height: auto;
  margin-top: -1px;
  padding-right: 12px;
}

.e-query-builder .e-group-body .e-rule-container.e-prev-joined-rule {
  border-bottom: 0;
  border-bottom-left-radius: 0;
  border-bottom-right-radius: 0;
}

.e-query-builder .e-group-body .e-rule-container.e-joined-rule {
  border-top-left-radius: 0;
  border-top-right-radius: 0;
  border-top-style: dotted;
  border-top-width: 2px;
  margin-top: 0;
}

.e-query-builder .e-group-body .e-rule-container.e-separate-rule {
  margin-top: 12px;
}

.e-query-builder .e-group-body .e-rule-container .e-rule-filter {
  padding: 12px 0 12px 12px;
  width: auto;
}

.e-query-builder .e-group-body .e-rule-container .e-rule-value .e-control-wrapper.e-numeric:not(:first-child), .e-query-builder .e-group-body .e-rule-container .e-rule-value .e-control-wrapper.e-date-wrapper:not(:first-child), .e-query-builder .e-group-body .e-rule-container .e-rule-value .e-control-wrapper.e-input-group:not(:first-child) {
  float: right;
  margin-top: 12px;
}

.e-query-builder .e-group-body .e-rule-container .e-rule-value .e-multi-select-wrapper {
  min-width: 190px;
}

.e-query-builder .e-group-body .e-rule-container .e-rule-value .e-multiselect, .e-query-builder .e-group-body .e-rule-container .e-rule-value .e-multi-select-wrapper {
  max-width: 200px;
}

.e-query-builder .e-group-body .e-rule-container .e-operator, .e-query-builder .e-group-body .e-rule-container .e-value {
  padding: 12px 0 12px 12px;
}

.e-query-builder .e-group-body .e-rule-container .e-operator .e-radio-wrapper, .e-query-builder .e-group-body .e-rule-container .e-value .e-radio-wrapper {
  margin-right: 15px;
}

.e-query-builder .e-group-body .e-horizontal-mode .e-rule-delete {
  display: inline-block;
  margin-left: 12px;
}

.e-query-builder .e-group-body .e-rule-container button.e-button-hide {
  display: none;
}

.e-query-builder .e-group-body .e-horizontal-mode .e-rule-filter, .e-query-builder .e-group-body .e-horizontal-mode .e-rule-operator, .e-query-builder .e-group-body .e-horizontal-mode .e-rule-value, .e-query-builder .e-group-body .e-horizontal-mode .e-rule-value-delete {
  display: inline-block;
}

.e-query-builder .e-group-body .e-rule-value.e-hide {
  display: none;
}

.e-query-builder .e-group-body .e-rule-value.e-show {
  display: inline-block;
}

.e-query-builder .e-group-body .e-rule-container.e-vertical-mode .e-rule-delete {
  margin-bottom: 12px;
  padding-right: none;
  right: 0;
}

.e-query-builder .e-group-body .e-rule-field.e-btn.e-small.e-round, .e-query-builder .e-group-body .e-rule-delete.e-btn.e-small.e-round {
  box-shadow: none;
}

.e-query-builder .e-group-body .e-vertical-mode .e-removerule.e-rule-delete {
  box-shadow: none;
  right: 0;
}

.e-query-builder .e-dropdown-btn {
  box-shadow: none;
}

.e-query-builder.e-device .e-group-body .e-rule-container .e-rule-filter {
  padding: 15px;
}

.e-query-builder.e-device .e-removerule.e-rule-delete {
  box-shadow: none;
}

.e-query-builder.e-device .e-group-body .e-rule-container .e-rule-value .e-multiselect, .e-query-builder.e-device .e-group-body .e-rule-container .e-rule-value .e-multi-select-wrapper {
  max-width: 100;
}

.e-query-builder.e-device .e-group-body .e-rule-container .e-rule-value-delete, .e-query-builder .e-group-body .e-rule-container.e-vertical-mode .e-rule-value-delete {
  text-align: right;
}

.e-query-builder .e-delete-icon::before {
  content: "";
}

.e-query-builder .e-edit-rule.e-btn.e-small {
  box-shadow: none;
}

.e-query-builder .e-edit-rule {
  right: 0;
}

.e-query-builder .e-collapse-rule {
  border: 1px solid;
  border-right: 0;
  border-top: 0;
  box-shadow: -1px 1px 4px 0 rgba(0, 0, 0, 0.12);
  font-size: 20px;
  padding: 5px;
  position: absolute;
  right: 0;
  top: 0;
}

.e-query-builder .e-summary-text {
  border-style: none;
  font-family: "Segoe UI", "GeezaPro", "DejaVu Serif", sans-serif, "-apple-system", "BlinkMacSystemFont";
  font-size: 14px;
  line-height: 1.5em;
  padding: 12px;
  resize: none;
  width: 100%;
}

.e-query-builder .e-summary-btndiv {
  padding: 12px;
  text-align: right;
}

.e-query-builder .e-rule-list .e-rule-container.e-vertical-mode .e-rule-filter, .e-query-builder .e-rule-list .e-rule-container.e-vertical-mode .e-operator, .e-query-builder .e-rule-list .e-rule-container.e-vertical-mode .e-value, .e-query-builder .e-rule-list .e-rule-container.e-vertical-mode .e-rule-value-delete, .e-query-builder.e-device .e-rule-list .e-rule-container .e-rule-filter, .e-query-builder.e-device .e-rule-list .e-rule-container .e-operator, .e-query-builder.e-device .e-rule-list .e-rule-container .e-value, .e-query-builder.e-device .e-rule-list .e-rule-container .e-rule-value-delete {
  padding: 15px 0 0 15px;
}

.e-tooltip-wrap.e-querybuilder-error .e-arrow-tip-inner.e-tip-top, .e-tooltip-wrap.e-querybuilder-error .e-arrow-tip-outer.e-tip-top {
  border-bottom: 8px solid;
}

.e-tooltip-wrap.e-querybuilder-error .e-arrow-tip-outer.e-tip-bottom, .e-tooltip-wrap.e-querybuilder-error .e-arrow-tip-inner.e-tip-bottom {
  border-top: 8px solid;
}

.e-dropdown-popup .e-addgroup {
  float: right;
}

.e-dropdown-popup ul .e-item.e-button-hide {
  display: none;
}

.e-addrule.e-add-icon::before, .e-addgroup.e-add-icon::before, .e-query-builder .e-add-icon::before {
  content: "";
}

.e-query-builder.e-device .e-group-container, .e-bigger.e-query-builder .e-group-container, .e-bigger .e-query-builder .e-group-container {
  padding: 15px;
}

.e-query-builder.e-device .e-rule-list > .e-group-container, .e-bigger.e-query-builder .e-rule-list > .e-group-container, .e-bigger .e-query-builder .e-rule-list > .e-group-container {
  padding: 0;
}

.e-query-builder.e-device .e-rule-list > .e-rule-container:not(:first-child)::before, .e-bigger.e-query-builder .e-rule-list > .e-rule-container:not(:first-child)::before, .e-bigger .e-query-builder .e-rule-list > .e-rule-container:not(:first-child)::before {
  height: calc(50% + 12px);
}

.e-query-builder.e-device .e-rule-list > .e-group-container:first-child, .e-bigger.e-query-builder .e-rule-list > .e-group-container:first-child, .e-bigger .e-query-builder .e-rule-list > .e-group-container:first-child {
  margin-top: 0;
}

.e-query-builder.e-device .e-rule-list > ::before, .e-bigger.e-query-builder .e-rule-list > ::before, .e-bigger .e-query-builder .e-rule-list > ::before {
  top: -12px;
}

.e-query-builder.e-device .e-group-action .e-btn, .e-bigger.e-query-builder .e-group-action .e-btn, .e-bigger .e-query-builder .e-group-action .e-btn {
  margin-left: 15px;
}

.e-query-builder.e-device .e-rule-list > :first-child::before, .e-bigger.e-query-builder .e-rule-list > :first-child::before, .e-bigger .e-query-builder .e-rule-list > :first-child::before {
  top: -14px;
}

.e-query-builder.e-device .e-rule-list, .e-bigger.e-query-builder .e-rule-list, .e-bigger .e-query-builder .e-rule-list {
  padding: 15px 0 15px 0;
  padding-bottom: 0;
}

.e-query-builder.e-device .e-rule-list .e-group-container, .e-bigger.e-query-builder .e-rule-list .e-group-container, .e-bigger .e-query-builder .e-rule-list .e-group-container {
  margin-top: 15px;
}

.e-query-builder.e-device .e-group-body, .e-bigger.e-query-builder .e-group-body, .e-bigger .e-query-builder .e-group-body {
  padding-left: 24px;
}

.e-query-builder.e-device .e-group-body .e-rule-container, .e-bigger.e-query-builder .e-group-body .e-rule-container, .e-bigger .e-query-builder .e-group-body .e-rule-container {
  margin-top: -1px;
  padding-right: 15px;
}

.e-query-builder.e-device .e-group-body .e-rule-container.e-vertical-mode, .e-bigger.e-query-builder .e-group-body .e-rule-container.e-vertical-mode, .e-bigger .e-query-builder .e-group-body .e-rule-container.e-vertical-mode {
  width: auto;
}

.e-query-builder.e-device .e-group-body .e-rule-container.e-separate-rule, .e-bigger.e-query-builder .e-group-body .e-rule-container.e-separate-rule, .e-bigger .e-query-builder .e-group-body .e-rule-container.e-separate-rule {
  margin-top: 15px;
}

.e-query-builder.e-device .e-group-body .e-rule-delete, .e-query-builder.e-device .e-group-body .e-rule-container.e-vertical-mode .e-rule-delete, .e-bigger.e-query-builder .e-group-body .e-rule-delete, .e-bigger.e-query-builder .e-group-body .e-rule-container.e-vertical-mode .e-rule-delete, .e-bigger .e-query-builder .e-group-body .e-rule-delete, .e-bigger .e-query-builder .e-group-body .e-rule-container.e-vertical-mode .e-rule-delete {
  margin-bottom: 15px;
  padding-right: none;
  right: 0;
}

.e-query-builder.e-device .e-group-body .e-rule-container.e-horizontal-mode .e-rule-delete, .e-bigger.e-query-builder .e-group-body .e-rule-container.e-horizontal-mode .e-rule-delete, .e-bigger .e-query-builder .e-group-body .e-rule-container.e-horizontal-mode .e-rule-delete {
  margin-bottom: 0;
}

.e-query-builder.e-device .e-group-body .e-horizontal-mode .e-rule-delete, .e-bigger.e-query-builder .e-group-body .e-horizontal-mode .e-rule-delete, .e-bigger .e-query-builder .e-group-body .e-horizontal-mode .e-rule-delete {
  display: inline-block;
  margin-left: 7.5px;
}

.e-query-builder.e-device .e-group-body .e-horizontal-mode .e-rule-filter, .e-query-builder.e-device .e-group-body .e-horizontal-mode .e-rule-operator, .e-query-builder.e-device .e-group-body .e-horizontal-mode .e-rule-value, .e-query-builder.e-device .e-group-body .e-horizontal-mode .e-rule-value-delete, .e-bigger.e-query-builder .e-group-body .e-horizontal-mode .e-rule-filter, .e-bigger.e-query-builder .e-group-body .e-horizontal-mode .e-rule-operator, .e-bigger.e-query-builder .e-group-body .e-horizontal-mode .e-rule-value, .e-bigger.e-query-builder .e-group-body .e-horizontal-mode .e-rule-value-delete, .e-bigger .e-query-builder .e-group-body .e-horizontal-mode .e-rule-filter, .e-bigger .e-query-builder .e-group-body .e-horizontal-mode .e-rule-operator, .e-bigger .e-query-builder .e-group-body .e-horizontal-mode .e-rule-value, .e-bigger .e-query-builder .e-group-body .e-horizontal-mode .e-rule-value-delete {
  display: inline-block;
}

.e-query-builder.e-device .e-group-body .e-rule-field.e-btn.e-small.e-round, .e-query-builder.e-device .e-group-body .e-rule-delete.e-btn.e-small.e-round, .e-bigger.e-query-builder .e-group-body .e-rule-field.e-btn.e-small.e-round, .e-bigger.e-query-builder .e-group-body .e-rule-delete.e-btn.e-small.e-round, .e-bigger .e-query-builder .e-group-body .e-rule-field.e-btn.e-small.e-round, .e-bigger .e-query-builder .e-group-body .e-rule-delete.e-btn.e-small.e-round {
  box-shadow: none;
}

.e-query-builder.e-device .e-summary-text, .e-bigger.e-query-builder .e-summary-text, .e-bigger .e-query-builder .e-summary-text {
  font-size: 16px;
  line-height: 1.5em;
  padding: 15px;
}

.e-query-builder.e-device .e-summary-btndiv, .e-bigger.e-query-builder .e-summary-btndiv, .e-bigger .e-query-builder .e-summary-btndiv {
  padding: 15px;
  text-align: right;
}

.e-control.e-device .e-rule-value {
  width: 100%;
}

.e-control.e-bigger .e-group-body .e-rule-container .e-rule-filter,
.e-control.e-bigger .e-group-body .e-rule-container .e-operator,
.e-control.e-bigger .e-group-body .e-rule-container .e-value {
  padding: 15px 0 15px 15px;
}

.e-query-builder {
  background-color: #fff;
  border-color: #400074;
}

.e-query-builder .e-group-header .e-btn-group {
  border-color: #400074;
}

.e-query-builder .e-group-container {
  border-color: #4f4f4f;
}

.e-query-builder .e-rule-container {
  background-color: #fff;
  border-color: #4f4f4f;
}

.e-query-builder .e-rule-container.e-joined-rule {
  border-top-color: #ccc;
}

.e-query-builder .e-rule-list .e-group-container {
  background-color: #fff;
}

.e-query-builder .e-rule-list > ::after, .e-query-builder .e-rule-list > ::before {
  border-color: #ccc;
}

.e-query-builder .e-btn-group input:checked + label.e-btn {
  background: #400074;
  border-color: #400074;
  color: #fff;
}

.e-query-builder .e-removerule.e-btn.e-round {
  background-color: #fff;
}

.e-query-builder .e-summary-content textarea {
  background-color: #fff;
  color: #000;
}

.e-query-builder .e-collapse-rule {
  background-color: #fff;
  border-color: #757575;
  color: #000;
}

.e-query-builder .e-collapse-rule:focus, .e-query-builder .e-collapse-rule:active, .e-query-builder .e-collapse-rule:hover {
  background-color: #400074;
}

.e-tooltip-wrap.e-querybuilder-error, .e-control.e-tooltip-wrap.e-popup.e-querybuilder-error {
  background-color: #b30900;
  border-color: #b30900;
}

.e-tooltip-wrap.e-querybuilder-error .e-arrow-tip-inner.e-tip-top, .e-tooltip-wrap.e-querybuilder-error .e-arrow-tip-outer.e-tip-top {
  border-bottom-color: #b30900;
  color: #b30900;
}

.e-tooltip-wrap.e-querybuilder-error .e-arrow-tip-outer.e-tip-bottom, .e-tooltip-wrap.e-querybuilder-error .e-arrow-tip-inner.e-tip-bottom {
  border-top-color: #b30900;
  color: #b30900;
}

.e-tooltip-wrap.e-querybuilder-error .e-tip-content, .e-tooltip-wrap.e-querybuilder-error .e-tip-content label {
  color: #000;
}

.e-control .e-js [class^='e-'], .e-control .e-js [class*=' e-'] {
  box-sizing: content-box;
}
