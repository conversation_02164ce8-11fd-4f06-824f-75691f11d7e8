/*! maskedtextbox layout */
.e-widget.e-control-wrapper.e-mask .e-control.e-maskedtextbox {
  font-family: <PERSON><PERSON>, "Segoe UI", "GeezaPro", "DejaVu Serif", sans-serif, "-apple-system", "BlinkMacSystemFont";
  font-size: 13px;
}

.e-bigger .e-widget.e-control-wrapper.e-mask .e-control.e-maskedtextbox {
  font-size: 14px;
}

.e-widget.e-control-wrapper.e-mask.e-bigger .e-control.e-maskedtextbox {
  font-size: 14px;
}

.e-content-placeholder.e-mask.e-placeholder-mask {
  background-size: 300px 33px;
  min-height: 33px;
}

.e-bigger .e-content-placeholder.e-mask.e-placeholder-mask, .e-bigger.e-content-placeholder.e-mask.e-placeholder-mask {
  background-size: 300px 40px;
  min-height: 40px;
}

/*! maskedtextbox theme */
.e-widget.e-control-wrapper.e-mask.e-error .e-control.e-maskedtextbox {
  color: #a80000;
}

.e-utility-mask.e-error {
  color: #a80000;
}

.e-control .e-js [class^='e-'], .e-control .e-js [class*=' e-'] {
  box-sizing: content-box;
}
