.e-input-group-icon.e-spin-up::before {
  content: '\e85e';
  font-family: 'e-icons';
}

.e-input-group-icon.e-spin-down::before {
  content: '\e84f';
  font-family: 'e-icons';
}

.e-numeric-container {
  width: 100%;
}

.e-content-placeholder.e-numeric.e-placeholder-numeric {
  background-size: 300px 33px;
  min-height: 33px;
}

.e-bigger.e-content-placeholder.e-numeric.e-placeholder-numeric, .e-bigger .e-content-placeholder.e-numeric.e-placeholder-numeric {
  background-size: 300px 40px;
  min-height: 40px;
}

.e-numeric.e-control-wrapper.e-input-group .e-input-group-icon {
  font-size: 14px;
}

.e-bigger .e-control-wrapper.e-numeric.e-input-group .e-input-group-icon, .e-bigger.e-control-wrapper.e-numeric.e-input-group .e-input-group-icon {
  font-size: 12px;
}

.e-control .e-js [class^='e-'], .e-control .e-js [class*=' e-'] {
  box-sizing: content-box;
}
