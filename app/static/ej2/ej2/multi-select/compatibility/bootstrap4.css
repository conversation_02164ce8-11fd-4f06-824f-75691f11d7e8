.e-dropdownbase .e-list-item .e-list-icon {
  padding: 0 8px 0 0;
}

.e-bigger .e-content.e-dropdownbase {
  padding: 8px 0;
}

.e-content.e-dropdownbase {
  padding: 6px 0;
}

.e-bigger .e-dropdownbase .e-list-item .e-list-icon {
  font-size: 16px;
}

.e-bigger .e-dropdownbase .e-list-item .e-list-icon, .e-bigger.e-small .e-dropdownbase .e-list-item .e-list-icon {
  padding: 0 12px 0 0;
}

.e-small .e-dropdownbase .e-list-item .e-list-icon {
  padding: 0 8px 0 0;
}

.e-input-group.e-ddl .e-spinner-pane .e-spinner-inner .e-spin-bootstrap4 .e-path-circle {
  stroke-width: 1px;
}

.e-multi-select-wrapper .e-chips-collection .e-chips .e-chips-close.e-icon::before {
  line-height: 30px;
  top: 0;
}

.e-multiselect .e-input-group-icon.e-ddl-icon {
  border-radius: 0 4px 4px 0;
  border-right-width: 0;
}

.e-multiselect.e-rtl .e-input-group-icon.e-ddl-icon {
  border-left-width: 0;
  border-radius: 4px 0 0 4px;
  border-right-width: 1px;
}

.e-bigger .e-multi-select-wrapper .e-chips > .e-chipcontent {
  font-size: 14px;
}

.e-bigger .e-multi-select-wrapper .e-chips-close {
  height: 30px;
  width: 30px;
}

.e-popup.e-multi-select-list-wrapper .e-list-item.e-active.e-item-focus.e-hover {
  box-shadow: none;
}

.e-bigger .e-ddl.e-popup .e-filter-parent .e-clear-icon {
  display: -ms-flexbox;
  display: flex;
}

.e-input-group.e-multiselect .e-spinner-pane .e-spinner-inner .e-spin-bootstrap4 .e-path-circle {
  stroke-width: 1px;
}

.e-small .e-multi-select-wrapper .e-chips {
  margin: 1px 4px 1px 0;
}

.e-small.e-bigger .e-multi-select-wrapper .e-chips {
  margin: 2px 4px 2px 0;
}

.e-multiselect.e-input-group .e-ddl-icon::before {
  content: '\e744';
  font-family: 'e-icons';
  font-size: 8px;
}

.e-bigger .e-multiselect.e-input-group .e-ddl-icon::before {
  font-size: 10px;
}

.e-multi-select-wrapper .e-chips .e-chips-close::before {
  content: '\e745';
  cursor: pointer;
  left: 10px;
  position: relative;
  top: 8px;
}

.e-multi-select-wrapper .e-close-hooker::before {
  content: '\e745';
  cursor: pointer;
  left: 15px;
  position: relative;
  top: 17px;
}

.e-bigger .e-multi-select-wrapper .e-chips .e-chips-close::before {
  font-size: 12px;
  top: 9px;
}

.e-multiselect.e-input-group .e-ddl-disable-icon::before {
  content: '';
}

.e-multi-select-wrapper {
  box-sizing: border-box;
  cursor: text;
  line-height: normal;
  min-height: 29px;
  padding: 0 32px 0 2px;
  position: relative;
  -webkit-user-select: none;
     -moz-user-select: none;
      -ms-user-select: none;
          user-select: none;
  width: 100%;
}

.e-multi-select-wrapper.e-delimiter .e-searcher {
  height: 27px;
  vertical-align: middle;
}

.e-multi-select-wrapper.e-delimiter .e-searcher .e-dropdownbase {
  height: 100%;
  min-height: 100%;
}

.e-multi-select-wrapper .e-delim-view {
  white-space: nowrap;
}

.e-multi-select-wrapper .e-delim-view.e-delim-values.e-delim-overflow, .e-multi-select-wrapper .e-delim-view.e-delim-values.e-delim-total {
  box-sizing: border-box;
  display: inline-block;
  overflow: hidden;
  text-overflow: ellipsis;
}

.e-multi-select-wrapper .e-chips.e-mob-chip.e-chip-selected .e-chips-close::before {
  color: #fff;
  font-size: 8px;
  left: 17px;
  top: 8px;
}

.e-multi-select-wrapper .e-chips.e-mob-chip.e-chip-selected .e-chipcontent {
  background-color: #6c757d;
  color: #fff;
  padding: 13px 0 13px 0;
}

.e-multi-select-wrapper .e-searcher.e-zero-size:not(.e-multiselect-box) {
  width: 0;
}

.e-multi-select-wrapper .e-searcher.e-zero-size:not(.e-multiselect-box) input[type='text'] {
  height: 1px;
  min-height: 1px;
}

.e-multi-select-wrapper .e-chips.e-mob-chip > .e-chipcontent {
  max-width: 100%;
}

.e-multiselect.e-input-group, .e-multiselect.e-float-input {
  word-wrap: initial;
}

.e-bigger .e-multi-select-wrapper {
  min-height: 36px;
}

.e-multi-select-wrapper.e-close-icon-hide {
  padding-right: 0;
}

.e-multi-select-wrapper .e-chips-collection {
  cursor: default;
  display: block;
}

.e-multi-select-wrapper .e-multi-hidden {
  border: 0;
  height: 0;
  position: absolute;
  visibility: hidden;
  width: 0;
}

.e-multi-select-wrapper .e-chips {
  -ms-flex-align: center;
      align-items: center;
  display: -ms-inline-flexbox;
  display: inline-flex;
  float: left;
  margin: 2px 4px 2px 0;
  max-width: 100%;
  overflow: hidden;
  padding: 0;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.e-bigger .e-multi-select-wrapper .e-chips {
  margin: 4px 4px 4px 0;
}

.e-multi-select-wrapper .e-chips > .e-chipcontent {
  max-width: 100%;
  overflow: hidden;
  padding: 6px 8px 6px 8px;
  text-indent: 0;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.e-multi-select-wrapper.e-delimiter .e-searcher {
  display: inline-block;
  float: none;
}

.e-multi-select-wrapper .e-mob-chip.e-chips > .e-chipcontent, .e-bigger .e-multi-select-wrapper .e-chips > .e-chipcontent {
  padding: 7px 12px 7px 12px;
}

.e-bigger .e-multi-select-wrapper .e-chips > .e-chipcontent {
  padding: 7px 8px 7px 12px;
}

.e-control.e-multiselect.e-rtl .e-multi-select-wrapper .e-mob-chip.e-chips > .e-chipcontent, .e-bigger .e-control.e-multiselect.e-rtl .e-multi-select-wrapper .e-chips > .e-chipcontent {
  padding: 7px 12px 7px 12px;
}

.e-multi-select-wrapper .e-chips-close {
  -ms-flex-item-align: center;
      align-self: center;
  display: -ms-flexbox;
  display: flex;
  float: right;
  font-family: 'e-icons';
  height: 24px;
  margin: 0 0 0;
  width: 24px;
}

.e-multi-select-wrapper .e-mob-chip.e-chips .e-chips-close {
  margin: 1px 0 0;
}

.e-multi-select-wrapper .e-chips-close.e-close-hooker {
  cursor: default;
  font-size: 10px;
  height: 40px;
  margin-top: -3.8em;
  position: absolute;
  right: 0;
  top: 100%;
  width: 40px;
}

.e-multiselect .e-down-icon .e-chips-close.e-close-hooker {
  right: 38px;
}

.e-bigger .e-multi-select-wrapper .e-chips-close.e-close-hooker {
  font-size: 12px;
  margin-top: -3.5em;
}

.e-multi-select-wrapper input[type='text'] {
  background: none;
  border: 0;
  font-family: "Helvetica Neue", "Helvetica", "Arial", sans-serif, "-apple-system", "BlinkMacSystemFont";
  font-size: 14px;
  font-weight: 400;
  height: 29px;
  min-height: 29px;
  outline: none;
  padding: 0;
  text-indent: 6px;
}

.e-bigger .e-multi-select-wrapper input[type='text'], .e-multi-select-wrapper.e-mob-wrapper input[type='text'] {
  height: 29px;
  min-height: 29px;
}

.e-bigger .e-multi-select-wrapper input[type='text'] {
  font-size: 16px;
  height: 36px;
  min-height: 36px;
}

.e-multi-select-wrapper input[type='text']::-ms-clear {
  display: none;
}

.e-multi-select-wrapper .e-searcher {
  display: block;
  float: left;
  width: auto;
}

.e-multi-select-wrapper .e-delim-values {
  font-family: "Helvetica Neue", "Helvetica", "Arial", sans-serif, "-apple-system", "BlinkMacSystemFont";
  font-size: 14px;
  line-height: 29px;
  max-width: 100%;
  padding-left: 6px;
  padding-right: 6px;
  vertical-align: middle;
}

.e-bigger .e-multi-select-wrapper .e-delim-values {
  line-height: 36px;
}

.e-bigger .e-multi-select-wrapper .e-delim-values .e-remain {
  padding-left: 12px;
}

.e-multi-select-list-wrapper .e-hide-listitem {
  display: none;
}

.e-multi-select-wrapper .e-delim-values .e-remain {
  color: #6c757d;
  cursor: pointer;
  display: inline-block;
  font-size: 14px;
  padding-left: 8px;
}

.e-control.e-multiselect.e-disabled .e-multi-select-wrapper, .e-control.e-multiselect.e-disabled .e-multi-select-wrapper .e-chips .e-chips-close::before {
  cursor: not-allowed;
}

.e-multi-select-wrapper .e-chips.e-mob-chip.e-chip-selected .e-chips-close {
  height: 46px;
  left: 0;
  margin: 0 0 0 0;
  margin-left: auto;
  position: relative;
  top: 0;
  width: 46px;
}

.e-multi-select-wrapper .e-chips.e-mob-chip.e-chip-selected {
  -ms-flex-item-align: center;
      align-self: center;
  box-shadow: none;
  display: -ms-inline-flexbox;
  display: inline-flex;
  padding: 0 0 0 20px;
  width: 92%;
}

.e-multi-select-wrapper .e-ddl-disable-icon::before {
  content: '';
}

.e-control.e-multiselect.e-rtl .e-multi-select-wrapper.e-delimiter .e-searcher {
  float: none;
}

.e-control.e-multiselect.e-rtl .e-multi-select-wrapper.e-close-icon-hide {
  padding-left: 0;
}

.e-control.e-multiselect.e-rtl .e-multi-select-wrapper {
  padding: 0 2px 0 32px;
}

.e-control.e-multiselect.e-rtl .e-multi-select-wrapper .e-chips.e-mob-chip.e-chip-selected .e-chips-close::before {
  left: -10px;
}

.e-control.e-multiselect.e-rtl .e-multi-select-wrapper .e-chips.e-mob-chip.e-chip-selected .e-chips-close {
  margin: 0;
}

.e-control.e-multiselect.e-rtl .e-multi-select-wrapper .e-searcher {
  float: right;
}

.e-control.e-multiselect.e-rtl .e-multi-select-wrapper .e-chips {
  float: right;
  margin: 2px 0 2px 4px;
  padding: 0 8px 0 0;
}

.e-control.e-multiselect.e-rtl .e-multi-select-wrapper .e-chips .e-chipcontent {
  padding: 6px 10px 6px 10px;
}

.e-control.e-multiselect.e-rtl .e-multi-select-wrapper .e-chips .e-chips-close {
  float: left;
}

.e-control.e-multiselect.e-rtl .e-multi-select-wrapper .e-chips .e-chips-close::before {
  left: -10px;
}

.e-control.e-multiselect.e-rtl .e-multi-select-wrapper .e-chips.e-mob-chip {
  padding: 0 4px 0 8px;
}

.e-control.e-multiselect.e-rtl .e-multi-select-wrapper .e-chips.e-mob-chip .e-chipcontent {
  padding: 8px 4px;
}

.e-control.e-multiselect.e-rtl .e-multi-select-wrapper .e-chips.e-mob-chip.e-chip-selected {
  padding: 0 8px 0 4px;
}

.e-control.e-multiselect.e-rtl .e-multi-select-wrapper .e-chips.e-mob-chip.e-chip-selected .e-chipcontent {
  padding: 12px 4px;
}

.e-control.e-multiselect.e-rtl .e-multi-select-wrapper .e-chips-close.e-close-hooker {
  left: 38px;
  position: absolute;
  right: auto;
}

.e-control.e-multiselect.e-rtl .e-multi-select-wrapper .e-close-hooker::before {
  left: -15px;
}

.e-control.e-multiselect.e-rtl .e-multi-select-wrapper .e-delim-values .e-remain {
  padding-right: 8px;
}

.e-multiselect.e-rtl .e-down-icon .e-chips-close.e-icon.e-close-hooker {
  left: 38px;
}

.e-multiselect.e-rtl .e-multi-select-wrapper.e-down-icon .e-close-hooker::before {
  left: -15px;
}

.e-popup.e-multi-select-list-wrapper .e-list-item .e-checkbox-wrapper {
  bottom: 1px;
  margin-right: 8px;
  position: relative;
  text-indent: 0;
  vertical-align: middle;
}

.e-popup.e-multi-select-list-wrapper.e-multiselect-group:not(.e-rtl) .e-list-item .e-checkbox-wrapper {
  padding-left: 14px;
}

.e-popup.e-multi-select-list-wrapper.e-multiselect-group.e-rtl .e-list-item .e-checkbox-wrapper {
  padding-right: 29px;
}

.e-bigger .e-popup.e-multi-select-list-wrapper.e-multiselect-group.e-rtl .e-list-item .e-checkbox-wrapper {
  padding-right: 5px;
}

.e-popup.e-multi-select-list-wrapper.e-multiselect-group .e-list-group-item .e-checkbox-wrapper {
  bottom: 1px;
  margin-right: 8px;
  position: relative;
  text-indent: 0;
  vertical-align: middle;
}

.e-bigger .e-popup.e-multi-select-list-wrapper .e-list-item .e-checkbox-wrapper {
  bottom: 1px;
  margin-right: 12px;
}

.e-bigger .e-popup.e-multi-select-list-wrapper.e-mulltiselect-group .e-list-group-item .e-checkbox-wrapper {
  bottom: 1px;
  margin-right: 12px;
}

.e-popup.e-multi-select-list-wrapper.e-rtl .e-list-item .e-checkbox-wrapper {
  margin-left: 12px;
  margin-right: 0;
}

.e-popup.e-multi-select-list-wrapper.e-multiselect-group.e-rtl .e-list-group-item .e-checkbox-wrapper {
  margin-left: 12px;
  margin-right: 0;
}

.e-popup.e-multi-select-list-wrapper.e-rtl .e-list-item {
  padding-right: 0;
}

.e-popup.e-multi-select-list-wrapper.e-rtl .e-dropdownbase.e-rtl.e-dd-group .e-list-item {
  padding-right: 15px;
}

.e-bigger .e-popup.e-multi-select-list-wrapper.e-multiselect-group .e-dropdownbase.e-dd-group .e-list-group-item {
  padding-left: 24px;
}

.e-bigger .e-popup.e-multi-select-list-wrapper.e-multiselect-group:not(.e-rtl) .e-dropdownbase.e-dd-group .e-list-item .e-checkbox-wrapper {
  padding-left: 20px;
}

.e-multi-select-list-wrapper .e-selectall-parent {
  cursor: pointer;
  display: block;
  line-height: 26px;
  overflow: hidden;
  position: relative;
  text-indent: 21px;
  white-space: nowrap;
  width: 100%;
}

.e-multi-select-list-wrapper .e-selectall-parent .e-all-text {
  color: #212529;
  font-family: "Helvetica Neue", "Helvetica", "Arial", sans-serif, "-apple-system", "BlinkMacSystemFont";
  font-size: 14px;
}

.e-bigger .e-multi-select-list-wrapper .e-selectall-parent {
  font-size: 14px;
  line-height: 40px;
  text-indent: 25px;
}

.e-multi-select-list-wrapper .e-selectall-parent .e-checkbox-wrapper {
  bottom: 1px;
  margin-right: 8px;
  position: relative;
  text-indent: 0;
  vertical-align: middle;
}

.e-bigger .e-multi-select-list-wrapper .e-selectall-parent .e-checkbox-wrapper {
  bottom: 1px;
  margin-right: 12px;
}

.e-multi-select-list-wrapper.e-rtl .e-selectall-parent .e-checkbox-wrapper {
  margin-left: 12px;
  margin-right: 0;
}

.e-multiselect .e-input-group-icon.e-ddl-icon {
  float: right;
  margin-top: 0;
}

.e-multiselect.e-rtl .e-input-group-icon.e-ddl-icon {
  float: left;
}

.e-multiselect.e-checkbox .e-multi-select-wrapper, .e-multiselect .e-multi-select-wrapper.e-down-icon {
  padding: 0;
}

.e-ddl.e-popup.e-multi-select-list-wrapper .e-filter-parent .e-input-filter, .e-ddl.e-popup.e-multi-select-list-wrapper .e-filter-parent .e-input-group.e-input-focus .e-input-filter {
  padding: 8px;
}

.e-bigger .e-ddl.e-popup.e-multi-select-list-wrappe .e-filter-parent .e-input-filter, .e-bigger .e-ddl.e-popup.e-multi-select-list-wrapper .e-filter-parent .e-input-group.e-input-focus {
  padding: 4px 0;
}

.e-ddl.e-popup.e-multi-select-list-wrapper .e-filter-parent .e-clear-icon, .e-bigger .e-ddl.e-popup.e-multi-select-list-wrapper .e-filter-parent .e-clear-icon {
  padding-left: 8px;
}

.e-ddl.e-popup.e-multi-select-list-wrapper .e-filter-parent .e-back-icon {
  margin: -2px 10px 0 -54px;
  padding: 0;
  position: absolute;
}

.e-bigger .e-checkbox .e-multi-select-wrapper .e-delim-values {
  line-height: 39px;
}

.e-checkbox .e-multi-select-wrapper .e-delim-values .e-remain {
  line-height: 20px;
  padding-left: 10px;
}

.e-popup.e-multi-select-list-wrapper .e-list-item.e-disable .e-checkbox-wrapper .e-frame {
  opacity: .3;
}

.e-popup.e-multi-select-list-wrapper .e-list-item.e-disable {
  opacity: .7;
}

.e-multi-select-wrapper input[readonly='true'] {
  pointer-events: none;
}

.e-multiselect.e-checkbox .e-multi-select-wrapper .e-searcher {
  pointer-events: none;
}

ejs-multiselect {
  display: block;
}

.e-small .e-multi-select-list-wrapper .e-selectall-parent {
  line-height: 22px;
}

.e-small .e-multi-select-wrapper .e-chips-close {
  height: 18px;
  width: 18px;
}

.e-small .e-multi-select-wrapper {
  min-height: 24px;
}

.e-small .e-multi-select-wrapper input[type='text'] {
  height: 24px;
  min-height: 24px;
}

.e-small .e-multi-select-wrapper .e-delim-values {
  font-size: 12px;
  line-height: 24px;
}

.e-small .e-multi-select-wrapper .e-chips-close.e-close-hooker {
  margin-top: -3.4em;
}

.e-small .e-multi-select-wrapper .e-chips > .e-chipcontent {
  padding: 0 8px 0 8px;
}

.e-bigger.e-small .e-multi-select-list-wrapper .e-selectall-parent {
  line-height: 34px;
}

.e-bigger.e-small .e-multi-select-wrapper .e-chips-close {
  height: 30px;
  width: 30px;
}

.e-bigger.e-small .e-multi-select-wrapper {
  min-height: 30px;
}

.e-bigger.e-small .e-multi-select-wrapper input[type='text'] {
  height: 34px;
  min-height: 34px;
}

.e-small.e-bigger .e-multi-select-wrapper .e-delim-values {
  font-size: 14px;
  line-height: 34px;
}

.e-bigger.e-small .e-multi-select-wrapper .e-chips-close.e-close-hooker {
  margin-top: -3.4em;
  right: 0;
}

.e-small.e-bigger .e-multi-select-wrapper .e-chips > .e-chipcontent {
  padding: 7px 8px 7px 12px;
}

.e-content-placeholder.e-multiselect.e-placeholder-multiselect {
  background-size: 300px 33px;
  min-height: 33px;
}

.e-bigger .e-content-placeholder.e-multiselect.e-placeholder-multiselect, .e-bigger.e-content-placeholder.e-multiselect.e-placeholder-multiselect {
  background-size: 300px 40px;
  min-height: 40px;
}

.e-multi-select-wrapper .e-chips.e-chip-selected .e-chips-close::before {
  color: #fff;
}

.e-multi-select-wrapper .e-chips.e-chip-selected {
  background-color: #545b62;
}

.e-multi-select-wrapper .e-chips.e-chip-selected:hover {
  background-color: #6c757d;
}

.e-control.e-multiselect {
  box-sizing: border-box;
}

.e-multi-select-wrapper .e-chips > .e-chipcontent {
  color: #fff;
  font-family: "Helvetica Neue", "Helvetica", "Arial", sans-serif, "-apple-system", "BlinkMacSystemFont";
  font-size: 14px;
}

.e-multi-select-wrapper .e-chips.e-chip-selected > .e-chipcontent {
  color: #fff;
}

.e-multi-select-wrapper .e-chips.e-chip-selected > .e-chipcontent:hover {
  color: #fff;
}

.e-multi-select-wrapper .e-chips {
  background-color: #6c757d;
  border-radius: 4px;
  height: 24px;
}

.e-multi-select-wrapper .e-chips:hover {
  background-color: #5a6268;
}

.e-multi-select-wrapper .e-chips > .e-chipcontent:hover {
  color: #fff;
}

.e-multi-select-wrapper .e-chips .e-chips-close::before {
  color: #fff;
  font-size: 8px;
}

.e-bigger .e-multi-select-wrapper .e-chips .e-chips-close::before {
  font-size: 10px;
}

.e-multi-select-wrapper .e-chips.e-mob-chip.e-chip-selected {
  background-color: #6c757d;
  border-radius: 4px;
  color: #fff;
  height: 46px;
  line-height: 46px;
}

.e-multi-select-wrapper .e-chips.e-mob-chip.e-chip-selected .e-chipcontent {
  color: #fff;
}

.e-multi-select-wrapper .e-chips.e-mob-chip, .e-bigger .e-multi-select-wrapper .e-chips {
  height: 28px;
}

.e-control.e-popup.e-multi-select-list-wrapper {
  box-shadow: none;
  box-sizing: content-box;
  overflow: initial;
}

.e-control.e-popup.e-multi-select-list-wrapper .e-list-item.e-active {
  border-bottom: 1px solid transparent;
  border-left: 1px solid #5a8e8a;
  border-right: 1px solid #5a8e8a;
  border-top: 1px solid 1px inside;
}

.e-control.e-popup.e-multi-select-list-wrapper.e-checkbox .e-list-item.e-active {
  background-color: transparent;
  border-color: transparent;
  color: #212529;
}

.e-control.e-popup.e-multi-select-list-wrapper.e-multiselect-group.e-checkbox .e-list-group-item.e-active {
  background-color: transparent;
  border-color: transparent;
  color: #212529;
}

.e-control.e-popup.e-multi-select-list-wrapper.e-checkbox .e-list-item.e-active.e-item-focus {
  color: #212529;
}

.e-control.e-popup.e-multi-select-list-wrapper.e-multiselect-group.e-checkbox .e-list-group-item.e-active.e-item-focus {
  color: #212529;
}

.e-control.e-popup.e-multi-select-list-wrapper.e-checkbox .e-list-item.e-active.e-hover {
  background-color: #f2f4f6;
}

.e-control.e-popup.e-multi-select-list-wrapper.e-multiselect-group.e-checkbox .e-list-group-item.e-active.e-hover {
  background-color: #f2f4f6;
}

.e-control.e-popup.e-multi-select-list-wrapper .e-list-item.e-active:first-child {
  border-bottom: 1px solid transparent;
  border-top: 1px solid transparent;
}

.e-control.e-popup.e-multi-select-list-wrapper .e-list-item.e-active:last-child {
  border-bottom: 1px solid transparent;
}

.e-control.e-popup.e-multi-select-list-wrapper .e-list-item.e-active.e-item-focus + li.e-active {
  border-top: 1px solid transparent;
}

.e-control.e-popup.e-multi-select-list-wrapper .e-list-item.e-active.e-item-focus {
  background-color: #5a8e8a;
  border: 1px solid 1px inside #6c757d;
  box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075), 0 0 8px rgba(102, 175, 233, 0.6);
  color: #fff;
}

.e-control.e-popup.e-multi-select-list-wrapper.e-multiselect-group .e-list-group-item.e-active.e-item-focus {
  background-color: #5a8e8a;
  border: 1px solid 1px inside #6c757d;
  box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075), 0 0 8px rgba(102, 175, 233, 0.6);
  color: #fff;
}

.e-control.e-popup.e-multi-select-list-wrapper .e-list-item {
  border: 1px solid transparent;
}

.e-control.e-popup.e-multi-select-list-wrapper.e-multiselect-group .e-list-group-item {
  border: 1px solid transparent;
}

.e-multi-select-wrapper input.e-dropdownbase:-moz-placeholder {
  color: #6c757d;
}

.e-multi-select-wrapper input.e-dropdownbase::-moz-placeholder {
  color: #6c757d;
}

.e-multi-select-wrapper input.e-dropdownbase:-ms-input-placeholder {
  color: #6c757d;
}

.e-multi-select-wrapper input.e-dropdownbase::-webkit-input-placeholder {
  color: #6c757d;
}

.e-ul.e-reorder {
  border-bottom: 1px solid rgba(0, 0, 0, 0.15);
}

.e-multi-select-list-wrapper .e-selectall-parent {
  border-bottom: 1px solid rgba(0, 0, 0, 0.15);
}

.e-ddl.e-popup.e-multi-select-list-wrapper .e-list-item.e-active.e-item-focus .e-checkbox-wrapper .e-frame.e-check {
  background-color: #fff;
  border-color: #fff;
  color: #5a8e8a;
}

.e-ddl.e-popup.e-multi-select-list-wrapper.e-multiselect-group .e-list-group-item.e-active.e-item-focus .e-checkbox-wrapper .e-frame.e-check {
  background-color: #fff;
  border-color: #fff;
  color: #5a8e8a;
}

.e-multi-select-wrapper .e-delim-values {
  color: #fff;
  color: #212529;
}

.e-multi-select-wrapper .e-chips-close.e-close-hooker {
  color: #495057;
}

.e-small .e-multi-select-wrapper .e-chips {
  height: 22px;
}

.e-small .e-multi-select-wrapper .e-chips > .e-chipcontent {
  font-size: 12px;
}

.e-small .e-multi-select-wrapper .e-chips .e-chips-close::before {
  left: 6px;
  top: 5px;
  font-size: 8px;
}

.e-small .e-multi-select-wrapper .e-close-hooker::before {
  left: 0;
}

.e-bigger.e-small .e-multi-select-wrapper .e-chips {
  height: 30px;
}

.e-bigger.e-small .e-multi-select-wrapper .e-chips > .e-chipcontent {
  font-size: 15px;
}

.e-bigger.e-small .e-multi-select-wrapper .e-chips .e-chips-close::before {
  left: 13px;
  top: 11px;
  font-size: 10px;
}

.e-control .e-js [class^='e-'], .e-control .e-js [class*=' e-'] {
  box-sizing: content-box;
}
