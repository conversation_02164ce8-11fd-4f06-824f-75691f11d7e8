/*! vscroll icons */
.e-control.e-vscroll.e-scroll-device .e-nav-up-arrow::before {
  content: '\e85e';
}

.e-control.e-vscroll.e-scroll-device .e-nav-down-arrow::before {
  content: '\e84f';
}

.e-control.e-vscroll .e-nav-up-arrow::before {
  content: '\e910';
  line-height: normal;
}

.e-control.e-vscroll .e-nav-down-arrow::before {
  content: '\e916';
  line-height: normal;
}

/*! v-scroll layout */
.e-bigger .e-control.e-vscroll:not(.e-scroll-device), .e-control.e-vscroll.e-bigger:not(.e-scroll-device) {
  padding: 50px 0;
}

.e-bigger .e-control.e-vscroll .e-icons, .e-control.e-vscroll.e-bigger .e-icons {
  font-size: 18px;
}

.e-bigger .e-control.e-vscroll.e-rtl .e-scroll-overlay.e-scroll-down-overlay, .e-control.e-vscroll.e-bigger.e-rtl .e-scroll-overlay.e-scroll-down-overlay {
  left: 50px;
}

.e-bigger .e-control.e-vscroll .e-scroll-overlay.e-scroll-down-overlay, .e-control.e-vscroll.e-bigger .e-scroll-overlay.e-scroll-down-overlay {
  right: 50px;
}

.e-bigger .e-control.e-vscroll .e-scroll-nav, .e-control.e-vscroll.e-bigger .e-scroll-nav {
  height: 50px;
}

.e-control.e-vscroll {
  display: block;
  position: relative;
  width: inherit;
}

.e-control.e-vscroll.e-rtl.e-scroll-device .e-scroll-nav {
  transform: skewX(-16deg) translateX(-6px);
}

.e-control.e-vscroll.e-rtl.e-scroll-device .e-scroll-overlay.e-scroll-down-overlay {
  left: 52px;
  right: auto;
  transform: skewX(-16deg) translateX(-6px);
}

.e-control.e-vscroll.e-rtl.e-scroll-device .e-scroll-overlay.e-scroll-up-overlay {
  left: auto;
  right: 0;
}

.e-control.e-vscroll:not(.e-scroll-device) {
  padding: 0 40px;
}

.e-control.e-vscroll.e-scroll-device .e-scroll-nav {
  transform: skewX(-16deg) translateX(6px);
  width: 52px;
  z-index: 1001;
}

.e-control.e-vscroll.e-scroll-device .e-scroll-nav .e-nav-arrow {
  font-size: 14px;
  transform: skewX(16deg);
}

.e-control.e-vscroll.e-scroll-device .e-scroll-overlay {
  opacity: 0.5;
  pointer-events: none;
  position: absolute;
  top: 0;
  z-index: 100;
}

.e-control.e-vscroll.e-scroll-device .e-scroll-overlay.e-scroll-down-overlay {
  left: auto;
  right: 52px;
  transform: skewX(-16deg) translateX(6px);
}

.e-control.e-vscroll.e-scroll-device .e-scroll-overlay.e-scroll-up-overlay {
  left: 0;
  right: auto;
}

.e-control.e-vscroll > * {
  height: inherit;
}

.e-control.e-vscroll .e-vscroll-content {
  display: inline-block;
  height: auto;
  position: relative;
  -ms-touch-action: none;
      touch-action: none;
  width: 100%;
}

.e-control.e-vscroll .e-vscroll-content > * {
  pointer-events: auto;
}

.e-control.e-vscroll.e-rtl .e-scroll-nav.e-scroll-up-nav {
  left: auto;
  right: 0;
}

.e-control.e-vscroll.e-rtl .e-scroll-nav.e-scroll-down-nav {
  left: 0;
  right: auto;
}

.e-control.e-vscroll .e-scroll-nav {
  -ms-flex-align: center;
      align-items: center;
  cursor: pointer;
  display: -ms-flexbox;
  display: flex;
  height: 40px;
  overflow: hidden;
  position: absolute;
  width: 100%;
}

.e-control.e-vscroll .e-scroll-nav.e-scroll-up-nav {
  top: 0;
}

.e-control.e-vscroll .e-scroll-nav.e-scroll-down-nav {
  bottom: 0;
}

.e-control.e-vscroll .e-scroll-nav.e-ie-align {
  display: table;
}

.e-control.e-vscroll .e-nav-arrow {
  position: relative;
}

.e-control.e-vscroll .e-nav-arrow.e-icons {
  display: table-cell;
  text-align: center;
  vertical-align: middle;
  width: 100%;
}

/*! v-scroll theme */
.e-control.e-vscroll .e-icons {
  color: #000;
}

.e-control.e-vscroll.e-rtl.e-scroll-device .e-scroll-nav.e-scroll-right-nav {
  border-color: #4a4848;
  box-shadow: 4px 0 8px 0 rgba(0, 0, 0, 0.06);
}

.e-control.e-vscroll.e-scroll-device .e-scroll-nav.e-scroll-right-nav {
  background-color: #201f1f;
  border-color: #4a4848;
  border-width: 1px;
  box-shadow: -4px 0 8px 0 rgba(0, 0, 0, 0.06);
}

.e-control.e-vscroll.e-scroll-device .e-scroll-nav.e-scroll-right-nav .e-nav-arrow {
  color: #0074cc;
}

.e-control.e-vscroll .e-scroll-overlay {
  background-color: transparent;
  background-repeat: repeat-x;
}

.e-control.e-vscroll .e-scroll-overlay.e-scroll-up-overlay {
  background-image: linear-gradient(-270deg, #201f1f 0%, rgba(32, 31, 31, 0) 100%);
}

.e-control.e-vscroll .e-scroll-overlay.e-scroll-down-overlay {
  background-image: linear-gradient(-270deg, rgba(32, 31, 31, 0) 0%, #201f1f 100%);
}

.e-control.e-vscroll.e-rtl .e-scroll-nav {
  background: #201f1f;
}

.e-control.e-vscroll.e-rtl .e-scroll-nav:hover {
  background: #333232;
  border: "";
  border-color: "";
  color: #dadada;
}

.e-control.e-vscroll:not(.e-scroll-device) .e-scroll-nav:hover {
  background: #333232;
  border: "";
  color: #dadada;
}

.e-control.e-vscroll:not(.e-scroll-device) .e-scroll-nav:focus {
  background: #333232;
  border: "";
  border-color: "";
  color: #dadada;
}

.e-control.e-vscroll:not(.e-scroll-device) .e-scroll-nav:active {
  background: #c7e7ff;
  border: "";
  box-shadow: "";
  color: #000;
}

.e-control.e-vscroll .e-scroll-nav {
  background: #201f1f;
}

.e-control.e-vscroll .e-scroll-nav.e-scroll-up-nav {
  border-bottom: 1px solid #9a9a9a;
}

.e-control.e-vscroll .e-scroll-nav.e-scroll-down-nav {
  border-top: 1px solid #9a9a9a;
}

.e-control.e-vscroll .e-scroll-nav::after {
  content: '';
}

.e-control.e-vscroll .e-scroll-nav:active::after {
  content: '';
}

.e-control .e-js [class^='e-'], .e-control .e-js [class*=' e-'] {
  box-sizing: content-box;
}
