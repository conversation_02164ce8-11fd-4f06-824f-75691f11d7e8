/*! vscroll icons */
.e-vscroll.e-scroll-device .e-nav-up-arrow::before {
  content: '\e85e';
}

.e-vscroll.e-scroll-device .e-nav-down-arrow::before {
  content: '\e84f';
}

.e-vscroll .e-nav-up-arrow::before {
  content: '\e910';
  line-height: normal;
}

.e-vscroll .e-nav-down-arrow::before {
  content: '\e916';
  line-height: normal;
}

/*! v-scroll layout */
.e-bigger .e-vscroll:not(.e-scroll-device),
.e-vscroll.e-bigger:not(.e-scroll-device) {
  padding: 50px 0;
}

.e-bigger .e-vscroll .e-icons,
.e-vscroll.e-bigger .e-icons {
  font-size: 18px;
}

.e-bigger .e-vscroll.e-rtl .e-scroll-overlay.e-scroll-down-overlay,
.e-vscroll.e-bigger.e-rtl .e-scroll-overlay.e-scroll-down-overlay {
  left: 50px;
}

.e-bigger .e-vscroll .e-scroll-overlay.e-scroll-down-overlay,
.e-vscroll.e-bigger .e-scroll-overlay.e-scroll-down-overlay {
  right: 50px;
}

.e-bigger .e-vscroll .e-scroll-nav,
.e-vscroll.e-bigger .e-scroll-nav {
  height: 50px;
}

.e-vscroll {
  display: block;
  position: relative;
  width: inherit;
}

.e-vscroll.e-rtl.e-scroll-device .e-scroll-nav {
  transform: skewX(-16deg) translateX(-6px);
}

.e-vscroll.e-rtl.e-scroll-device .e-scroll-overlay.e-scroll-down-overlay {
  left: 52px;
  right: auto;
  transform: skewX(-16deg) translateX(-6px);
}

.e-vscroll.e-rtl.e-scroll-device .e-scroll-overlay.e-scroll-up-overlay {
  left: auto;
  right: 0;
}

.e-vscroll:not(.e-scroll-device) {
  padding: 0 40px;
}

.e-vscroll.e-scroll-device .e-scroll-nav {
  transform: skewX(-16deg) translateX(6px);
  width: 52px;
  z-index: 1001;
}

.e-vscroll.e-scroll-device .e-scroll-nav .e-nav-arrow {
  font-size: 14px;
  transform: skewX(16deg);
}

.e-vscroll.e-scroll-device .e-scroll-overlay {
  opacity: 0.5;
  pointer-events: none;
  position: absolute;
  top: 0;
  z-index: 100;
}

.e-vscroll.e-scroll-device .e-scroll-overlay.e-scroll-down-overlay {
  left: auto;
  right: 52px;
  transform: skewX(-16deg) translateX(6px);
}

.e-vscroll.e-scroll-device .e-scroll-overlay.e-scroll-up-overlay {
  left: 0;
  right: auto;
}

.e-vscroll > * {
  height: inherit;
}

.e-vscroll .e-vscroll-content {
  display: inline-block;
  height: auto;
  position: relative;
  -ms-touch-action: none;
      touch-action: none;
  width: 100%;
}

.e-vscroll .e-vscroll-content > * {
  pointer-events: auto;
}

.e-vscroll.e-rtl .e-scroll-nav.e-scroll-up-nav {
  left: auto;
  right: 0;
}

.e-vscroll.e-rtl .e-scroll-nav.e-scroll-down-nav {
  left: 0;
  right: auto;
}

.e-vscroll .e-scroll-nav {
  -ms-flex-align: center;
      align-items: center;
  cursor: pointer;
  display: -ms-flexbox;
  display: flex;
  height: 40px;
  overflow: hidden;
  position: absolute;
  width: 100%;
}

.e-vscroll .e-scroll-nav.e-scroll-up-nav {
  top: 0;
}

.e-vscroll .e-scroll-nav.e-scroll-down-nav {
  bottom: 0;
}

.e-vscroll .e-scroll-nav.e-ie-align {
  display: table;
}

.e-vscroll .e-nav-arrow {
  position: relative;
}

.e-vscroll .e-nav-arrow.e-icons {
  display: table-cell;
  text-align: center;
  vertical-align: middle;
  width: 100%;
}

/*! v-scroll theme */
.e-vscroll .e-icons {
  color: #400074;
}

.e-vscroll.e-rtl.e-scroll-device .e-scroll-nav.e-scroll-right-nav {
  border-color: #000;
  box-shadow: 4px 0 8px 0 rgba(0, 0, 0, 0.06);
}

.e-vscroll.e-scroll-device .e-scroll-nav.e-scroll-right-nav {
  background-color: #d8d8d8;
  border-color: #000;
  border-width: 1px;
  box-shadow: -4px 0 8px 0 rgba(0, 0, 0, 0.06);
}

.e-vscroll.e-scroll-device .e-scroll-nav.e-scroll-right-nav .e-nav-arrow {
  color: #400074;
}

.e-vscroll .e-scroll-overlay {
  background-color: transparent;
  background-repeat: repeat-x;
}

.e-vscroll .e-scroll-overlay.e-scroll-up-overlay {
  background-image: linear-gradient(-270deg, #e4e4e4 0%, rgba(228, 228, 228, 0) 100%);
}

.e-vscroll .e-scroll-overlay.e-scroll-down-overlay {
  background-image: linear-gradient(-270deg, rgba(228, 228, 228, 0) 0%, #e4e4e4 100%);
}

.e-vscroll.e-rtl .e-scroll-nav {
  background: #fff;
}

.e-vscroll.e-rtl .e-scroll-nav:hover {
  background: #ecf;
  border: "";
  border-color: "";
  color: #000;
}

.e-vscroll.e-rtl .e-scroll-nav:hover:active {
  background: #400074;
}

.e-vscroll:not(.e-scroll-device) .e-scroll-nav:hover {
  background: #ecf;
  border: "";
  color: #000;
  border: 1px solid #000;
}

.e-vscroll:not(.e-scroll-device) .e-scroll-nav:hover:active {
  border: 0;
}

.e-vscroll:not(.e-scroll-device) .e-scroll-nav:focus {
  background: #ecf;
  border: "";
  border-color: "";
  color: #000;
  background: inherit;
  border: 2px solid #fff;
}

.e-vscroll:not(.e-scroll-device) .e-scroll-nav:active {
  background: #400074;
  border: "";
  box-shadow: "";
  color: #000;
}

.e-vscroll:not(.e-scroll-device) .e-scroll-nav:active .e-icons {
  color: #fff;
}

.e-vscroll .e-scroll-nav {
  background: #fff;
}

.e-vscroll .e-scroll-nav.e-scroll-up-nav {
  border-bottom: 1px solid #000;
}

.e-vscroll .e-scroll-nav.e-scroll-down-nav {
  border-top: 1px solid #000;
}

.e-vscroll .e-scroll-nav::after {
  content: '';
}

.e-vscroll .e-scroll-nav:active::after {
  content: '';
}
