.e-dropdownbase .e-list-item .e-list-icon {
  padding: 0 10px 0 0;
}

.e-bigger .e-dropdownbase .e-list-item .e-list-icon,
.e-bigger.e-small .e-dropdownbase .e-list-item .e-list-icon {
  padding: 0 12px 0 0;
}

.e-small .e-dropdownbase .e-list-item .e-list-icon {
  padding: 0 8px 0 0;
}

.e-bigger .e-dropdownbase,
.e-dropdownbase.e-bigger {
  min-height: 45px;
}

.e-bigger .e-dropdownbase .e-list-item,
.e-bigger .e-dropdownbase .e-list-group-item,
.e-bigger .e-dropdownbase .e-fixed-head,
.e-dropdownbase.e-bigger .e-list-item,
.e-dropdownbase.e-bigger .e-list-group-item,
.e-dropdownbase.e-bigger .e-fixed-head {
  line-height: 48px;
}

.e-bigger .e-dropdownbase .e-list-item .e-list-icon,
.e-dropdownbase.e-bigger .e-list-item .e-list-icon {
  font-size: 20px;
}

.e-dropdownbase {
  display: block;
  height: 100%;
  min-height: 36px;
  position: relative;
  width: 100%;
}

.e-dropdownbase .e-list-parent {
  margin: 0;
  padding: 0;
}

.e-dropdownbase .e-list-group-item,
.e-dropdownbase .e-fixed-head {
  cursor: default;
}

.e-dropdownbase .e-list-item {
  cursor: pointer;
  overflow: hidden;
  position: relative;
  text-overflow: ellipsis;
  vertical-align: middle;
  white-space: nowrap;
  width: 100%;
}

.e-dropdownbase .e-list-item .e-list-icon {
  font-size: 16px;
  vertical-align: middle;
}

.e-dropdownbase .e-fixed-head {
  position: absolute;
  top: 0;
}

.e-rtl .e-dropdownbase .e-fixed-head {
  left: 33px;
}

.e-dropdownbase.e-content {
  overflow: auto;
  position: relative;
}

.e-popup.e-ddl .e-dropdownbase.e-nodata {
  color: #333;
  cursor: default;
  font-family: "Segoe UI", "GeezaPro", "DejaVu Serif", sans-serif, "-apple-system", "BlinkMacSystemFont";
  font-size: 14px;
  padding: 14px 16px;
  text-align: center;
}

.e-rtl .e-dropdownbase.e-dd-group .e-list-item {
  padding-right: 20px;
}

.e-dropdownbase.e-dd-group .e-list-item {
  padding-left: 20px;
  text-indent: 0;
}

.e-small .e-dropdownbase.e-dd-group .e-list-item {
  text-indent: 0;
}

.e-small.e-bigger .e-dropdownbase.e-dd-group .e-list-item {
  text-indent: 0;
}

.e-popup.e-multi-select-list-wrapper.e-multiselect-group .e-dropdownbase.e-dd-group .e-list-group-item {
  text-indent: 0;
}

.e-popup.e-multi-select-list-wrapper.e-multiselect-group .e-dropdownbase.e-dd-group .e-list-group-item {
  cursor: pointer;
  font-weight: normal;
  overflow: hidden;
  position: relative;
  text-overflow: ellipsis;
  vertical-align: middle;
  white-space: nowrap;
  width: 100%;
}

.e-rtl.e-multiselect-group .e-dropdownbase.e-dd-group .e-list-item {
  padding-right: 20px;
}

.e-dropdownbase {
  border-color: rgba(234, 234, 234, 0.25);
}

.e-dropdownbase .e-list-item {
  background-color: #fff;
  border-bottom: 1px;
  border-color: none;
  color: rgba(51, 51, 51, 0.87);
  font-family: "Segoe UI", "GeezaPro", "DejaVu Serif", sans-serif, "-apple-system", "BlinkMacSystemFont";
  font-size: 14px;
  line-height: 36px;
  min-height: 36px;
  padding-right: 16px;
  text-indent: 10px;
}

.e-dropdownbase .e-list-group-item, .e-fixed-head {
  background-color: #fff;
  border-color: none;
  color: #0078d6;
  font-family: "Segoe UI", "GeezaPro", "DejaVu Serif", sans-serif, "-apple-system", "BlinkMacSystemFont";
  font-size: 14px;
  font-weight: 600;
  line-height: 36px;
  min-height: 36px;
  padding-left: 10px;
  padding-right: 16px;
}

.e-dropdownbase .e-list-item.e-active,
.e-dropdownbase .e-list-item.e-active.e-hover {
  background-color: #d1ebff;
  border-color: #fff;
  color: #333;
}

.e-dropdownbase .e-list-item.e-hover {
  background-color: #f4f4f4;
  border-color: #fff;
  color: #333;
}

.e-dropdownbase .e-list-item:last-child {
  border-bottom: 0;
}

.e-dropdownbase .e-list-item.e-item-focus {
  background-color: #f4f4f4;
}

.e-bigger .e-dropdownbase .e-list-group-item,
.e-bigger .e-dropdownbase .e-fixed-head {
  font-size: 14px;
}

.e-multi-column.e-ddl.e-popup.e-popup-open table {
  border-collapse: collapse;
  table-layout: fixed;
  width: 100%;
}

.e-multi-column.e-ddl.e-popup.e-popup-open th,
.e-multi-column.e-ddl.e-popup.e-popup-open td {
  display: table-cell;
  overflow: hidden;
  padding-right: 16px;
  text-indent: 10px;
  text-overflow: ellipsis;
}

.e-multi-column.e-ddl.e-popup.e-popup-open th {
  line-height: 36px;
  text-align: left;
}

.e-multi-column.e-ddl.e-popup.e-popup-open .e-ddl-header {
  background-color: #fff;
  border-color: #eaeaea;
  border-style: solid;
  border-width: 0 0 1px 0;
  color: #0078d6;
  font-family: "Segoe UI", "GeezaPro", "DejaVu Serif", sans-serif, "-apple-system", "BlinkMacSystemFont";
  font-size: 14px;
  font-weight: 600;
  text-indent: 10px;
}

.e-multi-column.e-ddl.e-popup.e-popup-open .e-dropdownbase .e-list-item {
  padding-right: 0;
}

.e-multi-column.e-ddl.e-popup.e-popup-open.e-scroller .e-ddl-header {
  padding-right: 16px;
}

.e-multi-column.e-ddl.e-popup.e-popup-open .e-ddl-header,
.e-multi-column.e-ddl.e-popup.e-popup-open.e-ddl-device .e-ddl-header {
  padding-right: 0;
}

.e-multi-column.e-ddl.e-popup.e-popup-open .e-text-center {
  text-align: center;
}

.e-multi-column.e-ddl.e-popup.e-popup-open .e-text-right {
  text-align: right;
}

.e-multi-column.e-ddl.e-popup.e-popup-open .e-text-left {
  text-align: left;
}

.e-small .e-dropdownbase .e-list-item,
.e-dropdownbase.e-small .e-list-item {
  color: #333;
  line-height: 26px;
  min-height: 26px;
  text-indent: 10px;
}

.e-small .e-dropdownbase .e-list-group-item,
.e-small .e-dropdownbase .e-fixed-head,
.e-dropdownbase.e-small .e-list-group-item,
.e-dropdownbase.e-small .e-fixed-head {
  line-height: 26px;
  min-height: 26px;
}

.e-small .e-dropdownbase .e-list-item .e-list-icon,
.e-dropdownbase.e-small .e-list-item .e-list-icon {
  font-size: 14px;
}

.e-bigger.e-small .e-dropdownbase .e-list-item,
.e-dropdownbase.e-small.e-bigger .e-list-item {
  color: #333;
  line-height: 40px;
  min-height: 40px;
  text-indent: 16px;
}

.e-bigger.e-small .e-dropdownbase .e-list-group-item,
.e-bigger.e-small .e-dropdownbase .e-fixed-head,
.e-dropdownbase.e-small.e-bigger .e-list-group-item,
.e-dropdownbase.e-small.e-bigger .e-fixed-head {
  line-height: 40px;
  min-height: 40px;
}

.e-bigger.e-small .e-dropdownbase .e-list-item .e-list-icon,
.e-dropdownbase.e-small.e-bigger .e-list-item .e-list-icon {
  font-size: 18px;
}

.e-bigger.e-small .e-dropdownbase .e-list-group-item,
.e-bigger.e-small .e-dropdownbase .e-fixed-head,
.e-dropdownbase.e-bigger.e-small .e-list-group-item,
.e-dropdownbase.e-bigger.e-small .e-fixed-head {
  font-size: 14px;
}

.e-ddl.e-popup.e-multiselect-group .e-list-group-item {
  background-color: #fff;
  border-bottom: 1px;
  border-color: none;
  color: rgba(51, 51, 51, 0.87);
  font-family: "Segoe UI", "GeezaPro", "DejaVu Serif", sans-serif, "-apple-system", "BlinkMacSystemFont";
  font-size: 14px;
  padding-right: 16px;
  text-indent: 10px;
}

.e-ddl.e-popup.e-multiselect-group .e-list-group-item.e-item-focus {
  background-color: #f4f4f4;
}

.e-ddl.e-popup.e-multiselect-group .e-list-group-item.e-active,
.e-ddl.e-popup.e-multiselect-group .e-list-group-item.e-active.e-hover {
  background-color: #d1ebff;
  border-color: #fff;
  color: #333;
}

.e-ddl.e-popup.e-multiselect-group .e-list-group-item.e-hover {
  background-color: #f4f4f4;
  border-color: #fff;
  color: #333;
}
