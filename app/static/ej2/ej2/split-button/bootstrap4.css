/*! splitbutton layout */
.e-split-btn-wrapper {
  display: -ms-inline-flexbox;
  display: inline-flex;
  white-space: nowrap;
  width: -webkit-min-content;
  width: min-content;
}

.e-split-btn-wrapper .e-split-btn {
  border-bottom-right-radius: 0;
  border-top-right-radius: 0;
  text-transform: none;
  z-index: 1;
  padding-left: 8px;
  padding-right: 8px;
}

.e-split-btn-wrapper .e-split-btn:focus {
  outline-offset: 0;
}

.e-split-btn-wrapper .e-split-btn:focus, .e-split-btn-wrapper .e-split-btn:hover, .e-split-btn-wrapper .e-split-btn:active {
  z-index: 2;
}

.e-split-btn-wrapper .e-split-btn.e-top-icon-btn {
  padding-bottom: 10px;
  padding-top: 10px;
}

.e-split-btn-wrapper .e-split-btn .e-btn-icon {
  font-size: 14px;
  margin-left: 0;
  padding-right: 8px;
  width: auto;
}

.e-split-btn-wrapper .e-split-btn .e-icon-top {
  padding-bottom: 4px;
  padding-right: 0;
}

.e-split-btn-wrapper .e-split-btn.e-icon-btn {
  padding-left: 8px;
  padding-right: 8px;
}

.e-split-btn-wrapper .e-split-btn.e-icon-btn .e-btn-icon {
  padding-right: 0;
}

.e-split-btn-wrapper .e-dropdown-btn {
  border-bottom-left-radius: 0;
  border-top-left-radius: 0;
  margin-left: -1px;
  padding-left: 8px;
  padding-right: 8px;
}

.e-split-btn-wrapper .e-dropdown-btn:focus, .e-split-btn-wrapper .e-dropdown-btn:hover, .e-split-btn-wrapper .e-dropdown-btn:active {
  z-index: 2;
}

.e-split-btn-wrapper .e-dropdown-btn:disabled:focus, .e-split-btn-wrapper .e-dropdown-btn:disabled:hover, .e-split-btn-wrapper .e-dropdown-btn:disabled:active {
  z-index: 0;
}

.e-split-btn-wrapper .e-dropdown-btn:focus {
  outline-offset: 0;
}

.e-split-btn-wrapper .e-btn {
  box-shadow: none;
}

.e-split-btn-wrapper .e-btn:hover, .e-split-btn-wrapper .e-btn:focus {
  box-shadow: none;
}

.e-split-btn-wrapper .e-btn:active {
  box-shadow: none;
}

.e-split-btn-wrapper .e-btn:disabled {
  box-shadow: none;
}

.e-split-btn-wrapper.e-rtl .e-split-btn {
  border-bottom-left-radius: 0;
  border-bottom-right-radius: 4px;
  border-top-left-radius: 0;
  border-top-right-radius: 4px;
  margin-left: -1px;
  padding-left: 8px;
  padding-right: 8px;
}

.e-split-btn-wrapper.e-rtl .e-split-btn .e-btn-icon {
  margin-right: 0;
  padding-left: 8px;
  padding-right: 0;
}

.e-split-btn-wrapper.e-rtl .e-split-btn .e-icon-top {
  padding-left: 0;
}

.e-split-btn-wrapper.e-rtl .e-split-btn.e-icon-btn {
  padding-left: 8px;
  padding-right: 8px;
}

.e-split-btn-wrapper.e-rtl .e-split-btn.e-icon-btn .e-btn-icon {
  padding-left: 0;
}

.e-split-btn-wrapper.e-rtl .e-dropdown-btn {
  border-bottom-left-radius: 4px;
  border-bottom-right-radius: 0;
  border-top-left-radius: 4px;
  border-top-right-radius: 0;
  margin-left: 0;
}

.e-split-btn-wrapper.e-vertical {
  display: inline-block;
}

.e-split-btn-wrapper.e-vertical .e-split-btn,
.e-split-btn-wrapper.e-vertical .e-dropdown-btn {
  display: block;
  width: 100%;
}

.e-split-btn-wrapper.e-vertical .e-split-btn {
  border-bottom-left-radius: 0;
  border-top-right-radius: 4px;
  line-height: 1;
  padding-bottom: 8px;
  padding-right: 8px;
}

.e-split-btn-wrapper.e-vertical .e-split-btn .e-icon-top {
  padding-bottom: 4px;
}

.e-split-btn-wrapper.e-vertical .e-dropdown-btn {
  border-bottom-left-radius: 4px;
  border-top-right-radius: 0;
  line-height: 0.334em;
  margin-left: 0;
  margin-top: -1px;
  padding-bottom: 8px;
  padding-top: 8px;
}

.e-split-btn-wrapper.e-vertical .e-dropdown-btn .e-icon-bottom {
  padding-top: 0;
}

.e-split-btn-wrapper.e-vertical.e-rtl .e-split-btn {
  border-bottom-right-radius: 0;
  border-top-left-radius: 4px;
  margin-left: 0;
}

.e-split-btn-wrapper.e-vertical.e-rtl .e-dropdown-btn {
  border-bottom-right-radius: 4px;
  border-top-left-radius: 0;
}

.e-bigger .e-split-btn-wrapper .e-split-btn,
.e-bigger.e-split-btn-wrapper .e-split-btn {
  padding-left: 12px;
  padding-right: 12px;
}

.e-bigger .e-split-btn-wrapper .e-split-btn.e-top-icon-btn,
.e-bigger.e-split-btn-wrapper .e-split-btn.e-top-icon-btn {
  padding-bottom: 12px;
  padding-top: 12px;
}

.e-bigger .e-split-btn-wrapper .e-split-btn .e-btn-icon,
.e-bigger.e-split-btn-wrapper .e-split-btn .e-btn-icon {
  font-size: 16px;
  margin-left: 0;
  padding-right: 10px;
  width: auto;
}

.e-bigger .e-split-btn-wrapper .e-split-btn .e-icon-top,
.e-bigger.e-split-btn-wrapper .e-split-btn .e-icon-top {
  padding-bottom: 6px;
  padding-right: 0;
}

.e-bigger .e-split-btn-wrapper .e-split-btn.e-icon-btn,
.e-bigger.e-split-btn-wrapper .e-split-btn.e-icon-btn {
  padding-left: 12px;
  padding-right: 12px;
}

.e-bigger .e-split-btn-wrapper .e-split-btn.e-icon-btn .e-btn-icon,
.e-bigger.e-split-btn-wrapper .e-split-btn.e-icon-btn .e-btn-icon {
  padding-right: 0;
}

.e-bigger .e-split-btn-wrapper .e-dropdown-btn,
.e-bigger.e-split-btn-wrapper .e-dropdown-btn {
  padding-left: 10px;
  padding-right: 10px;
}

.e-bigger .e-split-btn-wrapper.e-rtl .e-split-btn,
.e-bigger.e-split-btn-wrapper.e-rtl .e-split-btn {
  padding-left: 12px;
  padding-right: 12px;
}

.e-bigger .e-split-btn-wrapper.e-rtl .e-split-btn .e-btn-icon,
.e-bigger.e-split-btn-wrapper.e-rtl .e-split-btn .e-btn-icon {
  padding-left: 10px;
  padding-right: 0;
}

.e-bigger .e-split-btn-wrapper.e-rtl .e-split-btn .e-icon-top,
.e-bigger.e-split-btn-wrapper.e-rtl .e-split-btn .e-icon-top {
  padding-left: 0;
}

.e-bigger .e-split-btn-wrapper.e-rtl .e-split-btn.e-icon-btn,
.e-bigger.e-split-btn-wrapper.e-rtl .e-split-btn.e-icon-btn {
  padding-left: 12px;
  padding-right: 12px;
}

.e-bigger .e-split-btn-wrapper.e-rtl .e-split-btn.e-icon-btn .e-btn-icon,
.e-bigger.e-split-btn-wrapper.e-rtl .e-split-btn.e-icon-btn .e-btn-icon {
  padding-left: 0;
}

.e-bigger .e-split-btn-wrapper.e-vertical .e-split-btn,
.e-bigger.e-split-btn-wrapper.e-vertical .e-split-btn {
  padding-bottom: 12px;
  padding-right: 12px;
}

.e-bigger .e-split-btn-wrapper.e-vertical .e-dropdown-btn,
.e-bigger.e-split-btn-wrapper.e-vertical .e-dropdown-btn {
  padding-bottom: 12px;
  padding-top: 12px;
}

.e-bigger .e-split-btn-wrapper.e-vertical .e-dropdown-btn .e-icon-bottom,
.e-bigger.e-split-btn-wrapper.e-vertical .e-dropdown-btn .e-icon-bottom {
  padding-top: 0;
}

/*! splitbutton theme */
.e-split-btn-wrapper .e-split-btn {
  border-right-color: #6c757d;
}

.e-split-btn-wrapper .e-split-btn:focus {
  border-right-color: #6c757d;
}

.e-split-btn-wrapper .e-split-btn:active {
  border-right-color: #4e555b;
}

.e-split-btn-wrapper .e-split-btn:disabled {
  border-right-color: rgba(108, 117, 125, 0.65);
}

.e-split-btn-wrapper:hover .e-split-btn {
  border-right-color: #545b62;
}

.e-split-btn-wrapper:hover .e-split-btn:disabled {
  border-right-color: transparent;
}

.e-split-btn-wrapper.e-rtl .e-split-btn {
  border-left-color: #6c757d;
  border-right-color: #6c757d;
}

.e-split-btn-wrapper.e-rtl .e-split-btn:focus {
  border-left-color: #6c757d;
  border-right-color: #6c757d;
}

.e-split-btn-wrapper.e-rtl .e-split-btn:active {
  border-left-color: #4e555b;
  border-right-color: #4e555b;
}

.e-split-btn-wrapper.e-rtl .e-split-btn:disabled {
  border-left-color: transparent;
  border-right-color: rgba(108, 117, 125, 0.65);
}

.e-split-btn-wrapper.e-rtl:hover .e-split-btn {
  border-left-color: #545b62;
  border-right-color: #545b62;
}

.e-split-btn-wrapper.e-rtl:hover .e-split-btn:disabled {
  border-left-color: transparent;
}

.e-split-btn-wrapper.e-vertical .e-split-btn {
  border-bottom-color: #6c757d;
  border-right-color: #6c757d;
}

.e-split-btn-wrapper.e-vertical .e-split-btn:focus {
  border-bottom-color: #6c757d;
  border-right-color: #6c757d;
}

.e-split-btn-wrapper.e-vertical .e-split-btn:active {
  border-bottom-color: #4e555b;
  border-right-color: #4e555b;
}

.e-split-btn-wrapper.e-vertical .e-split-btn:disabled {
  border-bottom-color: transparent;
  border-right-color: rgba(108, 117, 125, 0.65);
}

.e-split-btn-wrapper.e-vertical:hover .e-split-btn {
  border-bottom-color: #545b62;
  border-right-color: #545b62;
}

.e-split-btn-wrapper.e-vertical:hover .e-split-btn:disabled {
  border-bottom-color: transparent;
}

.e-split-btn-wrapper.e-vertical.e-rtl .e-split-btn {
  border-left-color: #6c757d;
}

.e-split-btn-wrapper.e-vertical.e-rtl .e-split-btn:focus {
  border-left-color: #6c757d;
}

.e-split-btn-wrapper.e-vertical.e-rtl .e-split-btn:active {
  border-left-color: #6c757d;
}

.e-split-btn-wrapper.e-vertical.e-rtl .e-split-btn:disabled {
  border-left-color: rgba(108, 117, 125, 0.65);
}

.e-split-btn-wrapper.e-vertical.e-rtl:hover .e-split-btn {
  border-left-color: #545b62;
}
