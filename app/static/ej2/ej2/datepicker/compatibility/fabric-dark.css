/*! component icons */
.e-input-group-icon.e-date-icon,
.e-control.e-control-wrapper .e-input-group-icon.e-date-icon {
  font-size: 16px;
  margin: 0;
  outline: none;
}

.e-input-group-icon.e-date-icon::before,
.e-control.e-control-wrapper .e-input-group-icon.e-date-icon::before {
  content: '\e93a';
  font-family: 'e-icons';
}

.e-input-group-icon.e-date-icon:focus,
.e-control.e-control-wrapper .e-input-group-icon.e-date-icon:focus {
  background: #414040;
  border-radius: 0;
}

.e-bigger .e-input-group-icon.e-date-icon,
.e-control.e-control-wrapper.e-bigger .e-input-group-icon.e-date-icon,
.e-control.e-bigger .e-control-wrapper .e-input-group-icon.e-date-icon {
  font-size: 20px;
  margin: 0;
}

.e-bigger .e-input-group-icon.e-date-icon::before,
.e-control.e-control-wrapper.e-bigger .e-input-group-icon.e-date-icon::before,
.e-control.e-bigger .e-control-wrapper .e-input-group-icon.e-date-icon::before {
  content: '\e93a';
  font-family: 'e-icons';
}

.e-bigger .e-input-group-icon.e-date-icon:focus,
.e-control.e-control-wrapper.e-bigger .e-input-group-icon.e-date-icon:focus,
.e-control.e-bigger .e-control-wrapper .e-input-group-icon.e-date-icon:focus {
  background: #414040;
  border-radius: 0;
}

.e-small .e-input-group-icon.e-date-icon,
.e-control.e-control-wrapper.e-small .e-input-group-icon.e-date-icon,
.e-control.e-small .e-control-wrapper .e-input-group-icon.e-date-icon {
  font-size: 14px;
}

.e-small.e-bigger .e-input-group-icon.e-date-icon,
.e-control.e-control-wrapper.e-small.e-bigger .e-input-group-icon.e-date-icon,
.e-control.e-small.e-bigger .e-control-wrapper .e-input-group-icon.e-date-icon {
  font-size: 18px;
}

.e-input-group.e-control-wrapper.e-date-wrapper.e-non-edit.e-input-focus .e-input:focus ~ .e-clear-icon, .e-float-input.e-control-wrapper.e-input-group.e-date-wrapper.e-non-edit.e-input-focus input:focus ~ .e-clear-icon {
  display: -ms-flexbox;
  display: flex;
}

.e-control.e-datepicker .e-calendar .e-content table tbody tr.e-month-hide:last-child {
  display: table-row;
}

.e-control.e-datepicker.e-popup-wrapper {
  border-radius: 0;
  overflow-y: hidden;
  pointer-events: auto;
}

.e-control.e-datepicker.e-date-modal {
  background-color: rgba(248, 248, 248, 0.6);
  height: 100%;
  left: 0;
  opacity: .5;
  pointer-events: auto;
  position: fixed;
  top: 0;
  width: 100%;
  z-index: 999;
}

.e-control.e-datepicker .e-model-header {
  background-color: #0074cc;
  color: #201f1f;
  cursor: default;
  display: none;
  padding: 10px 10px 10px 15px;
}

.e-control.e-datepicker .e-model-header .e-model-year {
  font-size: 14px;
  font-weight: 500;
  line-height: 32px;
  margin: 0;
}

.e-control.e-datepicker .e-model-month, .e-control.e-datepicker .e-model-day {
  font-size: 20px;
  font-weight: 500;
  line-height: 32px;
  margin: 0;
}

.e-date-overflow {
  overflow: hidden !important;
}

.e-datepick-mob-popup-wrap {
  -ms-flex-align: center;
      align-items: center;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-direction: column;
      flex-direction: column;
  height: 100%;
  -ms-flex-pack: center;
      justify-content: center;
  left: 0;
  max-height: 100%;
  position: fixed;
  top: 0;
  width: 100%;
  z-index: 1002;
}

.e-datepick-mob-popup-wrap .e-datepicker.e-popup-wrapper.e-lib.e-popup.e-control.e-popup-open {
  position: relative;
  top: 0 !important;
  left: 0 !important;
}

.e-content-placeholder.e-datepicker.e-placeholder-datepicker {
  background-size: 250px 33px;
  min-height: 33px;
}

.e-bigger .e-content-placeholder.e-datepicker.e-placeholder-datepicker, .e-bigger.e-content-placeholder.e-datepicker.e-placeholder-datepicker {
  background-size: 250px 40px;
  min-height: 40px;
}

@media screen and (orientation: landscape) and (max-device-height: 360px) {
  .e-datepicker .e-calendar .e-month table tbody {
    display: inline-block;
    height: 150px;
    overflow: auto;
  }
}

.e-control.e-datepicker.e-popup-wrapper, .e-bigger.e-small .e-control.e-datepicker.e-popup-wrapper {
  border: 1px solid #414040;
  box-shadow: none;
}

.e-control.e-datepicker .e-calendar, .e-bigger.e-small .e-control.e-datepicker .e-calendar {
  background-color: #282727;
  border: none;
}

.e-control.e-input-group.e-date-wrapper.e-dateinput-active:active:not(.e-success):not(.e-warning):not(.e-error) {
  border: 1px solid #0074cc;
}

.e-date-wrapper span.e-input-group-icon.e-date-icon.e-icons.e-active {
  color: none;
}

.e-date-wrapper span.e-input-group-icon.e-date-icon.e-icons.e-active {
  color: none;
}

.e-control .e-js [class^='e-'], .e-control .e-js [class*=' e-'] {
  box-sizing: content-box;
}
