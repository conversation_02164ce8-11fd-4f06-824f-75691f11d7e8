.body {
    font-family: "Roboto", "Segoe UI", "GeezaPro", "DejaVu Serif", "sans-serif";
}

.e-view {
    bottom: 0;
    left: 0;
    overflow: hidden;
    position: absolute;
    right: 0;
    top: 0;
}

.sb-left {
    float: left;
}

.sb-right {
    float: right;
}

.sb-block {
    display: block;
}

.sb-table {
    display: table;
}

.sb-table-cell {
    display: table-cell;
    vertical-align: middle;
}

/*sample header*/


.sb-header {
    height: 48px;
    background: #FFFFFF;
    box-shadow: 0 2px 4px 0 rgba(0,0,0,0.12);
    z-index: 1001;
    opacity: 100;
}


.sb-header-left, .sb-header-right {
    height: 100%;
}

#sb-header-text {
    padding-left: 22px;
    opacity: 0.87;
    font-family: Roboto;
    font-weight: 500;
    font-size: 15px;
    color: #000000;
    text-align: left;
}

.header-logo {
    float: left;
    padding-left: 8px;
    padding-right: 22px;
}

.footer-logo {
    background: url(https://ej2.syncfusion.com/home/<USER>/footer-logo.svg) no-repeat right;
    height: 40px;
    width: 140px;
}

.sb-header-splitter {
    float: left;
    border-left: 1px solid rgb(196, 196, 196);
    height: 32px;
    margin-top: 8px;
    padding-left: 22 px;
}

.sb-header-settings {
    display: none;
}

.product-style a{
   padding-right: 15px;
   font-family:Roboto;
   font-weight: 500;
   font-size: 13px;
   color: #363636;
   text-align: center;
   text-decoration: none;
}
.product-style a:hover {
    color:#3C78EF;
}

.sb-download-btn a {
    text-decoration: none;
}

.sb-download-text {
    color: #FFFFFF;
}

.sb-download-wrapper {
    padding-right: 27px;
}

.sb-download-btn:hover .sb-download-btn:focus .sb-download-btn.active {
    border-radius: 2px;
    font-family: "Roboto";
    font-weight: bold;
    color: #FFFFFF;
}

.sb-download-btn:hover {
    background: #006CE6;
}

.sb-download-btn:focus {
    background: #0051CB;
}

.sb-download-btn.active {
    background: #0036B1;
}

.sb-download-btn {
    background-color: #3C78EF;
    opacity: 100;
    border-radius: 2px;
    font-family: "Roboto", "Segoe UI", "GeezaPro", "DejaVu Serif", "sans-serif";
    font-weight: 500;
    line-height: 18px;
    height: 32px;
    border-color: transparent;
}

.sb-bread-crumb h1 {
    padding-left: 20px;
    padding-top: 24px;
    padding-bottom: 10px;
    margin: 0;
}

.category-allcontrols a, .category-text a, .crumb-sample {
    display: table;
    opacity: 0.87;
    font-family: Roboto;
    font-weight: 500;
    font-size: 15px;
    color: #3C78EF;
    text-align: left;
    text-decoration: none;
}

.sb-bread-crumb .sb-bread-crumb-text>div {
    font-size: 15px;
    font-weight: 500;
    padding-right: 8px;
    cursor: default;
    display: table-cell;
    padding-top: 5px;
    height: 100%;
}

.sb-bread-crumb-text>div.seperator {
    font-weight: 700;
    font-size: 15px;
    vertical-align: bottom;
    /* padding-top: 5px; */
} 

.content {
overflow: auto;
height: calc(100% - 50px);
top: 50px;
}


.sample-content {
    top: 48px;
    background: #FFFFFF;
}

.div {
  display: block;
}

.crumb-sample {
    color: #000000;
    line-height: 18px;
}

.control-section {
    padding-left: 4px;
}

#description {
    padding-bottom: 16px;
    padding-left: 20px;
    opacity: 0.75;
    font-family: Roboto;
    font-size: 13px;
    color: #000000;
    text-align: left;
    padding-top: 14px;
}

#action-description {
    padding-left: 20px;
    padding-bottom: 15px;
    opacity: 0.75;
    font-family: Roboto;
    font-size: 13px;
    color: #000000;
    text-align: left;
}

#description-section {
    padding-left: 20px;
    padding-top: 30px;
    opacity: 0.87;
    font-family:Roboto;
    font-weight: 700;
    font-size: 15px;
    color: #000000;
    text-align: left;
}
.layout {
    display:inline-block;
    cursor: pointer;
}
#actionDes {
    opacity: 0.75;
}
/*sb-icons declaration*/

@font-face {
    font-family: 'sbicons';
    src: url(data:font/truetype;charset=utf-8;base64,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) format('truetype');
    font-weight: normal;
    font-style: normal;
}

.sb-icons {
    font-family: 'sbicons';
    speak: none;
    font-style: normal;
    font-weight: normal;
    font-variant: normal;
    text-transform: none;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

.sb-icon-Next:before {
    content: '\e91b';
}

.sb-icon-Previous:before {
    content: '\e91f';
}

#next-sample,
#prev-sample {
    font-size: 18px;
    border: 0.5px solid#E5E5E5;
    background: #FFFFFF;
    display: inline-table;
    padding: 2px 5px 0px 5px;
}

.sb-navigation-next a, .sb-navigation-prev a{
    color: #636363;
    text-decoration: none;
}

#prev-sample {
    border-radius: 4px 0px 0px 4px;
}

#next-sample {
    border-radius: 0px 4px 4px 0px;
    float: right;
}

#next-sample:hover,
#prev-sample:hover{
background-color: #D8D8D8;
text-decoration: none; 
}

.sb-sample-navigation {
    font-size: 18px;
    color: #5a5a5a;
    cursor: pointer;
}

.sb-custom-item {
    padding-right: 15px;
    padding-left: 30px;
    display: inline-table;
    padding-top: 20px;
}

a.e-disabled {
    cursor: not-allowed;
    opacity: 0.35;
}
@media (max-width: 550px) {
        
    .sb-headers {
        height: 48px;
        background: #FFFFFF;
        box-shadow: 0 2px 4px 0 rgba(0,0,0,0.12);
        z-index: 1001;
        opacity: 100;
    }
    .sb-header {
        display: none;
    }
    .syncfusion-logo {
        float: left;
        padding-left: 8px;
        padding-right: 8px;
    }
    .sync-logo {
        padding-right: 0px;
        padding-left: 8px;
        background: url(../../styles/images/SyncfusionLogo.svg) no-repeat right;
        height: 40px;
        width: 40px;
    }
    .sb-icon-notification {
        padding-left: 15px;
        padding-right: 15px;
        font-size: 18px;
        transform: rotate(-180deg);
        color: #5D5D5D;
        opacity: 100%;
    }
    #sb-header-text {
        padding-left: 0%;
    }

    .sb-icon-notification:before {
        content: "\ea08";
    }
    .product a:hover{
        color: #5D5D5D;
        text-decoration: none;
    }
}

