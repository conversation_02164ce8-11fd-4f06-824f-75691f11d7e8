/*!
 jQuery UI integration for DataTables' Editor
 ©2015 SpryMedia Ltd - datatables.net/license
*/
(function(b){"function"===typeof define&&define.amd?define(["jquery","datatables.net-jqui","datatables.net-editor"],function(c){return b(c,window,document)}):"object"===typeof exports?module.exports=function(c,a){c||(c=window);if(!a||!a.fn.dataTable)a=require("datatables.net-jqui")(c,a).$;a.fn.dataTable.Editor||require("datatables.net-editor")(c,a);return b(a,c,c.document)}:b(jQuery,window,document)})(function(b){var c=b.fn.dataTable,a=c.Editor,e=!1;a.defaults.display="jqueryui";b.extend(!0,b.fn.dataTable.Editor.classes,
{form:{button:"btn ui-button ui-widget ui-state-default ui-corner-all ui-button-text-only"}});a.display.jqueryui=b.extend(!0,{},a.models.displayController,{init:function(d){d.__dialouge=b("<div/>").css("display","none").appendTo("body").dialog(b.extend(true,a.display.jqueryui.modalOptions,{autoOpen:false,buttons:{A:function(){}},closeOnEscape:false}));b(d.__dialouge).on("dialogclose",function(){e||d.close()});return a.display.jqueryui},open:function(d,a,c){d.__dialouge.append(a).dialog("open");b(d.dom.formError).appendTo(d.__dialouge.parent().find("div.ui-dialog-buttonpane"));
d.__dialouge.parent().find(".ui-dialog-title").html(d.dom.header.innerHTML);d.__dialouge.parent().addClass("DTED");a=b(d.dom.buttons).children().addClass("ui-button ui-widget ui-state-default ui-corner-all ui-button-text-only").each(function(){b(this).wrapInner('<span class="ui-button-text" />')});d.__dialouge.parent().find("div.ui-dialog-buttonset").empty().append(a.parent());c&&c()},close:function(a,b){if(a.__dialouge){e=true;a.__dialouge.dialog("close");e=false}b&&b()},node:function(a){return a.__dialouge[0]},
captureFocus:!1});a.display.jqueryui.modalOptions={width:600,modal:!0};return c.Editor});
