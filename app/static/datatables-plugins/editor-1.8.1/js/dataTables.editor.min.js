/*!
 DataTables Editor v1.8.1

 ©2012-2018 SpryMedia Ltd, all rights reserved.
 License: editor.datatables.net/license
*/
var $jscomp=$jscomp||{};$jscomp.scope={};$jscomp.findInternal=function(d,r,q){d instanceof String&&(d=String(d));for(var l=d.length,w=0;w<l;w++){var D=d[w];if(r.call(q,D,w,d))return{i:w,v:D}}return{i:-1,v:void 0}};$jscomp.ASSUME_ES5=!1;$jscomp.ASSUME_NO_NATIVE_MAP=!1;$jscomp.ASSUME_NO_NATIVE_SET=!1;$jscomp.defineProperty=$jscomp.ASSUME_ES5||"function"==typeof Object.defineProperties?Object.defineProperty:function(d,r,q){d!=Array.prototype&&d!=Object.prototype&&(d[r]=q.value)};
$jscomp.getGlobal=function(d){return"undefined"!=typeof window&&window===d?d:"undefined"!=typeof global&&null!=global?global:d};$jscomp.global=$jscomp.getGlobal(this);$jscomp.polyfill=function(d,r,q,l){if(r){q=$jscomp.global;d=d.split(".");for(l=0;l<d.length-1;l++){var w=d[l];w in q||(q[w]={});q=q[w]}d=d[d.length-1];l=q[d];r=r(l);r!=l&&null!=r&&$jscomp.defineProperty(q,d,{configurable:!0,writable:!0,value:r})}};
$jscomp.polyfill("Array.prototype.find",function(d){return d?d:function(d,q){return $jscomp.findInternal(this,d,q).v}},"es6","es3");
(function(d){"function"===typeof define&&define.amd?define(["jquery","datatables.net"],function(r){return d(r,window,document)}):"object"===typeof exports?module.exports=function(r,q){r||(r=window);q&&q.fn.dataTable||(q=require("datatables.net")(r,q).$);return d(q,r,r.document)}:d(jQuery,window,document)})(function(d,r,q,l){function w(a){a=a.context[0];return a.oInit.editor||a._editor}function D(a,b,c,d){b||(b={});b.buttons===l&&(b.buttons="_basic");b.title===l&&(b.title=a.i18n[c].title);b.message===
l&&("remove"===c?(a=a.i18n[c].confirm,b.message=1!==d?a._.replace(/%d/,d):a["1"]):b.message="");return b}var v=d.fn.dataTable;if(!v||!v.versionCheck||!v.versionCheck("1.10.7"))throw"Editor requires DataTables 1.10.7 or newer";var h=function(a){this instanceof h||alert("DataTables Editor must be initialised as a 'new' instance'");this._constructor(a)};v.Editor=h;d.fn.DataTable.Editor=h;var x=function(a,b){b===l&&(b=q);return d('*[data-dte-e="'+a+'"]',b)},L=0,E=function(a,b){var c=[];d.each(a,function(a,
e){c.push(e[b])});return c},I=function(a,b){var c=this.files(a);if(!c[b])throw"Unknown file id "+b+" in table "+a;return c[b]},J=function(a){if(!a)return h.files;var b=h.files[a];if(!b)throw"Unknown file table name: "+a;return b},K=function(a){var b=[],c;for(c in a)a.hasOwnProperty(c)&&b.push(c);return b},G=function(a,b){if("object"!==typeof a||"object"!==typeof b)return a==b;var c=K(a),d=K(b);if(c.length!==d.length)return!1;d=0;for(var e=c.length;d<e;d++){var g=c[d];if("object"===typeof a[g]){if(!G(a[g],
b[g]))return!1}else if(a[g]!=b[g])return!1}return!0};h.Field=function(a,b,c){var f=this,e=c.i18n.multi;a=d.extend(!0,{},h.Field.defaults,a);if(!h.fieldTypes[a.type])throw"Error adding field - unknown field type "+a.type;this.s=d.extend({},h.Field.settings,{type:h.fieldTypes[a.type],name:a.name,classes:b,host:c,opts:a,multiValue:!1});a.id||(a.id="DTE_Field_"+a.name);a.dataProp&&(a.data=a.dataProp);""===a.data&&(a.data=a.name);var g=v.ext.oApi;this.valFromData=function(b){return g._fnGetObjectDataFn(a.data)(b,
"editor")};this.valToData=g._fnSetObjectDataFn(a.data);var k=d('<div class="'+b.wrapper+" "+b.typePrefix+a.type+" "+b.namePrefix+a.name+" "+a.className+'"><label data-dte-e="label" class="'+b.label+'" for="'+h.safeId(a.id)+'">'+a.label+'<div data-dte-e="msg-label" class="'+b["msg-label"]+'">'+a.labelInfo+'</div></label><div data-dte-e="input" class="'+b.input+'"><div data-dte-e="input-control" class="'+b.inputControl+'"/><div data-dte-e="multi-value" class="'+b.multiValue+'">'+e.title+'<span data-dte-e="multi-info" class="'+
b.multiInfo+'">'+e.info+'</span></div><div data-dte-e="msg-multi" class="'+b.multiRestore+'">'+e.restore+'</div><div data-dte-e="msg-error" class="'+b["msg-error"]+'"></div><div data-dte-e="msg-message" class="'+b["msg-message"]+'">'+a.message+'</div><div data-dte-e="msg-info" class="'+b["msg-info"]+'">'+a.fieldInfo+'</div></div><div data-dte-e="field-processing" class="'+b.processing+'"><span/></div></div>');c=this._typeFn("create",a);null!==c?x("input-control",k).prepend(c):k.css("display","none");
this.dom=d.extend(!0,{},h.Field.models.dom,{container:k,inputControl:x("input-control",k),label:x("label",k),fieldInfo:x("msg-info",k),labelInfo:x("msg-label",k),fieldError:x("msg-error",k),fieldMessage:x("msg-message",k),multi:x("multi-value",k),multiReturn:x("msg-multi",k),multiInfo:x("multi-info",k),processing:x("field-processing",k)});this.dom.multi.on("click",function(){f.s.opts.multiEditable&&!k.hasClass(b.disabled)&&"readonly"!==a.type&&(f.val(""),f.focus())});this.dom.multiReturn.on("click",
function(){f.multiRestore()});d.each(this.s.type,function(a,g){"function"===typeof g&&f[a]===l&&(f[a]=function(){var g=Array.prototype.slice.call(arguments);g.unshift(a);g=f._typeFn.apply(f,g);return g===l?f:g})})};h.Field.prototype={def:function(a){var b=this.s.opts;if(a===l)return a=b["default"]!==l?b["default"]:b.def,"function"===typeof a?a():a;b.def=a;return this},disable:function(){this.dom.container.addClass(this.s.classes.disabled);this._typeFn("disable");return this},displayed:function(){var a=
this.dom.container;return a.parents("body").length&&"none"!=a.css("display")?!0:!1},enable:function(){this.dom.container.removeClass(this.s.classes.disabled);this._typeFn("enable");return this},enabled:function(){return!1===this.dom.container.hasClass(this.s.classes.disabled)},error:function(a,b){var c=this.s.classes;a?this.dom.container.addClass(c.error):this.dom.container.removeClass(c.error);this._typeFn("errorMessage",a);return this._msg(this.dom.fieldError,a,b)},fieldInfo:function(a){return this._msg(this.dom.fieldInfo,
a)},isMultiValue:function(){return this.s.multiValue&&1!==this.s.multiIds.length},inError:function(){return this.dom.container.hasClass(this.s.classes.error)},input:function(){return this.s.type.input?this._typeFn("input"):d("input, select, textarea",this.dom.container)},focus:function(){this.s.type.focus?this._typeFn("focus"):d("input, select, textarea",this.dom.container).focus();return this},get:function(){if(this.isMultiValue())return l;var a=this._typeFn("get");return a!==l?a:this.def()},hide:function(a){var b=
this.dom.container;a===l&&(a=!0);this.s.host.display()&&a&&d.fn.slideUp?b.slideUp():b.css("display","none");return this},label:function(a){var b=this.dom.label,c=this.dom.labelInfo.detach();if(a===l)return b.html();b.html(a);b.append(c);return this},labelInfo:function(a){return this._msg(this.dom.labelInfo,a)},message:function(a,b){return this._msg(this.dom.fieldMessage,a,b)},multiGet:function(a){var b=this.s.multiValues,c=this.s.multiIds;if(a===l){a={};for(var d=0;d<c.length;d++)a[c[d]]=this.isMultiValue()?
b[c[d]]:this.val()}else a=this.isMultiValue()?b[a]:this.val();return a},multiRestore:function(){this.s.multiValue=!0;this._multiValueCheck()},multiSet:function(a,b){var c=this.s.multiValues,f=this.s.multiIds;b===l&&(b=a,a=l);var e=function(a,b){-1===d.inArray(f)&&f.push(a);c[a]=b};d.isPlainObject(b)&&a===l?d.each(b,function(a,b){e(a,b)}):a===l?d.each(f,function(a,c){e(c,b)}):e(a,b);this.s.multiValue=!0;this._multiValueCheck();return this},name:function(){return this.s.opts.name},node:function(){return this.dom.container[0]},
processing:function(a){this.dom.processing.css("display",a?"block":"none");return this},set:function(a,b){var c=function(a){return"string"!==typeof a?a:a.replace(/&gt;/g,">").replace(/&lt;/g,"<").replace(/&amp;/g,"&").replace(/&quot;/g,'"').replace(/&#39;/g,"'").replace(/&#10;/g,"\n")};this.s.multiValue=!1;var f=this.s.opts.entityDecode;if(f===l||!0===f)if(d.isArray(a)){f=0;for(var e=a.length;f<e;f++)a[f]=c(a[f])}else a=c(a);this._typeFn("set",a);b!==l&&!0!==b||this._multiValueCheck();return this},
show:function(a){var b=this.dom.container;a===l&&(a=!0);this.s.host.display()&&a&&d.fn.slideDown?b.slideDown():b.css("display","");return this},val:function(a){return a===l?this.get():this.set(a)},compare:function(a,b){return(this.s.opts.compare||G)(a,b)},dataSrc:function(){return this.s.opts.data},destroy:function(){this.dom.container.remove();this._typeFn("destroy");return this},multiEditable:function(){return this.s.opts.multiEditable},multiIds:function(){return this.s.multiIds},multiInfoShown:function(a){this.dom.multiInfo.css({display:a?
"block":"none"})},multiReset:function(){this.s.multiIds=[];this.s.multiValues={}},submittable:function(){return this.s.opts.submit},valFromData:null,valToData:null,_errorNode:function(){return this.dom.fieldError},_msg:function(a,b,c){if(b===l)return a.html();if("function"===typeof b){var f=this.s.host;b=b(f,new v.Api(f.s.table))}a.parent().is(":visible")&&d.fn.animate?(a.html(b),b?a.slideDown(c):a.slideUp(c)):(a.html(b||"").css("display",b?"block":"none"),c&&c());return this},_multiValueCheck:function(){var a=
this.s.multiIds,b=this.s.multiValues,c=this.s.multiValue,d=this.s.opts.multiEditable,e=!1;if(a)for(var g=0;g<a.length;g++){var k=b[a[g]];if(0<g&&!G(k,h)){e=!0;break}var h=k}e&&c||!d&&this.isMultiValue()?(this.dom.inputControl.css({display:"none"}),this.dom.multi.css({display:"block"})):(this.dom.inputControl.css({display:"block"}),this.dom.multi.css({display:"none"}),c&&!e&&this.set(h,!1));this.dom.multiReturn.css({display:a&&1<a.length&&e&&!c?"block":"none"});h=this.s.host.i18n.multi;this.dom.multiInfo.html(d?
h.info:h.noMulti);this.dom.multi.toggleClass(this.s.classes.multiNoEdit,!d);this.s.host._multiInfo();return!0},_typeFn:function(a){var b=Array.prototype.slice.call(arguments);b.shift();b.unshift(this.s.opts);var c=this.s.type[a];if(c)return c.apply(this.s.host,b)}};h.Field.models={};h.Field.defaults={className:"",data:"",def:"",fieldInfo:"",id:"",label:"",labelInfo:"",name:null,type:"text",message:"",multiEditable:!0,submit:!0};h.Field.models.settings={type:null,name:null,classes:null,opts:null,host:null};
h.Field.models.dom={container:null,label:null,labelInfo:null,fieldInfo:null,fieldError:null,fieldMessage:null};h.models={};h.models.displayController={init:function(a){},open:function(a,b,c){},close:function(a,b){}};h.models.fieldType={create:function(a){},get:function(a){},set:function(a,b){},enable:function(a){},disable:function(a){}};h.models.settings={ajaxUrl:null,ajax:null,dataSource:null,domTable:null,opts:null,displayController:null,fields:{},order:[],id:-1,displayed:!1,processing:!1,modifier:null,
action:null,idSrc:null,unique:0};h.models.button={label:null,fn:null,className:null};h.models.formOptions={onReturn:"submit",onBlur:"close",onBackground:"blur",onComplete:"close",onEsc:"close",onFieldError:"focus",submit:"all",focus:0,buttons:!0,title:!0,message:!0,drawType:!1,scope:"row"};h.display={};(function(a,b,c,d){h.display.lightbox=c.extend(!0,{},h.models.displayController,{init:function(a){e._init();return e},open:function(a,b,c){e._shown?c&&c():(e._dte=a,a=e._dom.content,a.children().detach(),
a.append(b).append(e._dom.close),e._shown=!0,e._show(c))},close:function(a,b){e._shown?(e._dte=a,e._hide(b),e._shown=!1):b&&b()},node:function(a){return e._dom.wrapper[0]},_init:function(){if(!e._ready){var a=e._dom;a.content=c("div.DTED_Lightbox_Content",e._dom.wrapper);a.wrapper.css("opacity",0);a.background.css("opacity",0)}},_show:function(b){var g=e._dom;a.orientation!==l&&c("body").addClass("DTED_Lightbox_Mobile");g.content.css("height","auto");g.wrapper.css({top:-e.conf.offsetAni});c("body").append(e._dom.background).append(e._dom.wrapper);
e._heightCalc();e._dte._animate(g.wrapper,{opacity:1,top:0},b);e._dte._animate(g.background,{opacity:1});setTimeout(function(){c("div.DTE_Footer").css("text-indent",-1)},10);g.close.bind("click.DTED_Lightbox",function(a){e._dte.close()});g.background.bind("click.DTED_Lightbox",function(a){e._dte.background()});c("div.DTED_Lightbox_Content_Wrapper",g.wrapper).bind("click.DTED_Lightbox",function(a){c(a.target).hasClass("DTED_Lightbox_Content_Wrapper")&&e._dte.background()});c(a).bind("resize.DTED_Lightbox",
function(){e._heightCalc()});e._scrollTop=c("body").scrollTop();a.orientation!==l&&(b=c("body").children().not(g.background).not(g.wrapper),c("body").append('<div class="DTED_Lightbox_Shown"/>'),c("div.DTED_Lightbox_Shown").append(b))},_heightCalc:function(){var b=e._dom,d=c(a).height()-2*e.conf.windowPadding-c("div.DTE_Header",b.wrapper).outerHeight()-c("div.DTE_Footer",b.wrapper).outerHeight();c("div.DTE_Body_Content",b.wrapper).css("maxHeight",d)},_hide:function(b){var g=e._dom;b||(b=function(){});
if(a.orientation!==l){var d=c("div.DTED_Lightbox_Shown");d.children().appendTo("body");d.remove()}c("body").removeClass("DTED_Lightbox_Mobile").scrollTop(e._scrollTop);e._dte._animate(g.wrapper,{opacity:0,top:e.conf.offsetAni},function(){c(this).detach();b()});e._dte._animate(g.background,{opacity:0},function(){c(this).detach()});g.close.unbind("click.DTED_Lightbox");g.background.unbind("click.DTED_Lightbox");c("div.DTED_Lightbox_Content_Wrapper",g.wrapper).unbind("click.DTED_Lightbox");c(a).unbind("resize.DTED_Lightbox")},
_dte:null,_ready:!1,_shown:!1,_dom:{wrapper:c('<div class="DTED DTED_Lightbox_Wrapper"><div class="DTED_Lightbox_Container"><div class="DTED_Lightbox_Content_Wrapper"><div class="DTED_Lightbox_Content"></div></div></div></div>'),background:c('<div class="DTED_Lightbox_Background"><div/></div>'),close:c('<div class="DTED_Lightbox_Close"></div>'),content:null}});var e=h.display.lightbox;e.conf={offsetAni:25,windowPadding:25}})(r,q,jQuery,jQuery.fn.dataTable);(function(a,b,c,d){h.display.envelope=c.extend(!0,
{},h.models.displayController,{init:function(a){e._dte=a;e._init();return e},open:function(a,b,d){e._dte=a;c(e._dom.content).children().detach();e._dom.content.appendChild(b);e._dom.content.appendChild(e._dom.close);e._show(d)},close:function(a,b){e._dte=a;e._hide(b)},node:function(a){return e._dom.wrapper[0]},_init:function(){e._ready||(e._dom.content=c("div.DTED_Envelope_Container",e._dom.wrapper)[0],b.body.appendChild(e._dom.background),b.body.appendChild(e._dom.wrapper),e._dom.background.style.visbility=
"hidden",e._dom.background.style.display="block",e._cssBackgroundOpacity=c(e._dom.background).css("opacity"),e._dom.background.style.display="none",e._dom.background.style.visbility="visible")},_show:function(b){b||(b=function(){});e._dom.content.style.height="auto";var g=e._dom.wrapper.style;g.opacity=0;g.display="block";var d=e._findAttachRow(),f=e._heightCalc(),h=d.offsetWidth;g.display="none";g.opacity=1;e._dom.wrapper.style.width=h+"px";e._dom.wrapper.style.marginLeft=-(h/2)+"px";e._dom.wrapper.style.top=
c(d).offset().top+d.offsetHeight+"px";e._dom.content.style.top=-1*f-20+"px";e._dom.background.style.opacity=0;e._dom.background.style.display="block";c(e._dom.background).animate({opacity:e._cssBackgroundOpacity},"normal");c(e._dom.wrapper).fadeIn();e.conf.windowScroll?c("html,body").animate({scrollTop:c(d).offset().top+d.offsetHeight-e.conf.windowPadding},function(){c(e._dom.content).animate({top:0},600,b)}):c(e._dom.content).animate({top:0},600,b);c(e._dom.close).bind("click.DTED_Envelope",function(a){e._dte.close()});
c(e._dom.background).bind("click.DTED_Envelope",function(a){e._dte.background()});c("div.DTED_Lightbox_Content_Wrapper",e._dom.wrapper).bind("click.DTED_Envelope",function(a){c(a.target).hasClass("DTED_Envelope_Content_Wrapper")&&e._dte.background()});c(a).bind("resize.DTED_Envelope",function(){e._heightCalc()})},_heightCalc:function(){e.conf.heightCalc?e.conf.heightCalc(e._dom.wrapper):c(e._dom.content).children().height();var b=c(a).height()-2*e.conf.windowPadding-c("div.DTE_Header",e._dom.wrapper).outerHeight()-
c("div.DTE_Footer",e._dom.wrapper).outerHeight();c("div.DTE_Body_Content",e._dom.wrapper).css("maxHeight",b);return c(e._dte.dom.wrapper).outerHeight()},_hide:function(b){b||(b=function(){});c(e._dom.content).animate({top:-(e._dom.content.offsetHeight+50)},600,function(){c([e._dom.wrapper,e._dom.background]).fadeOut("normal",b)});c(e._dom.close).unbind("click.DTED_Lightbox");c(e._dom.background).unbind("click.DTED_Lightbox");c("div.DTED_Lightbox_Content_Wrapper",e._dom.wrapper).unbind("click.DTED_Lightbox");
c(a).unbind("resize.DTED_Lightbox")},_findAttachRow:function(){var a=c(e._dte.s.table).DataTable();return"head"===e.conf.attach?a.table().header():"create"===e._dte.s.action?a.table().header():a.row(e._dte.s.modifier).node()},_dte:null,_ready:!1,_cssBackgroundOpacity:1,_dom:{wrapper:c('<div class="DTED DTED_Envelope_Wrapper"><div class="DTED_Envelope_Shadow"></div><div class="DTED_Envelope_Container"></div></div>')[0],background:c('<div class="DTED_Envelope_Background"><div/></div>')[0],close:c('<div class="DTED_Envelope_Close">&times;</div>')[0],
content:null}});var e=h.display.envelope;e.conf={windowPadding:50,heightCalc:null,attach:"row",windowScroll:!0}})(r,q,jQuery,jQuery.fn.dataTable);h.prototype.add=function(a,b){if(d.isArray(a)){var c=0;for(b=a.length;c<b;c++)this.add(a[c])}else{c=a.name;if(c===l)throw"Error adding field. The field requires a `name` option";if(this.s.fields[c])throw"Error adding field '"+c+"'. A field already exists with this name";this._dataSource("initField",a);var f=new h.Field(a,this.classes.field,this);this.s.mode&&
(a=this.s.editFields,f.multiReset(),d.each(a,function(a,b){var c;b.data&&(c=f.valFromData(b.data));f.multiSet(a,c!==l?c:f.def())}));this.s.fields[c]=f;b===l?this.s.order.push(c):null===b?this.s.order.unshift(c):(a=d.inArray(b,this.s.order),this.s.order.splice(a+1,0,c))}this._displayReorder(this.order());return this};h.prototype.ajax=function(a){return a?(this.s.ajax=a,this):this.s.ajax};h.prototype.background=function(){var a=this.s.editOpts.onBackground;"function"===typeof a?a(this):"blur"===a?this.blur():
"close"===a?this.close():"submit"===a&&this.submit();return this};h.prototype.blur=function(){this._blur();return this};h.prototype.bubble=function(a,b,c,f){var e=this;if(this._tidy(function(){e.bubble(a,b,f)}))return this;d.isPlainObject(b)?(f=b,b=l,c=!0):"boolean"===typeof b&&(c=b,f=b=l);d.isPlainObject(c)&&(f=c,c=!0);c===l&&(c=!0);f=d.extend({},this.s.formOptions.bubble,f);var g=this._dataSource("individual",a,b);this._edit(a,g,"bubble",f,function(){var a=e._formOptions(f);if(!e._preopen("bubble"))return e;
d(r).on("resize."+a,function(){e.bubblePosition()});var b=[];e.s.bubbleNodes=b.concat.apply(b,E(g,"attach"));var h=e.classes.bubble;b=d('<div class="'+h.bg+'"><div/></div>');h=d('<div class="'+h.wrapper+'"><div class="'+h.liner+'"><div class="'+h.table+'"><div class="'+h.close+'" /><div class="DTE_Processing_Indicator"><span></div></div></div><div class="'+h.pointer+'" /></div>');c&&(h.appendTo("body"),b.appendTo("body"));var p=h.children().eq(0),n=p.children(),t=n.children();p.append(e.dom.formError);
n.prepend(e.dom.form);f.message&&p.prepend(e.dom.formInfo);f.title&&p.prepend(e.dom.header);f.buttons&&n.append(e.dom.buttons);var u=d().add(h).add(b);e._closeReg(function(b){e._animate(u,{opacity:0},function(){u.detach();d(r).off("resize."+a);e._clearDynamicInfo()})});b.click(function(){e.blur()});t.click(function(){e._close()});e.bubblePosition();e._animate(u,{opacity:1});e._focus(e.s.includeFields,f.focus);e._postopen("bubble")});return this};h.prototype.bubblePosition=function(){var a=d("div.DTE_Bubble"),
b=d("div.DTE_Bubble_Liner"),c=this.s.bubbleNodes,f=0,e=0,g=0,k=0;d.each(c,function(a,b){a=d(b).offset();b=d(b).get(0);f+=a.top;e+=a.left;g+=a.left+b.offsetWidth;k+=a.top+b.offsetHeight});f/=c.length;e/=c.length;g/=c.length;k/=c.length;c=f;var h=(e+g)/2,m=b.outerWidth(),p=h-m/2;m=p+m;var n=d(r).width();a.css({top:c,left:h});b.length&&0>b.offset().top?a.css("top",k).addClass("below"):a.removeClass("below");m+15>n?b.css("left",15>p?-(p-15):-(m-n+15)):b.css("left",15>p?-(p-15):0);return this};h.prototype.buttons=
function(a){var b=this;"_basic"===a?a=[{text:this.i18n[this.s.action].submit,action:function(){this.submit()}}]:d.isArray(a)||(a=[a]);d(this.dom.buttons).empty();d.each(a,function(a,f){"string"===typeof f&&(f={text:f,action:function(){this.submit()}});a=f.text||f.label;var c=f.action||f.fn;d("<button/>",{"class":b.classes.form.button+(f.className?" "+f.className:"")}).html("function"===typeof a?a(b):a||"").attr("tabindex",f.tabIndex!==l?f.tabIndex:0).on("keyup",function(a){13===a.keyCode&&c&&c.call(b)}).on("keypress",
function(a){13===a.keyCode&&a.preventDefault()}).on("click",function(a){a.preventDefault();c&&c.call(b)}).appendTo(b.dom.buttons)});return this};h.prototype.clear=function(a){var b=this,c=this.s.fields;"string"===typeof a?(b.field(a).destroy(),delete c[a],c=d.inArray(a,this.s.order),this.s.order.splice(c,1),a=d.inArray(a,this.s.includeFields),-1!==a&&this.s.includeFields.splice(a,1)):d.each(this._fieldNames(a),function(a,c){b.clear(c)});return this};h.prototype.close=function(){this._close(!1);return this};
h.prototype.create=function(a,b,c,f){var e=this,g=this.s.fields,k=1;if(this._tidy(function(){e.create(a,b,c,f)}))return this;"number"===typeof a&&(k=a,a=b,b=c);this.s.editFields={};for(var h=0;h<k;h++)this.s.editFields[h]={fields:this.s.fields};var m=this._crudArgs(a,b,c,f);this.s.mode="main";this.s.action="create";this.s.modifier=null;this.dom.form.style.display="block";this._actionClass();this._displayReorder(this.fields());d.each(g,function(a,b){b.multiReset();for(a=0;a<k;a++)b.multiSet(a,b.def());
b.set(b.def())});this._event("initCreate",null,function(){e._assembleMain();e._formOptions(m.opts);m.maybeOpen()});return this};h.prototype.dependent=function(a,b,c){if(d.isArray(a)){for(var f=0,e=a.length;f<e;f++)this.dependent(a[f],b,c);return this}var g=this,k=this.field(a),h={type:"POST",dataType:"json"};c=d.extend({event:"change",data:null,preUpdate:null,postUpdate:null},c);var m=function(a){c.preUpdate&&c.preUpdate(a);d.each({labels:"label",options:"update",values:"val",messages:"message",errors:"error"},
function(b,c){a[b]&&d.each(a[b],function(a,b){g.field(a)[c](b)})});d.each(["hide","show","enable","disable"],function(b,c){if(a[c])g[c](a[c])});c.postUpdate&&c.postUpdate(a);k.processing(!1)};d(k.node()).on(c.event,function(a){if(0!==d(k.node()).find(a.target).length){k.processing(!0);a={};a.rows=g.s.editFields?E(g.s.editFields,"data"):null;a.row=a.rows?a.rows[0]:null;a.values=g.val();if(c.data){var e=c.data(a);e&&(c.data=e)}"function"===typeof b?(a=b(k.val(),a,m))&&("object"===typeof a&&"function"===
typeof a.then?a.then(function(a){a&&m(a)}):m(a)):(d.isPlainObject(b)?d.extend(h,b):h.url=b,d.ajax(d.extend(h,{url:b,data:a,success:m})))}});return this};h.prototype.destroy=function(){this.s.displayed&&this.close();this.clear();this.s.template&&d("body").append(this.s.template);var a=this.s.displayController;a.destroy&&a.destroy(this);d(q).off(".dte"+this.s.unique);this.s=this.dom=null};h.prototype.disable=function(a){var b=this;d.each(this._fieldNames(a),function(a,d){b.field(d).disable()});return this};
h.prototype.display=function(a){return a===l?this.s.displayed:this[a?"open":"close"]()};h.prototype.displayed=function(){return d.map(this.s.fields,function(a,b){return a.displayed()?b:null})};h.prototype.displayNode=function(){return this.s.displayController.node(this)};h.prototype.edit=function(a,b,c,d,e){var g=this;if(this._tidy(function(){g.edit(a,b,c,d,e)}))return this;var f=this._crudArgs(b,c,d,e);this._edit(a,this._dataSource("fields",a),"main",f.opts,function(){g._assembleMain();g._formOptions(f.opts);
f.maybeOpen()});return this};h.prototype.enable=function(a){var b=this;d.each(this._fieldNames(a),function(a,d){b.field(d).enable()});return this};h.prototype.error=function(a,b){b===l?(this._message(this.dom.formError,a),this.s.globalError=a):this.field(a).error(b);return this};h.prototype.field=function(a){var b=this.s.fields;if(!b[a])throw"Unknown field name - "+a;return b[a]};h.prototype.fields=function(){return d.map(this.s.fields,function(a,b){return b})};h.prototype.file=I;h.prototype.files=
J;h.prototype.get=function(a){var b=this;a||(a=this.fields());if(d.isArray(a)){var c={};d.each(a,function(a,d){c[d]=b.field(d).get()});return c}return this.field(a).get()};h.prototype.hide=function(a,b){var c=this;d.each(this._fieldNames(a),function(a,d){c.field(d).hide(b)});return this};h.prototype.ids=function(a){return d.map(this.s.editFields,function(b,c){return!0===a?"#"+c:c})};h.prototype.inError=function(a){d(this.dom.formError);if(this.s.globalError)return!0;a=this._fieldNames(a);for(var b=
0,c=a.length;b<c;b++)if(this.field(a[b]).inError())return!0;return!1};h.prototype.inline=function(a,b,c){var f=this;d.isPlainObject(b)&&(c=b,b=l);c=d.extend({},this.s.formOptions.inline,c);var e=this._dataSource("individual",a,b),g,k,h=0,m,p=!1,n=this.classes.inline;d.each(e,function(a,b){if(0<h)throw"Cannot edit more than one row inline at a time";g=d(b.attach[0]);m=0;d.each(b.displayFields,function(a,b){if(0<m)throw"Cannot edit more than one field inline at a time";k=b;m++});h++});if(d("div.DTE_Field",
g).length||this._tidy(function(){f.inline(a,b,c)}))return this;this._edit(a,e,"inline",c,function(){var a=f._formOptions(c);if(!f._preopen("inline"))return f;var b=g.contents().detach();g.append(d('<div class="'+n.wrapper+'"><div class="'+n.liner+'" style="width:'+g.width()+'px"><div class="DTE_Processing_Indicator"><span/></div></div><div class="'+n.buttons+'"/></div>'));g.find("div."+n.liner.replace(/ /g,".")).append(k.node()).append(f.dom.formError);c.buttons&&g.find("div."+n.buttons.replace(/ /g,
".")).append(f.dom.buttons);f._closeReg(function(c){p=!0;d(q).off("click"+a);c||(g.contents().detach(),g.append(b));f._clearDynamicInfo()});setTimeout(function(){if(!p)d(q).on("click"+a,function(a){var b=d.fn.addBack?"addBack":"andSelf";k._typeFn("owns",a.target)||-1!==d.inArray(g[0],d(a.target).parents()[b]())||f.blur()})},0);f._focus([k],c.focus);f._postopen("inline")});return this};h.prototype.message=function(a,b){b===l?this._message(this.dom.formInfo,a):this.field(a).message(b);return this};
h.prototype.mode=function(a){if(!a)return this.s.action;if(!this.s.action)throw"Not currently in an editing mode";this.s.action=a;return this};h.prototype.modifier=function(){return this.s.modifier};h.prototype.multiGet=function(a){var b=this;a===l&&(a=this.fields());if(d.isArray(a)){var c={};d.each(a,function(a,d){c[d]=b.field(d).multiGet()});return c}return this.field(a).multiGet()};h.prototype.multiSet=function(a,b){var c=this;d.isPlainObject(a)&&b===l?d.each(a,function(a,b){c.field(a).multiSet(b)}):
this.field(a).multiSet(b);return this};h.prototype.node=function(a){var b=this;a||(a=this.order());return d.isArray(a)?d.map(a,function(a){return b.field(a).node()}):this.field(a).node()};h.prototype.off=function(a,b){d(this).off(this._eventName(a),b);return this};h.prototype.on=function(a,b){d(this).on(this._eventName(a),b);return this};h.prototype.one=function(a,b){d(this).one(this._eventName(a),b);return this};h.prototype.open=function(){var a=this;this._displayReorder();this._closeReg(function(b){a.s.displayController.close(a,
function(){a._clearDynamicInfo()})});if(!this._preopen("main"))return this;this.s.displayController.open(this,this.dom.wrapper,function(){a._focus(d.map(a.s.order,function(b){return a.s.fields[b]}),a.s.editOpts.focus)});this._postopen("main");return this};h.prototype.order=function(a){if(!a)return this.s.order;arguments.length&&!d.isArray(a)&&(a=Array.prototype.slice.call(arguments));if(this.s.order.slice().sort().join("-")!==a.slice().sort().join("-"))throw"All fields, and no additional fields, must be provided for ordering.";
d.extend(this.s.order,a);this._displayReorder();return this};h.prototype.remove=function(a,b,c,f,e){var g=this;if(this._tidy(function(){g.remove(a,b,c,f,e)}))return this;a.length===l&&(a=[a]);var k=this._crudArgs(b,c,f,e),h=this._dataSource("fields",a);this.s.action="remove";this.s.modifier=a;this.s.editFields=h;this.dom.form.style.display="none";this._actionClass();this._event("initRemove",[E(h,"node"),E(h,"data"),a],function(){g._event("initMultiRemove",[h,a],function(){g._assembleMain();g._formOptions(k.opts);
k.maybeOpen();var a=g.s.editOpts;null!==a.focus&&d("button",g.dom.buttons).eq(a.focus).focus()})});return this};h.prototype.set=function(a,b){var c=this;if(!d.isPlainObject(a)){var f={};f[a]=b;a=f}d.each(a,function(a,b){c.field(a).set(b)});return this};h.prototype.show=function(a,b){var c=this;d.each(this._fieldNames(a),function(a,d){c.field(d).show(b)});return this};h.prototype.submit=function(a,b,c,f){var e=this,g=this.s.fields,k=[],h=0,m=!1;if(this.s.processing||!this.s.action)return this;this._processing(!0);
var l=function(){k.length!==h||m||e._event("initSubmit",[e.s.action],function(g){!1===g?e._processing(!1):(m=!0,e._submit(a,b,c,f))})};this.error();d.each(g,function(a,b){b.inError()&&k.push(a)});d.each(k,function(a,b){g[b].error("",function(){h++;l()})});l();return this};h.prototype.template=function(a){if(a===l)return this.s.template;this.s.template=null===a?null:d(a);return this};h.prototype.title=function(a){var b=d(this.dom.header).children("div."+this.classes.header.content);if(a===l)return b.html();
"function"===typeof a&&(a=a(this,new v.Api(this.s.table)));b.html(a);return this};h.prototype.val=function(a,b){return b!==l||d.isPlainObject(a)?this.set(a,b):this.get(a)};var C=v.Api.register;C("editor()",function(){return w(this)});C("row.create()",function(a){var b=w(this);b.create(D(b,a,"create"));return this});C("row().edit()",function(a){var b=w(this);b.edit(this[0][0],D(b,a,"edit"));return this});C("rows().edit()",function(a){var b=w(this);b.edit(this[0],D(b,a,"edit"));return this});C("row().delete()",
function(a){var b=w(this);b.remove(this[0][0],D(b,a,"remove",1));return this});C("rows().delete()",function(a){var b=w(this);b.remove(this[0],D(b,a,"remove",this[0].length));return this});C("cell().edit()",function(a,b){a?d.isPlainObject(a)&&(b=a,a="inline"):a="inline";w(this)[a](this[0][0],b);return this});C("cells().edit()",function(a){w(this).bubble(this[0],a);return this});C("file()",I);C("files()",J);d(q).on("xhr.dt",function(a,b,c){"dt"===a.namespace&&c&&c.files&&d.each(c.files,function(a,b){h.files[a]=
b})});h.error=function(a,b){throw b?a+" For more information, please refer to https://datatables.net/tn/"+b:a;};h.pairs=function(a,b,c){var f;b=d.extend({label:"label",value:"value"},b);if(d.isArray(a)){var e=0;for(f=a.length;e<f;e++){var g=a[e];d.isPlainObject(g)?c(g[b.value]===l?g[b.label]:g[b.value],g[b.label],e,g.attr):c(g,g,e)}}else e=0,d.each(a,function(a,b){c(b,a,e);e++})};h.safeId=function(a){return a.replace(/\./g,"-")};h.upload=function(a,b,c,f,e){var g=new FileReader,k=0,A=[];a.error(b.name,
"");f(b,b.fileReadText||"<i>Uploading file</i>");g.onload=function(m){var p=new FormData;p.append("action","upload");p.append("uploadField",b.name);p.append("upload",c[k]);b.ajaxData&&b.ajaxData(p);if(b.ajax)var n=b.ajax;else d.isPlainObject(a.s.ajax)?n=a.s.ajax.upload?a.s.ajax.upload:a.s.ajax:"string"===typeof a.s.ajax&&(n=a.s.ajax);if(!n)throw"No Ajax option specified for upload plug-in";"string"===typeof n&&(n={url:n});if("function"===typeof n.data){m={};var t=n.data(m);t!==l&&"string"!==typeof t&&
(m=t);d.each(m,function(a,b){p.append(a,b)})}if(!1===a._event("preUpload",[b.name,c[k],p]))k<c.length-1?(k++,g.readAsDataURL(c[k])):e.call(a,A);else{var u=!1;a.on("preSubmit.DTE_Upload",function(){u=!0;return!1});d.ajax(d.extend({},n,{type:"post",data:p,dataType:"json",contentType:!1,processData:!1,xhr:function(){var a=d.ajaxSettings.xhr();a.upload&&(a.upload.onprogress=function(a){a.lengthComputable&&(a=(a.loaded/a.total*100).toFixed(0)+"%",f(b,1===c.length?a:k+":"+c.length+" "+a))},a.upload.onloadend=
function(a){f(b,b.processingText||"Processing")});return a},success:function(n){a.off("preSubmit.DTE_Upload");a._event("uploadXhrSuccess",[b.name,n]);if(n.fieldErrors&&n.fieldErrors.length){n=n.fieldErrors;for(var m=0,t=n.length;m<t;m++)a.error(n[m].name,n[m].status)}else n.error?a.error(n.error):n.upload&&n.upload.id?(n.files&&d.each(n.files,function(a,b){h.files[a]||(h.files[a]={});d.extend(h.files[a],b)}),A.push(n.upload.id),k<c.length-1?(k++,g.readAsDataURL(c[k])):(e.call(a,A),u&&a.submit())):
a.error(b.name,"A server error occurred while uploading the file");f(b)},error:function(c){a._event("uploadXhrError",[b.name,c]);a.error(b.name,"A server error occurred while uploading the file");f(b)}}))}};c=d.map(c,function(a){return a});b._limitLeft!==l&&c.splice(b._limitLeft,c.length);g.readAsDataURL(c[0])};h.prototype._constructor=function(a){a=d.extend(!0,{},h.defaults,a);this.s=d.extend(!0,{},h.models.settings,{table:a.domTable||a.table,dbTable:a.dbTable||null,ajaxUrl:a.ajaxUrl,ajax:a.ajax,
idSrc:a.idSrc,dataSource:a.domTable||a.table?h.dataSources.dataTable:h.dataSources.html,formOptions:a.formOptions,legacyAjax:a.legacyAjax,template:a.template?d(a.template).detach():null});this.classes=d.extend(!0,{},h.classes);this.i18n=a.i18n;h.models.settings.unique++;var b=this,c=this.classes;this.dom={wrapper:d('<div class="'+c.wrapper+'"><div data-dte-e="processing" class="'+c.processing.indicator+'"><span/></div><div data-dte-e="body" class="'+c.body.wrapper+'"><div data-dte-e="body_content" class="'+
c.body.content+'"/></div><div data-dte-e="foot" class="'+c.footer.wrapper+'"><div class="'+c.footer.content+'"/></div></div>')[0],form:d('<form data-dte-e="form" class="'+c.form.tag+'"><div data-dte-e="form_content" class="'+c.form.content+'"/></form>')[0],formError:d('<div data-dte-e="form_error" class="'+c.form.error+'"/>')[0],formInfo:d('<div data-dte-e="form_info" class="'+c.form.info+'"/>')[0],header:d('<div data-dte-e="head" class="'+c.header.wrapper+'"><div class="'+c.header.content+'"/></div>')[0],
buttons:d('<div data-dte-e="form_buttons" class="'+c.form.buttons+'"/>')[0]};if(d.fn.dataTable.TableTools){var f=d.fn.dataTable.TableTools.BUTTONS,e=this.i18n;d.each(["create","edit","remove"],function(a,b){f["editor_"+b].sButtonText=e[b].button})}d.each(a.events,function(a,c){b.on(a,function(){var a=Array.prototype.slice.call(arguments);a.shift();c.apply(b,a)})});c=this.dom;var g=c.wrapper;c.formContent=x("form_content",c.form)[0];c.footer=x("foot",g)[0];c.body=x("body",g)[0];c.bodyContent=x("body_content",
g)[0];c.processing=x("processing",g)[0];a.fields&&this.add(a.fields);d(q).on("init.dt.dte"+this.s.unique,function(a,c,g){b.s.table&&c.nTable===d(b.s.table).get(0)&&(c._editor=b)}).on("xhr.dt.dte"+this.s.unique,function(a,c,g){g&&b.s.table&&c.nTable===d(b.s.table).get(0)&&b._optionsUpdate(g)});try{this.s.displayController=h.display[a.display].init(this)}catch(k){throw"Cannot find display controller "+a.display;}this._event("initComplete",[])};h.prototype._actionClass=function(){var a=this.classes.actions,
b=this.s.action,c=d(this.dom.wrapper);c.removeClass([a.create,a.edit,a.remove].join(" "));"create"===b?c.addClass(a.create):"edit"===b?c.addClass(a.edit):"remove"===b&&c.addClass(a.remove)};h.prototype._ajax=function(a,b,c,f){var e=this.s.action,g;f={type:"POST",dataType:"json",data:null,error:[function(a,b,c){g=c}],success:[],complete:[function(a,e){var f=null;if(204===a.status||"null"===a.responseText)f={};else try{f=a.responseJSON?a.responseJSON:d.parseJSON(a.responseText)}catch(z){}d.isPlainObject(f)||
d.isArray(f)?b(f,400<=a.status,a):c(a,e,g)}]};var k=this.s.ajax||this.s.ajaxUrl,h="edit"===e||"remove"===e?E(this.s.editFields,"idSrc"):null;d.isArray(h)&&(h=h.join(","));d.isPlainObject(k)&&k[e]&&(k=k[e]);if("function"===typeof k){var m=null;f=null;if(this.s.ajaxUrl){var p=this.s.ajaxUrl;p.create&&(m=p[e]);-1!==m.indexOf(" ")&&(e=m.split(" "),f=e[0],m=e[1]);m=m.replace(/_id_/,h)}k(f,m,a,b,c)}else"string"===typeof k?-1!==k.indexOf(" ")?(e=k.split(" "),f.type=e[0],f.url=e[1]):f.url=k:(k=d.extend({},
k||{}),k.complete&&(f.complete.unshift(k.complete),delete k.complete),k.error&&(f.error.unshift(k.error),delete k.error),f=d.extend({},f,k)),f.url=f.url.replace(/_id_/,h),f.data&&(k=(h="function"===typeof f.data)?f.data(a):f.data,a=h&&k?k:d.extend(!0,a,k)),f.data=a,"DELETE"!==f.type||f.deleteBody!==l&&!0!==f.deleteBody||(a=d.param(f.data),f.url+=-1===f.url.indexOf("?")?"?"+a:"&"+a,delete f.data),d.ajax(f)};h.prototype._animate=function(a,b,c,f){d.fn.animate?a.stop().animate(b,c,f):(a.css(b),"function"===
typeof c?c.call(a):f&&f.call(a))};h.prototype._assembleMain=function(){var a=this.dom;d(a.wrapper).prepend(a.header);d(a.footer).append(a.formError).append(a.buttons);d(a.bodyContent).append(a.formInfo).append(a.form)};h.prototype._blur=function(){var a=this.s.editOpts.onBlur;!1!==this._event("preBlur")&&("function"===typeof a?a(this):"submit"===a?this.submit():"close"===a&&this._close())};h.prototype._clearDynamicInfo=function(){if(this.s){var a=this.classes.field.error,b=this.s.fields;d("div."+
a,this.dom.wrapper).removeClass(a);d.each(b,function(a,b){b.error("").message("")});this.error("").message("")}};h.prototype._close=function(a){!1!==this._event("preClose")&&(this.s.closeCb&&(this.s.closeCb(a),this.s.closeCb=null),this.s.closeIcb&&(this.s.closeIcb(),this.s.closeIcb=null),d("body").off("focus.editor-focus"),this.s.displayed=!1,this._event("close"))};h.prototype._closeReg=function(a){this.s.closeCb=a};h.prototype._crudArgs=function(a,b,c,f){var e=this;if(!d.isPlainObject(a))if("boolean"===
typeof a){var g=a;a=b}else{var k=a;var h=b;g=c;a=f}g===l&&(g=!0);k&&e.title(k);h&&e.buttons(h);return{opts:d.extend({},this.s.formOptions.main,a),maybeOpen:function(){g&&e.open()}}};h.prototype._dataSource=function(a){var b=Array.prototype.slice.call(arguments);b.shift();var c=this.s.dataSource[a];if(c)return c.apply(this,b)};h.prototype._displayReorder=function(a){var b=this,c=d(this.dom.formContent),f=this.s.fields,e=this.s.order,g=this.s.template,k=this.s.mode||"main";a?this.s.includeFields=a:
a=this.s.includeFields;c.children().detach();d.each(e,function(d,e){d=e instanceof h.Field?e.name():e;-1!==b._weakInArray(d,a)&&(g&&"main"===k?(g.find('editor-field[name="'+d+'"]').after(f[d].node()),g.find('[data-editor-template="'+d+'"]').append(f[d].node())):c.append(f[d].node()))});g&&"main"===k&&g.appendTo(c);this._event("displayOrder",[this.s.displayed,this.s.action,c])};h.prototype._edit=function(a,b,c,f,e){var g=this,k=this.s.fields,h=[],m,p={};this.s.editFields=b;this.s.editData=p;this.s.modifier=
a;this.s.action="edit";this.dom.form.style.display="block";this.s.mode=c;this._actionClass();d.each(k,function(a,c){c.multiReset();m=!1;p[a]={};d.each(b,function(b,g){if(g.fields[a]){var e=c.valFromData(g.data);p[a][b]=null===e?"":d.isArray(e)?e.slice():e;if(!f||"row"===f.scope){if(c.multiSet(b,e!==l?e:c.def()),!g.displayFields||g.displayFields[a])m=!0}else if(!g.displayFields||g.displayFields[a])c.multiSet(b,e!==l?e:c.def()),m=!0}});0!==c.multiIds().length&&m&&h.push(a)});k=this.order().slice();
for(var n=k.length-1;0<=n;n--)-1===d.inArray(k[n].toString(),h)&&k.splice(n,1);this._displayReorder(k);this._event("initEdit",[E(b,"node")[0],E(b,"data")[0],a,c],function(){g._event("initMultiEdit",[b,a,c],function(){e()})})};h.prototype._event=function(a,b,c){b||(b=[]);if(d.isArray(a)){c=0;for(var f=a.length;c<f;c++)this._event(a[c],b)}else return f=d.Event(a),d(this).triggerHandler(f,b),0===a.indexOf("pre")&&!1===f.result&&d(this).triggerHandler(d.Event(a+"Cancelled"),b),c&&(f.result&&"object"===
typeof f.result&&f.result.then?f.result.then(c):c()),f.result};h.prototype._eventName=function(a){for(var b=a.split(" "),c=0,d=b.length;c<d;c++){a=b[c];var e=a.match(/^on([A-Z])/);e&&(a=e[1].toLowerCase()+a.substring(3));b[c]=a}return b.join(" ")};h.prototype._fieldFromNode=function(a){var b=null;d.each(this.s.fields,function(c,f){d(f.node()).find(a).length&&(b=f)});return b};h.prototype._fieldNames=function(a){return a===l?this.fields():d.isArray(a)?a:[a]};h.prototype._focus=function(a,b){var c=
this,f;a=d.map(a,function(a){return"string"===typeof a?c.s.fields[a]:a});"number"===typeof b?f=a[b]:b&&(f=0===b.indexOf("jq:")?d("div.DTE "+b.replace(/^jq:/,"")):this.s.fields[b]);(this.s.setFocus=f)&&f.focus()};h.prototype._formOptions=function(a){var b=this,c=L++,f=".dteInline"+c;a.closeOnComplete!==l&&(a.onComplete=a.closeOnComplete?"close":"none");a.submitOnBlur!==l&&(a.onBlur=a.submitOnBlur?"submit":"close");a.submitOnReturn!==l&&(a.onReturn=a.submitOnReturn?"submit":"none");a.blurOnBackground!==
l&&(a.onBackground=a.blurOnBackground?"blur":"none");this.s.editOpts=a;this.s.editCount=c;if("string"===typeof a.title||"function"===typeof a.title)this.title(a.title),a.title=!0;if("string"===typeof a.message||"function"===typeof a.message)this.message(a.message),a.message=!0;"boolean"!==typeof a.buttons&&(this.buttons(a.buttons),a.buttons=!0);d(q).on("keydown"+f,function(a){if(13===a.keyCode&&b.s.displayed){var c=d(q.activeElement);c&&b._fieldFromNode(c).canReturnSubmit(c)&&a.preventDefault()}});
d(q).on("keyup"+f,function(c){var g=d(q.activeElement);if(13===c.keyCode&&b.s.displayed){var e=b._fieldFromNode(g);e&&"function"===typeof e.canReturnSubmit&&e.canReturnSubmit(g)&&("submit"===a.onReturn?(c.preventDefault(),b.submit()):"function"===typeof a.onReturn&&(c.preventDefault(),a.onReturn(b,c)))}else if(27===c.keyCode)if(c.preventDefault(),"function"===typeof a.onEsc)a.onEsc(b,c);else"blur"===a.onEsc?b.blur():"close"===a.onEsc?b.close():"submit"===a.onEsc&&b.submit();else g.parents(".DTE_Form_Buttons").length&&
(37===c.keyCode?g.prev("button").focus():39===c.keyCode&&g.next("button").focus())});this.s.closeIcb=function(){d(q).off("keydown"+f);d(q).off("keyup"+f)};return f};h.prototype._legacyAjax=function(a,b,c){if(this.s.legacyAjax&&c)if("send"===a)if("create"===b||"edit"===b){var f;d.each(c.data,function(a,b){if(f!==l)throw"Editor: Multi-row editing is not supported by the legacy Ajax data format";f=a});c.data=c.data[f];"edit"===b&&(c.id=f)}else c.id=d.map(c.data,function(a,b){return b}),delete c.data;
else!c.data&&c.row?c.data=[c.row]:c.data||(c.data=[])};h.prototype._optionsUpdate=function(a){var b=this;a.options&&d.each(this.s.fields,function(c,d){a.options[c]!==l&&(d=b.field(c))&&d.update&&d.update(a.options[c])})};h.prototype._message=function(a,b){var c=d.fn.animate?!0:!1;"function"===typeof b&&(b=b(this,new v.Api(this.s.table)));a=d(a);c&&a.stop();b?this.s.displayed&&c?a.html(b).fadeIn():a.html(b).css("display","block"):this.s.displayed&&c?a.fadeOut(function(){a.html("")}):a.html("").css("display",
"none")};h.prototype._multiInfo=function(){var a=this.s.fields,b=this.s.includeFields,c=!0;if(b)for(var d=0,e=b.length;d<e;d++){var g=a[b[d]];var k=g.multiEditable();g.isMultiValue()&&k&&c?(g=!0,c=!1):g=g.isMultiValue()&&!k?!0:!1;a[b[d]].multiInfoShown(g)}};h.prototype._postopen=function(a){var b=this,c=this.s.displayController.captureFocus;c===l&&(c=!0);d(this.dom.form).off("submit.editor-internal").on("submit.editor-internal",function(a){a.preventDefault()});if(c&&("main"===a||"bubble"===a))d("body").on("focus.editor-focus",
function(){0===d(q.activeElement).parents(".DTE").length&&0===d(q.activeElement).parents(".DTED").length&&b.s.setFocus&&b.s.setFocus.focus()});this._multiInfo();this._event("open",[a,this.s.action]);return!0};h.prototype._preopen=function(a){if(!1===this._event("preOpen",[a,this.s.action]))return this._clearDynamicInfo(),this._event("cancelOpen",[a,this.s.action]),"inline"!==this.s.mode&&"bubble"!==this.s.mode||!this.s.closeIcb||this.s.closeIcb(),this.s.closeIcb=null,!1;this.s.displayed=a;return!0};
h.prototype._processing=function(a){var b=this.classes.processing.active;d(["div.DTE",this.dom.wrapper]).toggleClass(b,a);this.s.processing=a;this._event("processing",[a])};h.prototype._submit=function(a,b,c,f){var e=this,g=!1,k={},h={},m=v.ext.oApi._fnSetObjectDataFn,p=this.s.fields,n=this.s.editCount,t=this.s.editFields,u=this.s.editData,z=this.s.editOpts,F=z.submit,y=this.s.action,B={action:y,data:{}};this.s.dbTable&&(B.table=this.s.dbTable);if("create"===y||"edit"===y)if(d.each(t,function(a,b){var c=
{},e={};d.each(p,function(f,k){if(b.fields[f]&&k.submittable()){var h=k.multiGet(),n=m(f);if(h[a]===l)f=k.valFromData(b.data),n(c,f);else{h=h[a];var t=d.isArray(h)&&-1!==f.indexOf("[]")?m(f.replace(/\[.*$/,"")+"-many-count"):null;n(c,h);t&&t(c,h.length);"edit"!==y||u[f]&&k.compare(h,u[f][a])||(n(e,h),g=!0,t&&t(e,h.length))}}});d.isEmptyObject(c)||(k[a]=c);d.isEmptyObject(e)||(h[a]=e)}),"create"===y||"all"===F||"allIfChanged"===F&&g)B.data=k;else if("changed"===F&&g)B.data=h;else{this.s.action=null;
if("close"===z.onComplete&&(f===l||f))this._close(!1);else if("function"===typeof z.onComplete)z.onComplete(this);a&&a.call(this);this._processing(!1);this._event("submitComplete");return}else"remove"===y&&d.each(t,function(a,b){B.data[a]=b.data});this._legacyAjax("send",y,B);var H=d.extend(!0,{},B);c&&c(B);!1===this._event("preSubmit",[B,y])?this._processing(!1):(this.s.ajax||this.s.ajaxUrl?this._ajax:this._submitTable).call(this,B,function(c,g,d){e._submitSuccess(c,g,B,H,e.s.action,n,f,a,b,d)},
function(a,c,g){e._submitError(a,c,g,b,B,e.s.action)},B)};h.prototype._submitTable=function(a,b,c,f){var e=a.action,g={data:[]},k=v.ext.oApi._fnGetObjectDataFn(this.s.idSrc),h=v.ext.oApi._fnSetObjectDataFn(this.s.idSrc);if("remove"!==e){var m="main"===this.s.mode?this._dataSource("fields",this.modifier()):this._dataSource("individual",this.modifier());d.each(a.data,function(a,b){var c=d.fn.dataTableExt.oApi._fnExtend;if("edit"===e){var f=c({},m[a].data,!0);f=c(f,b,!0)}else f=c({},b,!0);b=k(f);"create"===
e&&b===l?h(f,+new Date+""+a):h(f,b);g.data.push(f)})}b(g)};h.prototype._submitSuccess=function(a,b,c,f,e,g,h,A,m,p){var k=this,t=this.s.fields,u=this.s.editOpts,z=this.s.modifier;this._legacyAjax("receive",e,a);this._event("postSubmit",[a,c,e,p]);a.error||(a.error="");a.fieldErrors||(a.fieldErrors=[]);if(b||a.error||a.fieldErrors.length){var F=[];a.error&&F.push(a.error);d.each(a.fieldErrors,function(a,b){var c=t[b.name];if(c.displayed()){if(c.error(b.status||"Error"),0===a)if("focus"===u.onFieldError)k._animate(d(k.dom.bodyContent,
k.s.wrapper),{scrollTop:d(c.node()).position().top},500),c.focus();else if("function"===typeof u.onFieldError)u.onFieldError(k,b)}else F.push(c.name()+": "+(b.status||"Error"))});this.error(F.join("<br>"));this._event("submitUnsuccessful",[a]);m&&m.call(k,a)}else{b={};if(!a.data||"create"!==e&&"edit"!==e)"remove"===e&&(this._dataSource("prep",e,z,f,a,b),this._event("preRemove",[a,this.ids()]),this._dataSource("remove",z,t,b),this._event(["remove","postRemove"],[a,this.ids()]),this._dataSource("commit",
e,z,a.data,b));else{this._dataSource("prep",e,z,f,a,b);for(f=0;f<a.data.length;f++){var y=a.data[f];c=this._dataSource("id",y);this._event("setData",[a,y,e]);"create"===e?(this._event("preCreate",[a,y,c]),this._dataSource("create",t,y,b),this._event(["create","postCreate"],[a,y,c])):"edit"===e&&(this._event("preEdit",[a,y,c]),this._dataSource("edit",z,t,y,b),this._event(["edit","postEdit"],[a,y,c]))}this._dataSource("commit",e,z,a.data,b)}if(g===this.s.editCount)if(this.s.action=null,"close"===u.onComplete&&
(h===l||h))this._close(a.data?!0:!1);else if("function"===typeof u.onComplete)u.onComplete(this);A&&A.call(k,a);this._event("submitSuccess",[a,y,e])}this._processing(!1);this._event("submitComplete",[a,y,e])};h.prototype._submitError=function(a,b,c,d,e,g){this._event("postSubmit",[null,e,g,a]);this.error(this.i18n.error.system);this._processing(!1);d&&d.call(this,a,b,c);this._event(["submitError","submitComplete"],[a,b,c,e])};h.prototype._tidy=function(a){var b=this,c=this.s.table?new d.fn.dataTable.Api(this.s.table):
null,f=!1;c&&(f=c.settings()[0].oFeatures.bServerSide);return this.s.processing?(this.one("submitComplete",function(){if(f)c.one("draw",a);else setTimeout(function(){a()},10)}),!0):"inline"===this.display()||"bubble"===this.display()?(this.one("close",function(){if(b.s.processing)b.one("submitComplete",function(d,g){if(f&&g)c.one("draw",a);else setTimeout(function(){b.s&&a()},10)});else setTimeout(function(){b.s&&a()},10)}).blur(),!0):!1};h.prototype._weakInArray=function(a,b){for(var c=0,d=b.length;c<
d;c++)if(a==b[c])return c;return-1};h.defaults={table:null,ajaxUrl:null,fields:[],display:"lightbox",ajax:null,idSrc:"DT_RowId",events:{},i18n:{create:{button:"New",title:"Create new entry",submit:"Create"},edit:{button:"Edit",title:"Edit entry",submit:"Update"},remove:{button:"Delete",title:"Delete",submit:"Delete",confirm:{_:"Are you sure you wish to delete %d rows?",1:"Are you sure you wish to delete 1 row?"}},error:{system:'A system error has occurred (<a target="_blank" href="//datatables.net/tn/12">More information</a>).'},
multi:{title:"Multiple values",info:"The selected items contain different values for this input. To edit and set all items for this input to the same value, click or tap here, otherwise they will retain their individual values.",restore:"Undo changes",noMulti:"This input can be edited individually, but not part of a group."},datetime:{previous:"Previous",next:"Next",months:"January February March April May June July August September October November December".split(" "),weekdays:"Sun Mon Tue Wed Thu Fri Sat".split(" "),
amPm:["am","pm"],unknown:"-"}},formOptions:{bubble:d.extend({},h.models.formOptions,{title:!1,message:!1,buttons:"_basic",submit:"changed"}),inline:d.extend({},h.models.formOptions,{buttons:!1,submit:"changed"}),main:d.extend({},h.models.formOptions)},legacyAjax:!1};(function(){function a(a){var b=q;if("keyless"!==a&&(b=d('[data-editor-id="'+a+'"]'),0===b.length&&(b="string"===typeof a?d(p(a)):d(a)),0===b.length))throw"Could not find an element with `data-editor-id` or `id` of: "+a;return b}function b(b,
c){b=a(b);return d('[data-editor-field="'+c+'"]',b)}function c(a,c,g){d.each(c,function(c,d){c=d.valFromData(g);c!==l&&(d=b(a,d.dataSrc()),d.filter("[data-editor-value]").length?d.attr("data-editor-value",c):d.each(function(){for(;this.childNodes.length;)this.removeChild(this.firstChild)}).html(c))})}var f=h.dataSources={},e=function(a){a=d(a);setTimeout(function(){a.addClass("highlight");setTimeout(function(){a.addClass("noHighlight").removeClass("highlight");setTimeout(function(){a.removeClass("noHighlight")},
550)},500)},20)},g=function(a,b,c,g,d){b.rows(c).indexes().each(function(c){c=b.row(c);var e=c.data(),f=d(e);f===l&&h.error("Unable to find row identifier",14);a[f]={idSrc:f,data:e,node:c.node(),fields:g,type:"row"}})},k=function(a,b,c){a=a.settings()[0].aoColumns[c];var g=a.editField!==l?a.editField:a.mData,e={};d.each(b,function(a,b){if(d.isArray(g))for(a=0;a<g.length;a++){var c=b,f=g[a];c.name()===f&&(e[c.name()]=c)}else b.name()===g&&(e[b.name()]=b)});d.isEmptyObject(e)&&h.error("Unable to automatically determine field from source. Please specify the field name.",
11);return e},A=function(a,b,c,e,f,h){b.cells(c).indexes().each(function(n){var m=b.cell(n),t=b.row(n.row).data();t=f(t);var l=h||k(b,e,n.column),u="object"===typeof c&&c.nodeName||c instanceof d;if(a[t]){var p=a[t].attach;var A=a[t].displayFields}g(a,b,n.row,e,f);a[t].attach=p||[];a[t].attach.push(u?d(c).get(0):m.node());a[t].displayFields=A||{};d.extend(a[t].displayFields,l)})},m=function(a,b,c,g,d){b.cells(null,c).indexes().each(function(c){A(a,b,c,g,d)})},p=function(a){return"string"===typeof a?
"#"+a.replace(/(:|\.|\[|\]|,)/g,"\\$1"):"#"+a};f.dataTable={id:function(a){return v.ext.oApi._fnGetObjectDataFn(this.s.idSrc)(a)},individual:function(a,b){var c=v.ext.oApi._fnGetObjectDataFn(this.s.idSrc),g=d(this.s.table).DataTable(),e=this.s.fields,f={};if(b){d.isArray(b)||(b=[b]);var h={};d.each(b,function(a,b){h[b]=e[b]})}A(f,g,a,e,c,h);return f},fields:function(a){var b=v.ext.oApi._fnGetObjectDataFn(this.s.idSrc),c=d(this.s.table).DataTable(),e=this.s.fields,f={};!d.isPlainObject(a)||a.rows===
l&&a.columns===l&&a.cells===l?g(f,c,a,e,b):(a.rows!==l&&g(f,c,a.rows,e,b),a.columns!==l&&m(f,c,a.columns,e,b),a.cells!==l&&A(f,c,a.cells,e,b));return f},create:function(a,b){a=d(this.s.table).DataTable();a.settings()[0].oFeatures.bServerSide&&"none"!==this.s.editOpts.drawType||(b=a.row.add(b),e(b.node()))},edit:function(a,b,c,g){var h=this;a=d(this.s.table).DataTable();if(!a.settings()[0].oFeatures.bServerSide||"none"===this.s.editOpts.drawType||"none"===this.s.editOpts.drawType){var k=f.dataTable.id.call(this,
c);try{var n=a.row(p(k))}catch(H){n=a}n.any()||(n=a.row(function(a,b,c){return k==f.dataTable.id.call(h,b)}));n.any()?(a=d.fn.dataTableExt.oApi._fnExtend,b=a({},n.data(),!0),b=a(b,c,!0),n.data(b),c=d.inArray(k,g.rowIds),g.rowIds.splice(c,1)):n=a.row.add(c);e(n.node())}},remove:function(a,b,c){var g=this;b=d(this.s.table).DataTable();var e=c.cancelled;if(0===e.length)b.rows(a).remove();else{var h=[];b.rows(a).every(function(){var a=f.dataTable.id.call(g,this.data());-1===d.inArray(a,e)&&h.push(this.index())});
b.rows(h).remove()}},prep:function(a,b,c,g,e){if("edit"===a){var f=g.cancelled||[];e.rowIds=d.map(c.data,function(a,b){return d.isEmptyObject(c.data[b])||-1!==d.inArray(b,f)?l:b})}else"remove"===a&&(e.cancelled=g.cancelled||[])},commit:function(a,b,c,g){var e=this;b=d(this.s.table).DataTable();if("edit"===a&&g.rowIds.length){a=g.rowIds;g=function(a){return function(b,c,g){return a==f.dataTable.id.call(e,c)}};c=0;for(var h=a.length;c<h;c++){try{var k=b.row(p(a[c]))}catch(H){k=b}k.any()||(k=b.row(g(a[c])));
k.any()&&k.remove()}}k=this.s.editOpts.drawType;"none"!==k&&b.draw(k)}};f.html={id:function(a){return v.ext.oApi._fnGetObjectDataFn(this.s.idSrc)(a)},initField:function(a){var b=d('[data-editor-label="'+(a.data||a.name)+'"]');!a.label&&b.length&&(a.label=b.html())},individual:function(a,c){if(a instanceof d||a.nodeName){var g=a;c||(c=[d(a).attr("data-editor-field")]);var e=d.fn.addBack?"addBack":"andSelf";a=d(a).parents("[data-editor-id]")[e]().data("editor-id")}a||(a="keyless");c&&!d.isArray(c)&&
(c=[c]);if(!c||0===c.length)throw"Cannot automatically determine field name from data source";e=f.html.fields.call(this,a);var k=this.s.fields,h={};d.each(c,function(a,b){h[b]=k[b]});d.each(e,function(e,f){f.type="cell";if(g)e=d(g);else{e=a;for(var m=c,n=d(),l=0,p=m.length;l<p;l++)n=n.add(b(e,m[l]));e=n.toArray()}f.attach=e;f.fields=k;f.displayFields=h});return e},fields:function(a){var c={},g=f.html;if(d.isArray(a)){for(var e=0,k=a.length;e<k;e++){var h=g.fields.call(this,a[e]);c[a[e]]=h[a[e]]}return c}var m=
{};g=this.s.fields;a||(a="keyless");d.each(g,function(c,g){c=g.dataSrc();c=b(a,c);c=c.filter("[data-editor-value]").length?c.attr("data-editor-value"):c.html();g.valToData(m,null===c?l:c)});c[a]={idSrc:a,data:m,node:q,fields:g,type:"row"};return c},create:function(b,g){if(g){var d=f.html.id.call(this,g);try{a(d).length&&c(d,b,g)}catch(z){}}},edit:function(a,b,g){a=f.html.id.call(this,g)||"keyless";c(a,b,g)},remove:function(b,c){a(b).remove()}}})();h.classes={wrapper:"DTE",processing:{indicator:"DTE_Processing_Indicator",
active:"processing"},header:{wrapper:"DTE_Header",content:"DTE_Header_Content"},body:{wrapper:"DTE_Body",content:"DTE_Body_Content"},footer:{wrapper:"DTE_Footer",content:"DTE_Footer_Content"},form:{wrapper:"DTE_Form",content:"DTE_Form_Content",tag:"",info:"DTE_Form_Info",error:"DTE_Form_Error",buttons:"DTE_Form_Buttons",button:"btn"},field:{wrapper:"DTE_Field",typePrefix:"DTE_Field_Type_",namePrefix:"DTE_Field_Name_",label:"DTE_Label",input:"DTE_Field_Input",inputControl:"DTE_Field_InputControl",
error:"DTE_Field_StateError","msg-label":"DTE_Label_Info","msg-error":"DTE_Field_Error","msg-message":"DTE_Field_Message","msg-info":"DTE_Field_Info",multiValue:"multi-value",multiInfo:"multi-info",multiRestore:"multi-restore",multiNoEdit:"multi-noEdit",disabled:"disabled",processing:"DTE_Processing_Indicator"},actions:{create:"DTE_Action_Create",edit:"DTE_Action_Edit",remove:"DTE_Action_Remove"},inline:{wrapper:"DTE DTE_Inline",liner:"DTE_Inline_Field",buttons:"DTE_Inline_Buttons"},bubble:{wrapper:"DTE DTE_Bubble",
liner:"DTE_Bubble_Liner",table:"DTE_Bubble_Table",close:"icon close",pointer:"DTE_Bubble_Triangle",bg:"DTE_Bubble_Background"}};(function(){if(v.TableTools){var a=v.TableTools.BUTTONS,b={sButtonText:null,editor:null,formTitle:null};a.editor_create=d.extend(!0,a.text,b,{formButtons:[{label:null,fn:function(a){this.submit()}}],fnClick:function(a,b){a=b.editor;var c=a.i18n.create;b=b.formButtons;b[0].label||(b[0].label=c.submit);a.create({title:c.title,buttons:b})}});a.editor_edit=d.extend(!0,a.select_single,
b,{formButtons:[{label:null,fn:function(a){this.submit()}}],fnClick:function(a,b){a=this.fnGetSelectedIndexes();if(1===a.length){var c=b.editor,g=c.i18n.edit;b=b.formButtons;b[0].label||(b[0].label=g.submit);c.edit(a[0],{title:g.title,buttons:b})}}});a.editor_remove=d.extend(!0,a.select,b,{question:null,formButtons:[{label:null,fn:function(a){var b=this;this.submit(function(a){d.fn.dataTable.TableTools.fnGetInstance(d(b.s.table).DataTable().table().node()).fnSelectNone()})}}],fnClick:function(a,b){a=
this.fnGetSelectedIndexes();if(0!==a.length){var c=b.editor,g=c.i18n.remove;b=b.formButtons;var d="string"===typeof g.confirm?g.confirm:g.confirm[a.length]?g.confirm[a.length]:g.confirm._;b[0].label||(b[0].label=g.submit);c.remove(a,{message:d.replace(/%d/g,a.length),title:g.title,buttons:b})}}})}a=v.ext.buttons;d.extend(a,{create:{text:function(a,b,d){return a.i18n("buttons.create",d.editor.i18n.create.button)},className:"buttons-create",editor:null,formButtons:{text:function(a){return a.i18n.create.submit},
action:function(a){this.submit()}},formMessage:null,formTitle:null,action:function(a,b,d,g){var c=this;a=g.editor;this.processing(!0);a.one("preOpen",function(){c.processing(!1)}).create({buttons:g.formButtons,message:g.formMessage,title:g.formTitle||a.i18n.create.title})}},edit:{extend:"selected",text:function(a,b,d){return a.i18n("buttons.edit",d.editor.i18n.edit.button)},className:"buttons-edit",editor:null,formButtons:{text:function(a){return a.i18n.edit.submit},action:function(a){this.submit()}},
formMessage:null,formTitle:null,action:function(a,b,d,g){var c=this;a=g.editor;d=b.rows({selected:!0}).indexes();var e=b.columns({selected:!0}).indexes();b=b.cells({selected:!0}).indexes();b=e.length||b.length?{rows:d,columns:e,cells:b}:d;this.processing(!0);a.one("preOpen",function(){c.processing(!1)}).edit(b,{message:g.formMessage,buttons:g.formButtons,title:g.formTitle||a.i18n.edit.title})}},remove:{extend:"selected",limitTo:["rows"],text:function(a,b,d){return a.i18n("buttons.remove",d.editor.i18n.remove.button)},
className:"buttons-remove",editor:null,formButtons:{text:function(a){return a.i18n.remove.submit},action:function(a){this.submit()}},formMessage:function(a,b){b=b.rows({selected:!0}).indexes();a=a.i18n.remove;return("string"===typeof a.confirm?a.confirm:a.confirm[b.length]?a.confirm[b.length]:a.confirm._).replace(/%d/g,b.length)},formTitle:null,action:function(a,b,d,g){var c=this;a=g.editor;this.processing(!0);a.one("preOpen",function(){c.processing(!1)}).remove(b.rows({selected:!0}).indexes(),{buttons:g.formButtons,
message:g.formMessage,title:g.formTitle||a.i18n.remove.title})}}});a.editSingle=d.extend({},a.edit);a.editSingle.extend="selectedSingle";a.removeSingle=d.extend({},a.remove);a.removeSingle.extend="selectedSingle"})();h.fieldTypes={};h.DateTime=function(a,b){this.c=d.extend(!0,{},h.DateTime.defaults,b);var c=this.c.classPrefix,f=this.c.i18n;if(!r.moment&&"YYYY-MM-DD"!==this.c.format)throw"Editor datetime: Without momentjs only the format 'YYYY-MM-DD' can be used";b=function(a){return'<div class="'+
c+'-timeblock"><div class="'+c+'-iconUp"><button>'+f.previous+'</button></div><div class="'+c+'-label"><span/><select class="'+c+"-"+a+'"/></div><div class="'+c+'-iconDown"><button>'+f.next+"</button></div></div>"};b=d('<div class="'+c+'"><div class="'+c+'-date"><div class="'+c+'-title"><div class="'+c+'-iconLeft"><button>'+f.previous+'</button></div><div class="'+c+'-iconRight"><button>'+f.next+'</button></div><div class="'+c+'-label"><span/><select class="'+c+'-month"/></div><div class="'+c+'-label"><span/><select class="'+
c+'-year"/></div></div><div class="'+c+'-calendar"/></div><div class="'+c+'-time">'+b("hours")+"<span>:</span>"+b("minutes")+"<span>:</span>"+b("seconds")+b("ampm")+'</div><div class="'+c+'-error"/></div>');this.dom={container:b,date:b.find("."+c+"-date"),title:b.find("."+c+"-title"),calendar:b.find("."+c+"-calendar"),time:b.find("."+c+"-time"),error:b.find("."+c+"-error"),input:d(a)};this.s={d:null,display:null,namespace:"editor-dateime-"+h.DateTime._instance++,parts:{date:null!==this.c.format.match(/[YMD]|L(?!T)|l/),
time:null!==this.c.format.match(/[Hhm]|LT|LTS/),seconds:-1!==this.c.format.indexOf("s"),hours12:null!==this.c.format.match(/[haA]/)}};this.dom.container.append(this.dom.date).append(this.dom.time).append(this.dom.error);this.dom.date.append(this.dom.title).append(this.dom.calendar);this._constructor()};d.extend(h.DateTime.prototype,{destroy:function(){this._hide();this.dom.container.off().empty();this.dom.input.off(".editor-datetime")},errorMsg:function(a){var b=this.dom.error;a?b.html(a):b.empty()},
hide:function(){this._hide()},max:function(a){this.c.maxDate=a;this._optionsTitle();this._setCalander()},min:function(a){this.c.minDate=a;this._optionsTitle();this._setCalander()},owns:function(a){return 0<d(a).parents().filter(this.dom.container).length},val:function(a,b){if(a===l)return this.s.d;if(a instanceof Date)this.s.d=this._dateToUtc(a);else if(null===a||""===a)this.s.d=null;else if("string"===typeof a)if(r.moment){var c=r.moment.utc(a,this.c.format,this.c.momentLocale,this.c.momentStrict);
this.s.d=c.isValid()?c.toDate():null}else c=a.match(/(\d{4})\-(\d{2})\-(\d{2})/),this.s.d=c?new Date(Date.UTC(c[1],c[2]-1,c[3])):null;if(b||b===l)this.s.d?this._writeOutput():this.dom.input.val(a);this.s.d||(this.s.d=this._dateToUtc(new Date));this.s.display=new Date(this.s.d.toString());this.s.display.setUTCDate(1);this._setTitle();this._setCalander();this._setTime()},_constructor:function(){var a=this,b=this.c.classPrefix,c=this.c.i18n,f=this.c.onChange;this.s.parts.date||this.dom.date.css("display",
"none");this.s.parts.time||this.dom.time.css("display","none");this.s.parts.seconds||(this.dom.time.children("div.editor-datetime-timeblock").eq(2).remove(),this.dom.time.children("span").eq(1).remove());this.s.parts.hours12||this.dom.time.children("div.editor-datetime-timeblock").last().remove();this._optionsTitle();this._optionsTime("hours",this.s.parts.hours12?12:24,1);this._optionsTime("minutes",60,this.c.minutesIncrement);this._optionsTime("seconds",60,this.c.secondsIncrement);this._options("ampm",
["am","pm"],c.amPm);this.dom.input.attr("autocomplete","off").on("focus.editor-datetime click.editor-datetime",function(){a.dom.container.is(":visible")||a.dom.input.is(":disabled")||(a.val(a.dom.input.val(),!1),a._show())}).on("keyup.editor-datetime",function(){a.dom.container.is(":visible")&&a.val(a.dom.input.val(),!1)});this.dom.container.on("change","select",function(){var c=d(this),g=c.val();c.hasClass(b+"-month")?(a._correctMonth(a.s.display,g),a._setTitle(),a._setCalander()):c.hasClass(b+"-year")?
(a.s.display.setUTCFullYear(g),a._setTitle(),a._setCalander()):c.hasClass(b+"-hours")||c.hasClass(b+"-ampm")?(a.s.parts.hours12?(c=1*d(a.dom.container).find("."+b+"-hours").val(),g="pm"===d(a.dom.container).find("."+b+"-ampm").val(),a.s.d.setUTCHours(12!==c||g?g&&12!==c?c+12:c:0)):a.s.d.setUTCHours(g),a._setTime(),a._writeOutput(!0),f()):c.hasClass(b+"-minutes")?(a.s.d.setUTCMinutes(g),a._setTime(),a._writeOutput(!0),f()):c.hasClass(b+"-seconds")&&(a.s.d.setSeconds(g),a._setTime(),a._writeOutput(!0),
f());a.dom.input.focus();a._position()}).on("click",function(c){var g=c.target.nodeName.toLowerCase();"select"!==g&&(c.stopPropagation(),"button"===g?(c=d(c.target),g=c.parent(),g.hasClass("disabled")||(g.hasClass(b+"-iconLeft")?(a.s.display.setUTCMonth(a.s.display.getUTCMonth()-1),a._setTitle(),a._setCalander(),a.dom.input.focus()):g.hasClass(b+"-iconRight")?(a._correctMonth(a.s.display,a.s.display.getUTCMonth()+1),a._setTitle(),a._setCalander(),a.dom.input.focus()):g.hasClass(b+"-iconUp")?(c=g.parent().find("select")[0],
c.selectedIndex=c.selectedIndex!==c.options.length-1?c.selectedIndex+1:0,d(c).change()):g.hasClass(b+"-iconDown")?(c=g.parent().find("select")[0],c.selectedIndex=0===c.selectedIndex?c.options.length-1:c.selectedIndex-1,d(c).change()):(a.s.d||(a.s.d=a._dateToUtc(new Date)),a.s.d.setUTCDate(1),a.s.d.setUTCFullYear(c.data("year")),a.s.d.setUTCMonth(c.data("month")),a.s.d.setUTCDate(c.data("day")),a._writeOutput(!0),a.s.parts.time?a._setCalander():setTimeout(function(){a._hide()},10),f()))):a.dom.input.focus())})},
_compareDates:function(a,b){return this._dateToUtcString(a)===this._dateToUtcString(b)},_correctMonth:function(a,b){var c=this._daysInMonth(a.getUTCFullYear(),b),d=a.getUTCDate()>c;a.setUTCMonth(b);d&&(a.setUTCDate(c),a.setUTCMonth(b))},_daysInMonth:function(a,b){return[31,0!==a%4||0===a%100&&0!==a%400?28:29,31,30,31,30,31,31,30,31,30,31][b]},_dateToUtc:function(a){return new Date(Date.UTC(a.getFullYear(),a.getMonth(),a.getDate(),a.getHours(),a.getMinutes(),a.getSeconds()))},_dateToUtcString:function(a){return a.getUTCFullYear()+
"-"+this._pad(a.getUTCMonth()+1)+"-"+this._pad(a.getUTCDate())},_hide:function(){var a=this.s.namespace;this.dom.container.detach();d(r).off("."+a);d(q).off("keydown."+a);d("div.DTE_Body_Content").off("scroll."+a);d("body").off("click."+a)},_hours24To12:function(a){return 0===a?12:12<a?a-12:a},_htmlDay:function(a){if(a.empty)return'<td class="empty"></td>';var b=["day"],c=this.c.classPrefix;a.disabled&&b.push("disabled");a.today&&b.push("today");a.selected&&b.push("selected");return'<td data-day="'+
a.day+'" class="'+b.join(" ")+'"><button class="'+c+"-button "+c+'-day" type="button" data-year="'+a.year+'" data-month="'+a.month+'" data-day="'+a.day+'">'+a.day+"</button></td>"},_htmlMonth:function(a,b){var c=this._dateToUtc(new Date),f=this._daysInMonth(a,b),e=(new Date(Date.UTC(a,b,1))).getUTCDay(),g=[],h=[];0<this.c.firstDay&&(e-=this.c.firstDay,0>e&&(e+=7));for(var l=f+e,m=l;7<m;)m-=7;l+=7-m;var p=this.c.minDate;m=this.c.maxDate;p&&(p.setUTCHours(0),p.setUTCMinutes(0),p.setSeconds(0));m&&(m.setUTCHours(23),
m.setUTCMinutes(59),m.setSeconds(59));for(var n=0,t=0;n<l;n++){var u=new Date(Date.UTC(a,b,1+(n-e))),q=this.s.d?this._compareDates(u,this.s.d):!1,r=this._compareDates(u,c),v=n<e||n>=f+e,w=p&&u<p||m&&u>m,x=this.c.disableDays;d.isArray(x)&&-1!==d.inArray(u.getUTCDay(),x)?w=!0:"function"===typeof x&&!0===x(u)&&(w=!0);h.push(this._htmlDay({day:1+(n-e),month:b,year:a,selected:q,today:r,disabled:w,empty:v}));7===++t&&(this.c.showWeekNumber&&h.unshift(this._htmlWeekOfYear(n-e,b,a)),g.push("<tr>"+h.join("")+
"</tr>"),h=[],t=0)}c=this.c.classPrefix;f=c+"-table";this.c.showWeekNumber&&(f+=" weekNumber");p&&(p=p>new Date(Date.UTC(a,b,1,0,0,0)),this.dom.title.find("div."+c+"-iconLeft").css("display",p?"none":"block"));m&&(a=m<new Date(Date.UTC(a,b+1,1,0,0,0)),this.dom.title.find("div."+c+"-iconRight").css("display",a?"none":"block"));return'<table class="'+f+'"><thead>'+this._htmlMonthHead()+"</thead><tbody>"+g.join("")+"</tbody></table>"},_htmlMonthHead:function(){var a=[],b=this.c.firstDay,c=this.c.i18n,
d=function(a){for(a+=b;7<=a;)a-=7;return c.weekdays[a]};this.c.showWeekNumber&&a.push("<th></th>");for(var e=0;7>e;e++)a.push("<th>"+d(e)+"</th>");return a.join("")},_htmlWeekOfYear:function(a,b,c){a=new Date(c,b,a,0,0,0,0);a.setDate(a.getDate()+4-(a.getDay()||7));return'<td class="'+this.c.classPrefix+'-week">'+Math.ceil(((a-new Date(c,0,1))/864E5+1)/7)+"</td>"},_options:function(a,b,c){c||(c=b);a=this.dom.container.find("select."+this.c.classPrefix+"-"+a);a.empty();for(var d=0,e=b.length;d<e;d++)a.append('<option value="'+
b[d]+'">'+c[d]+"</option>")},_optionSet:function(a,b){var c=this.dom.container.find("select."+this.c.classPrefix+"-"+a);a=c.parent().children("span");c.val(b);b=c.find("option:selected");a.html(0!==b.length?b.text():this.c.i18n.unknown)},_optionsTime:function(a,b,c){a=this.dom.container.find("select."+this.c.classPrefix+"-"+a);var f=0,e=b,g=12===b?function(a){return a}:this._pad;12===b&&(f=1,e=13);if(12===b||24===b)var h=this.c.hoursAvailable;for(b=f;b<e;b+=c)h&&-1===d.inArray(b,h)||a.append('<option value="'+
b+'">'+g(b)+"</option>")},_optionsTitle:function(a,b){a=this.c.i18n;var c=this.c.minDate;b=this.c.maxDate;c=c?c.getFullYear():null;b=b?b.getFullYear():null;c=null!==c?c:(new Date).getFullYear()-this.c.yearRange;b=null!==b?b:(new Date).getFullYear()+this.c.yearRange;this._options("month",this._range(0,11),a.months);this._options("year",this._range(c,b))},_pad:function(a){return 10>a?"0"+a:a},_position:function(){var a=this.dom.input.offset(),b=this.dom.container,c=this.dom.input.outerHeight();b.css({top:a.top+
c,left:a.left}).appendTo("body");var f=b.outerHeight(),e=b.outerWidth(),g=d(r).scrollTop();a.top+c+f-g>d(r).height()&&(c=a.top-f,b.css("top",0>c?0:c));e+a.left>d(r).width()&&(a=d(r).width()-e,b.css("left",0>a?0:a))},_range:function(a,b){for(var c=[];a<=b;a++)c.push(a);return c},_setCalander:function(){this.s.display&&this.dom.calendar.empty().append(this._htmlMonth(this.s.display.getUTCFullYear(),this.s.display.getUTCMonth()))},_setTitle:function(){this._optionSet("month",this.s.display.getUTCMonth());
this._optionSet("year",this.s.display.getUTCFullYear())},_setTime:function(){var a=this.s.d,b=a?a.getUTCHours():0;this.s.parts.hours12?(this._optionSet("hours",this._hours24To12(b)),this._optionSet("ampm",12>b?"am":"pm")):this._optionSet("hours",b);this._optionSet("minutes",a?a.getUTCMinutes():0);this._optionSet("seconds",a?a.getSeconds():0)},_show:function(){var a=this,b=this.s.namespace;this._position();d(r).on("scroll."+b+" resize."+b,function(){a._position()});d("div.DTE_Body_Content").on("scroll."+
b,function(){a._position()});d(q).on("keydown."+b,function(b){9!==b.keyCode&&27!==b.keyCode&&13!==b.keyCode||a._hide()});setTimeout(function(){d("body").on("click."+b,function(b){d(b.target).parents().filter(a.dom.container).length||b.target===a.dom.input[0]||a._hide()})},10)},_writeOutput:function(a){var b=this.s.d;b=r.moment?r.moment.utc(b,l,this.c.momentLocale,this.c.momentStrict).format(this.c.format):b.getUTCFullYear()+"-"+this._pad(b.getUTCMonth()+1)+"-"+this._pad(b.getUTCDate());this.dom.input.val(b);
a&&this.dom.input.focus()}});h.DateTime._instance=0;h.DateTime.defaults={classPrefix:"editor-datetime",disableDays:null,firstDay:1,format:"YYYY-MM-DD",hoursAvailable:null,i18n:h.defaults.i18n.datetime,maxDate:null,minDate:null,minutesIncrement:1,momentStrict:!0,momentLocale:"en",onChange:function(){},secondsIncrement:1,showWeekNumber:!1,yearRange:10};(function(){function a(a,b){if(null===b||b===l)b=a.uploadText||"Choose file...";a._input.find("div.upload button").html(b)}function b(b,c,e,f){var g=
b.classes.form.button,k=d('<div class="editor_upload"><div class="eu_table"><div class="row"><div class="cell upload limitHide"><button class="'+g+'" /><input type="file" '+(f?"multiple":"")+'/></div><div class="cell clearValue"><button class="'+g+'" /></div></div><div class="row second"><div class="cell limitHide"><div class="drop"><span/></div></div><div class="cell"><div class="rendered"/></div></div></div></div>');c._input=k;c._enabled=!0;a(c);if(r.FileReader&&!1!==c.dragDrop){k.find("div.drop span").text(c.dragDropText||
"Drag and drop a file here to upload");var m=k.find("div.drop");m.on("drop",function(g){c._enabled&&(h.upload(b,c,g.originalEvent.dataTransfer.files,a,e),m.removeClass("over"));return!1}).on("dragleave dragexit",function(a){c._enabled&&m.removeClass("over");return!1}).on("dragover",function(a){c._enabled&&m.addClass("over");return!1});b.on("open",function(){d("body").on("dragover.DTE_Upload drop.DTE_Upload",function(a){return!1})}).on("close",function(){d("body").off("dragover.DTE_Upload drop.DTE_Upload")})}else k.addClass("noDrop"),
k.append(k.find("div.rendered"));k.find("div.clearValue button").on("click",function(){h.fieldTypes.upload.set.call(b,c,"")});k.find("input[type=file]").on("change",function(){h.upload(b,c,this.files,a,function(a){e.call(b,a);k.find("input[type=file]").val("")})});return k}function c(a){setTimeout(function(){a.trigger("change",{editor:!0,editorSet:!0})},0)}var f=h.fieldTypes,e=d.extend(!0,{},h.models.fieldType,{get:function(a){return a._input.val()},set:function(a,b){a._input.val(b);c(a._input)},
enable:function(a){a._input.prop("disabled",!1)},disable:function(a){a._input.prop("disabled",!0)},canReturnSubmit:function(a,b){return!0}});f.hidden={create:function(a){a._val=a.value;return null},get:function(a){return a._val},set:function(a,b){a._val=b}};f.readonly=d.extend(!0,{},e,{create:function(a){a._input=d("<input/>").attr(d.extend({id:h.safeId(a.id),type:"text",readonly:"readonly"},a.attr||{}));return a._input[0]}});f.text=d.extend(!0,{},e,{create:function(a){a._input=d("<input/>").attr(d.extend({id:h.safeId(a.id),
type:"text"},a.attr||{}));return a._input[0]}});f.password=d.extend(!0,{},e,{create:function(a){a._input=d("<input/>").attr(d.extend({id:h.safeId(a.id),type:"password"},a.attr||{}));return a._input[0]}});f.textarea=d.extend(!0,{},e,{create:function(a){a._input=d("<textarea/>").attr(d.extend({id:h.safeId(a.id)},a.attr||{}));return a._input[0]},canReturnSubmit:function(a,b){return!1}});f.select=d.extend(!0,{},e,{_addOptions:function(a,b,c){var g=a._input[0].options,e=0;if(c)e=g.length;else if(g.length=
0,a.placeholder!==l){c=a.placeholderValue!==l?a.placeholderValue:"";e+=1;g[0]=new Option(a.placeholder,c);var f=a.placeholderDisabled!==l?a.placeholderDisabled:!0;g[0].hidden=f;g[0].disabled=f;g[0]._editor_val=c}b&&h.pairs(b,a.optionsPair,function(a,b,c,f){b=new Option(b,a);b._editor_val=a;f&&d(b).attr(f);g[c+e]=b})},create:function(a){a._input=d("<select/>").attr(d.extend({id:h.safeId(a.id),multiple:!0===a.multiple},a.attr||{})).on("change.dte",function(b,c){c&&c.editor||(a._lastSet=f.select.get(a))});
f.select._addOptions(a,a.options||a.ipOpts);return a._input[0]},update:function(a,b,d){f.select._addOptions(a,b,d);b=a._lastSet;b!==l&&f.select.set(a,b,!0);c(a._input)},get:function(a){var b=a._input.find("option:selected").map(function(){return this._editor_val}).toArray();return a.multiple?a.separator?b.join(a.separator):b:b.length?b[0]:null},set:function(a,b,e){e||(a._lastSet=b);a.multiple&&a.separator&&!d.isArray(b)?b="string"===typeof b?b.split(a.separator):[]:d.isArray(b)||(b=[b]);var g,f=b.length,
h,k=!1,l=a._input.find("option");a._input.find("option").each(function(){h=!1;for(g=0;g<f;g++)if(this._editor_val==b[g]){k=h=!0;break}this.selected=h});a.placeholder&&!k&&!a.multiple&&l.length&&(l[0].selected=!0);e||c(a._input);return k},destroy:function(a){a._input.off("change.dte")}});f.checkbox=d.extend(!0,{},e,{_addOptions:function(a,b,c){var g=a._input,e=0;c?e=d("input",g).length:g.empty();b&&h.pairs(b,a.optionsPair,function(b,c,f,k){g.append('<div><input id="'+h.safeId(a.id)+"_"+(f+e)+'" type="checkbox" /><label for="'+
h.safeId(a.id)+"_"+(f+e)+'">'+c+"</label></div>");d("input:last",g).attr("value",b)[0]._editor_val=b;k&&d("input:last",g).attr(k)})},create:function(a){a._input=d("<div />");f.checkbox._addOptions(a,a.options||a.ipOpts);return a._input[0]},get:function(a){var b=[],c=a._input.find("input:checked");c.length?c.each(function(){b.push(this._editor_val)}):a.unselectedValue!==l&&b.push(a.unselectedValue);return a.separator===l||null===a.separator?b:b.join(a.separator)},set:function(a,b){var g=a._input.find("input");
d.isArray(b)||"string"!==typeof b?d.isArray(b)||(b=[b]):b=b.split(a.separator||"|");var e,f=b.length,h;g.each(function(){h=!1;for(e=0;e<f;e++)if(this._editor_val==b[e]){h=!0;break}this.checked=h});c(g)},enable:function(a){a._input.find("input").prop("disabled",!1)},disable:function(a){a._input.find("input").prop("disabled",!0)},update:function(a,b,c){var d=f.checkbox,g=d.get(a);d._addOptions(a,b,c);d.set(a,g)}});f.radio=d.extend(!0,{},e,{_addOptions:function(a,b,c){var g=a._input,e=0;c?e=d("input",
g).length:g.empty();b&&h.pairs(b,a.optionsPair,function(b,c,f,k){g.append('<div><input id="'+h.safeId(a.id)+"_"+(f+e)+'" type="radio" name="'+a.name+'" /><label for="'+h.safeId(a.id)+"_"+(f+e)+'">'+c+"</label></div>");d("input:last",g).attr("value",b)[0]._editor_val=b;k&&d("input:last",g).attr(k)})},create:function(a){a._input=d("<div />");f.radio._addOptions(a,a.options||a.ipOpts);this.on("open",function(){a._input.find("input").each(function(){this._preChecked&&(this.checked=!0)})});return a._input[0]},
get:function(a){a=a._input.find("input:checked");return a.length?a[0]._editor_val:l},set:function(a,b){a._input.find("input").each(function(){this._preChecked=!1;this._preChecked=this._editor_val==b?this.checked=!0:this.checked=!1});c(a._input.find("input:checked"))},enable:function(a){a._input.find("input").prop("disabled",!1)},disable:function(a){a._input.find("input").prop("disabled",!0)},update:function(a,b,c){var d=f.radio,g=d.get(a);d._addOptions(a,b,c);b=a._input.find("input");d.set(a,b.filter('[value="'+
g+'"]').length?g:b.eq(0).attr("value"))}});f.date=d.extend(!0,{},e,{create:function(a){a._input=d("<input />").attr(d.extend({id:h.safeId(a.id),type:"text"},a.attr));d.datepicker?(a._input.addClass("jqueryui"),a.dateFormat||(a.dateFormat=d.datepicker.RFC_2822),setTimeout(function(){d(a._input).datepicker(d.extend({showOn:"both",dateFormat:a.dateFormat,buttonImage:a.dateImage,buttonImageOnly:!0,onSelect:function(){a._input.focus().click()}},a.opts));d("#ui-datepicker-div").css("display","none")},10)):
a._input.attr("type","date");return a._input[0]},set:function(a,b){d.datepicker&&a._input.hasClass("hasDatepicker")?a._input.datepicker("setDate",b).change():d(a._input).val(b)},enable:function(a){d.datepicker?a._input.datepicker("enable"):d(a._input).prop("disabled",!1)},disable:function(a){d.datepicker?a._input.datepicker("disable"):d(a._input).prop("disabled",!0)},owns:function(a,b){return d(b).parents("div.ui-datepicker").length||d(b).parents("div.ui-datepicker-header").length?!0:!1}});f.datetime=
d.extend(!0,{},e,{create:function(a){a._input=d("<input />").attr(d.extend(!0,{id:h.safeId(a.id),type:"text"},a.attr));a._picker=new h.DateTime(a._input,d.extend({format:a.format,i18n:this.i18n.datetime,onChange:function(){c(a._input)}},a.opts));a._closeFn=function(){a._picker.hide()};if(!1===a.keyInput)a._input.on("keydown",function(a){a.preventDefault()});this.on("close",a._closeFn);return a._input[0]},set:function(a,b){a._picker.val(b);c(a._input)},owns:function(a,b){return a._picker.owns(b)},
errorMessage:function(a,b){a._picker.errorMsg(b)},destroy:function(a){this.off("close",a._closeFn);a._input.off("keydown");a._picker.destroy()},minDate:function(a,b){a._picker.min(b)},maxDate:function(a,b){a._picker.max(b)}});f.upload=d.extend(!0,{},e,{create:function(a){var c=this;return b(c,a,function(b){h.fieldTypes.upload.set.call(c,a,b[0]);c._event("postUpload",[a.name,b[0]])})},get:function(a){return a._val},set:function(a,b){a._val=b;var c=a._input;if(a.display){var d=c.find("div.rendered");
a._val?d.html(a.display(a._val)):d.empty().append("<span>"+(a.noFileText||"No file")+"</span>")}d=c.find("div.clearValue button");b&&a.clearText?(d.html(a.clearText),c.removeClass("noClear")):c.addClass("noClear");a._input.find("input").triggerHandler("upload.editor",[a._val])},enable:function(a){a._input.find("input").prop("disabled",!1);a._enabled=!0},disable:function(a){a._input.find("input").prop("disabled",!0);a._enabled=!1},canReturnSubmit:function(a,b){return!1}});f.uploadMany=d.extend(!0,
{},e,{_showHide:function(a){a.limit&&(a._container.find("div.limitHide").css("display",a._val.length>=a.limit?"none":"block"),a._limitLeft=a.limit-a._val.length)},create:function(a){var c=this,e=b(c,a,function(b){a._val=a._val.concat(b);h.fieldTypes.uploadMany.set.call(c,a,a._val);c._event("postUpload",[a.name,a._val])},!0);e.addClass("multi").on("click","button.remove",function(b){b.stopPropagation();b=d(this).data("idx");a._val.splice(b,1);h.fieldTypes.uploadMany.set.call(c,a,a._val)});return a._container=
e},get:function(a){return a._val},set:function(a,b){b||(b=[]);if(!d.isArray(b))throw"Upload collections must have an array as a value";a._val=b;var c=this,e=a._input;if(a.display)if(e=e.find("div.rendered").empty(),b.length){var f=d("<ul/>").appendTo(e);d.each(b,function(b,d){f.append("<li>"+a.display(d,b)+' <button class="'+c.classes.form.button+' remove" data-idx="'+b+'">&times;</button></li>')})}else e.append("<span>"+(a.noFileText||"No files")+"</span>");h.fieldTypes.uploadMany._showHide(a);a._input.find("input").triggerHandler("upload.editor",
[a._val])},enable:function(a){a._input.find("input").prop("disabled",!1);a._enabled=!0},disable:function(a){a._input.find("input").prop("disabled",!0);a._enabled=!1},canReturnSubmit:function(a,b){return!1}})})();v.ext.editorFields&&d.extend(h.fieldTypes,v.ext.editorFields);v.ext.editorFields=h.fieldTypes;h.files={};h.prototype.CLASS="Editor";h.version="1.8.1";return h});
