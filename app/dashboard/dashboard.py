
from common import TrialModelView
from app import db
from flask import Blueprint, render_template
from flask_login import login_required

dashboard_bp = Blueprint("dashboard_bp", __name__,
                         template_folder="templates", static_folder="static")


def add_admins(admin):
    admin.add_view(TrialModelView(Dashboard, db.session, category='Mask'))


@dashboard_bp.route('/new-dashboard')
@login_required
def new_dashboard():
    template = 'new-dashboard.html'
    return render_template(template)
