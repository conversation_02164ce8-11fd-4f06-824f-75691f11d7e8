<!DOCTYPE html>
{% from "security/_macros.html" import render_field,render_field_with_errors %}

<html lang="en">
<head>

    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <meta name="description" content="">
    <meta name="author" content="">

    <title>Rezibase</title>

    <!-- Bootstrap core CSS-->
    <link href="/static/theme/vendor/bootstrap/css/bootstrap.min.css" rel="stylesheet">

    <!-- Custom fonts for this template-->
    <link href="/static/theme/vendor/fontawesome-free/css/all.min.css" rel="stylesheet" type="text/css">

    <!-- Custom styles for this template-->
    <link href="/static/theme/css/sb-admin.css" rel="stylesheet">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/zxcvbn/4.2.0/zxcvbn.js"></script>

    <style>
        .er-lockup svg {
            fill: #AD9B85;
        }

        .password-meter-wrap {
            margin-top: 5px;
            height: 16px;
            background-color: #ddd;
        }

        .password-meter-bar {
            width: 0;
            height: 100%;
            transition: width 400ms ease-in;
        }

        .password-meter-bar.level0 {
            width: 20%;
            background-color: #d00;
        }

        .password-meter-bar.level1 {
            width: 40%;
            background-color: #f50;
        }

        .password-meter-bar.level2 {
            width: 60%;
            background-color: #ff0;
        }

        .password-meter-bar.level3 {
            width: 80%;
            background-color: rgb(161, 168, 65);
        }

        .password-meter-bar.level4 {
            width: 100%;
            background-color: #393;
        }

        p {
            margin: 0;
        }

        .failed {
            color: red;
            font-size: small;

        }
        .passed {
            color: green;
            font-size: small;

        }
              .parsley-error {
            color: #B94A48;
            background-color: #F2DEDE;
            border: 1px solid #EED3D7;
        }
    </style>

</head>
<body class="bg-dark">
{% include "security/_messages.html" %}

<div class="container">
    <div class="card card-register mx-auto mt-5">
        <div class="card-header">
            <div class="sixcol er-lockup" align="center">
                <img src="{{url_for('static', filename='assets/media/logos/logo-23.png')}}" style="width: 358px; height: 69.2px;" width="100%">

            </div>
        </div>
        <div class="card-body">
            <form action="/register/" method="POST" name="register_user_form">

                {{ register_user_form.hidden_tag() }}
                {{ render_field_with_errors(register_user_form.first_name) }}
                {{ render_field_with_errors(register_user_form.last_name) }}
                {{ render_field_with_errors(register_user_form.organisation) }}
                {{ render_field_with_errors(register_user_form.email) }}
                {{ render_field_with_errors(register_user_form.password) }}
                <div class="form-group">
                    <div class="password-meter-wrap">
                        <div class="password-meter-bar"></div>

                    </div>
                    <p id="password-strength-text"></p>
                </div>
                {% if register_user_form.password_confirm %}
                    {{ render_field_with_errors(register_user_form.password_confirm) }}
                {% endif %}
                {{ render_field(register_user_form.submit, class="btn btn-primary btn-block") }}
            </form>
            <div class="text-center">
                <a class="d-block small mt-3" style="color: #5a8e89" href="/login/">Login Page</a>
                <a class="d-block small" style="color: #5a8e89" href="{{ url_for_security('forgot_password') }}">Forgot Password?</a>
            </div>


        </div>
    </div>
</div>
</body>

<!-- Bootstrap core JavaScript-->
<script src="/static/theme/vendor/jquery/jquery.min.js"></script>
<script src="/static/theme/vendor/bootstrap/js/bootstrap.bundle.min.js"></script>

<!-- Core plugin JavaScript-->
<script src="/static/theme/vendor/jquery-easing/jquery.easing.min.js"></script>
<script>
    document.getElementById("submit").setAttribute("disabled","true");
    function validatePassword() {
        let failed = "<p class='failed'>✕";
        let passed = "<p class='passed'>✓";

        var p = document.getElementById('password').value,
            errors = "";
        if (p.length < 15) {
            errors = errors + failed;
        } else {
            errors = errors + passed;
        }
        errors = errors + " Password must be at least 15 characters</p>";
        if (p.search(/[a-z]/) < 0) {
            errors = errors + failed;
        } else {
            errors = errors + passed;
        }
        errors = errors + " Password must contain at least one lowercase letter<br/>";

        if (p.search(/[A-Z]/) < 0) {
            errors = errors + failed;
        } else {
            errors = errors + passed;
        }
        errors = errors + " Password must contain at least one UPPERCASE letter<br/>";
        if (p.search(/[0-9]/) < 0) {
            errors = errors + failed;
        } else {
            errors = errors + passed;
        }
        errors = errors + " Password must contain at least one digit<br/>";
        var format = /[ `!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?~]/;
        if (!format.test(p)) {
            errors = errors + failed;
        } else {
            errors = errors + passed;
        }
        errors = errors + " Password must contain a special character<br/>";
        return errors;

    }

    'use strict';

    class PasswordMeter {
        constructor(selector) {
            this.wrappers = document.querySelectorAll(selector);
            if (this.wrappers.length > 0) {
                this.init(this.wrappers);
            }
        }

        init(wrappers) {
            wrappers.forEach(wrapper => {
                let bar = wrapper.querySelector('.password-meter-bar');
                let input = document.getElementById("password");
                let text = document.getElementById('password-strength-text');
                let strongPassword = "Strong password: avoid common words and sequences";
                document.getElementById("submit").setAttribute("disabled","true");
                let phone = document.getElementById("phone");
                let patt = /04[\d]{8}/g;

                input.addEventListener('keyup', () => {
                    document.getElementById("submit").setAttribute("disabled","true");
                    let value = input.value;
                    bar.classList.remove('level0', 'level1', 'level2', 'level3', 'level4');

                    let result = zxcvbn(value);
                    let errors = validatePassword();

                    if (errors.indexOf('✕') > -1) {
                        if (result.score > 0)
                            result.score = result.score - 1;

                        text.innerHTML = errors + "✕ " + strongPassword;
                    } else if (result.score == 4) {
                        text.innerHTML = errors + "✓ " + strongPassword;
                        document.getElementById("submit").removeAttribute("disabled");
                    } else {
                        text.innerHTML = errors + "✕ " + strongPassword;
                    }
                    let cls = `level${result.score}`;
                    bar.classList.add(cls);
                }, false);
            });
        }
    }

    document.addEventListener('DOMContentLoaded', () => {
        const passwordMeter = new PasswordMeter('.password-meter-wrap');
    }, false);
</script>
</html>