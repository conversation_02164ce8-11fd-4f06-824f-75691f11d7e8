#!/bin/bash
set -xeuo pipefail

# Get the absolute path of the directory where this script is located
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" &> /dev/null && pwd )"

cd "${SCRIPT_DIR}/hasura"

# In the Hasura docs, it's 'metadata apply' then 'migrate apply':
# https://hasura.io/docs/2.0/migrations-metadata-seeds/manage-migrations/
# I think that's wrong though, and we need to update the database
# i.e. migrate first, then do the meta data afterwards.

export HASURA_DB_NAME="${HASURA_DB_NAME:-Rezibase}"

ENDPOINT="http://localhost:${HASURA_PORT:-3020}"
./hasura-cli migrate apply --all-databases --endpoint "${ENDPOINT}"

./hasura-cli metadata reload --endpoint "${ENDPOINT}"
./hasura-cli migrate status --endpoint "${ENDPOINT}" --database-name "${HASURA_DB_NAME}"

./hasura-cli metadata apply --endpoint "${ENDPOINT}"

# We're getting errors during the first deploy. I'm not
# sure if they're real errors or not, so ignoring for now.
./hasura-cli metadata reload --endpoint "${ENDPOINT}" || true
./hasura-cli migrate status --endpoint "${ENDPOINT}" --database-name "${HASURA_DB_NAME}" || true

echo "Done updating Hasura"
