table:
  name: pas_pt_address
  schema: public
object_relationships:
  - name: pas_pt
    using:
      foreign_key_constraint_on: patientid
insert_permissions:
  - role: user
    permission:
      check:
        pas_pt:
          site_id:
            _eq: X-Hasura-Site-Id
      columns:
        - address_1
        - address_2
        - address_type_code
        - patientid
        - postcode
        - statename
        - suburb
    comment: ""
select_permissions:
  - role: user
    permission:
      columns:
        - address_1
        - address_2
        - address_type_code
        - postcode
        - statename
        - suburb
        - addressid
        - patientid
      filter:
        pas_pt:
          site_id:
            _eq: X-Hasura-Site-Id
    comment: ""
update_permissions:
  - role: user
    permission:
      columns:
        - address_1
        - address_2
        - address_type_code
        - patientid
        - postcode
        - statename
        - suburb
      filter:
        pas_pt:
          site_id:
            _eq: X-Hasura-Site-Id
      check:
        pas_pt:
          site_id:
            _eq: X-Hasura-Site-Id
    comment: ""
event_triggers:
  - name: pas_pt_address
    definition:
      enable_manual: false
      insert:
        columns: '*'
      update:
        columns: '*'
    retry_conf:
      interval_sec: 10
      num_retries: 0
      timeout_sec: 60
    webhook: '{{HASURA_WEBHOOK_URL}}'
