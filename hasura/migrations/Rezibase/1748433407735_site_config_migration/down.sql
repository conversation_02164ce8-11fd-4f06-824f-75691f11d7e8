DO
$$
    DECLARE
        -- Variables to hold the extracted values
        v_site_institution                               varchar(50);
        v_site_state                                     bpchar(3);
        v_site_logo_resourcename                         varchar(100);
        v_site_logo_showonreports                        bool;
        v_site_logo_width                                float8;
        v_site_logo_height                               float8;
        v_site_logo_left                                 float8;
        v_site_logo_top                                  float8;
        v_site_logo_textflow                             varchar(50);
        v_pas_mode_local                                 bool;
        v_emr_mode_active                                bool;
        v_healthservice_code_primary                     varchar(255);
        v_db_files_repository_path                       varchar(255);
        v_option_suppress_fer_pcmpv_value_rft_report     bool;
        v_option_printauthorisedonreports                bool;
        v_option_printauthorisedonreports_suppressnodata bool;
        v_option_printverifiedonreports                  bool;
        v_option_printverifiedonreports_suppressnodata   bool;
        v_option_fer_calc_preats_ers2021                 bool;
        v_option_fer_calc_ats_ers2021                    bool;
        v_fer_calc_changeover_date                       date;
        v_accreditation_logo_showonreports               bool;
        v_accreditation_logo_resourcename                varchar(255);
        v_accreditation_logo_width                       float8;
        v_accreditation_logo_height                      float8;
        v_accreditation_logo_left                        float8;
        v_accreditation_logo_top                         float8;
        v_site_country                                   varchar(255);
        v_site_parameter_units                           varchar(255);
        v_autoreport_algorithm                           varchar(255);
        v_cpet_pred_selection                            varchar(255);

        -- JSON objects for grouped settings
        logo_positioning_json                            json;
        accred_positioning_json                          json;
        report_options_json                              json;

    BEGIN
        -- Recreate the site_config table
        CREATE TABLE IF NOT EXISTS site_config
        (
            configid                                       int4 NOT NULL,
            site_id                                        bpchar(5),
            site_name                                      varchar(50),
            site_institution                               varchar(50),
            site_state                                     bpchar(3),
            site_logo_resourcename                         varchar(100),
            site_logo_showonreports                        bool,
            site_logo_width                                float8,
            site_logo_height                               float8,
            site_logo_left                                 float8,
            site_logo_top                                  float8,
            site_logo_textflow                             varchar(50),
            pas_mode_local                                 bool,
            emr_mode_active                                bool,
            healthservice_code_primary                     varchar(255),
            db_files_repository_path                       varchar(255),
            option_suppress_fer_pcmpv_value_rft_report     bool          DEFAULT false,
            option_printauthorisedonreports                bool,
            option_printauthorisedonreports_suppressnodata bool,
            option_printverifiedonreports                  bool,
            option_printverifiedonreports_suppressnodata   bool,
            option_fer_calc_preats_ers2021                 bool NOT NULL DEFAULT true,
            option_fer_calc_ats_ers2021                    bool NOT NULL DEFAULT false,
            fer_calc_changeover_date                       date,
            accreditation_logo_showonreports               bool,
            accreditation_logo_resourcename                varchar(255),
            accreditation_logo_width                       float8,
            accreditation_logo_height                      float8,
            accreditation_logo_left                        float8,
            accreditation_logo_top                         float8,
            module_qa_enabled                              bool,
            site_country                                   varchar(255),
            import_rft_results_enabled                     bool NOT NULL,
            site_parameter_units                           varchar(255)  DEFAULT 'TRADITIONAL'::character varying,
            autoreport_algorithm                           varchar(255),
            cpet_pred_selection                            varchar(255)
        );

        -- Extract simple value settings
        SELECT value
        INTO v_site_institution
        FROM site_settings
        WHERE name = 'site_institution'
          AND site_id = 1;

        SELECT value
        INTO v_site_state
        FROM site_settings
        WHERE name = 'site_state'
          AND site_id = 1;

        SELECT value
        INTO v_site_logo_resourcename
        FROM site_settings
        WHERE name = 'site_logo'
          AND site_id = 1;

        SELECT CASE WHEN value = 'Y' THEN true ELSE false END
        INTO v_site_logo_showonreports
        FROM site_settings
        WHERE name = 'site_logo_showonreports'
          AND site_id = 1;

        SELECT CASE WHEN value = 'Y' THEN true ELSE false END
        INTO v_pas_mode_local
        FROM site_settings
        WHERE name = 'pas_mode_local'
          AND site_id = 1;

        SELECT CASE WHEN value = 'Y' THEN true ELSE false END
        INTO v_emr_mode_active
        FROM site_settings
        WHERE name = 'emr_mode_active'
          AND site_id = 1;

        SELECT value
        INTO v_healthservice_code_primary
        FROM site_settings
        WHERE name = 'healthservice_code_primary'
          AND site_id = 1;

        SELECT value
        INTO v_db_files_repository_path
        FROM site_settings
        WHERE name = 'db_files_repository_path'
          AND site_id = 1;

        SELECT CASE WHEN value = 'Y' THEN true ELSE false END
        INTO v_accreditation_logo_showonreports
        FROM site_settings
        WHERE name = 'accreditation_logo_showonreports'
          AND site_id = 1;

        SELECT value
        INTO v_accreditation_logo_resourcename
        FROM site_settings
        WHERE name = 'accreditation_logo_resourcename'
          AND site_id = 1;

        SELECT value
        INTO v_site_country
        FROM site_settings
        WHERE name = 'site_country'
          AND site_id = 1;

        SELECT value
        INTO v_site_parameter_units
        FROM site_settings
        WHERE name = 'site_parameter_units'
          AND site_id = 1;

        SELECT value
        INTO v_autoreport_algorithm
        FROM site_settings
        WHERE name = 'autoreport_algorithm'
          AND site_id = 1;

        SELECT value
        INTO v_cpet_pred_selection
        FROM site_settings
        WHERE name = 'cpet_pred_selection'
          AND site_id = 1;

-- Extract JSON grouped settings
        SELECT value_json
        INTO logo_positioning_json
        FROM site_settings
        WHERE name = 'site_logo_positioning'
          AND site_id = 1;

        SELECT value_json
        INTO accred_positioning_json
        FROM site_settings
        WHERE name = 'accreditation_logo_positioning'
          AND site_id = 1;

        SELECT value_json
        INTO report_options_json
        FROM site_settings
        WHERE name = 'report_options'
          AND site_id = 1;

-- Extract values from JSON objects
        IF logo_positioning_json IS NOT NULL THEN
            v_site_logo_width := (logo_positioning_json ->> 'width')::float8;
            v_site_logo_height := (logo_positioning_json ->> 'height')::float8;
            v_site_logo_left := (logo_positioning_json ->> 'left')::float8;
            v_site_logo_top := (logo_positioning_json ->> 'top')::float8;
            v_site_logo_textflow := logo_positioning_json ->> 'textflow';
        END IF;

        IF accred_positioning_json IS NOT NULL THEN
            v_accreditation_logo_width := (accred_positioning_json ->> 'width')::float8;
            v_accreditation_logo_height := (accred_positioning_json ->> 'height')::float8;
            v_accreditation_logo_left := (accred_positioning_json ->> 'left')::float8;
            v_accreditation_logo_top := (accred_positioning_json ->> 'top')::float8;
        END IF;

        IF report_options_json IS NOT NULL THEN
            v_option_suppress_fer_pcmpv_value_rft_report :=
                    (report_options_json ->> 'suppress_fer_pcmpv_value_rft_report')::bool;
            v_option_printauthorisedonreports := (report_options_json ->> 'print_authorised_on_reports')::bool;
            v_option_printauthorisedonreports_suppressnodata :=
                    (report_options_json ->> 'print_authorised_suppress_no_data')::bool;
            v_option_printverifiedonreports := (report_options_json ->> 'print_verified_on_reports')::bool;
            v_option_printverifiedonreports_suppressnodata :=
                    (report_options_json ->> 'print_verified_suppress_no_data')::bool;
            v_option_fer_calc_preats_ers2021 := (report_options_json ->> 'fer_calc_preats_ers2021')::bool;
            v_option_fer_calc_ats_ers2021 := (report_options_json ->> 'fer_calc_ats_ers2021')::bool;
            v_fer_calc_changeover_date := (report_options_json ->> 'fer_calc_changeover_date')::date;
        END IF;

        -- Clear existing site_config data (if any)
        DELETE FROM site_config;

-- Insert the reconstructed row into site_config
        INSERT INTO site_config (site_id,
                                 site_name,
                                 site_institution,
                                 site_state,
                                 site_logo_resourcename,
                                 site_logo_showonreports,
                                 site_logo_width,
                                 site_logo_height,
                                 site_logo_left,
                                 site_logo_top,
                                 site_logo_textflow,
                                 pas_mode_local,
                                 emr_mode_active,
                                 healthservice_code_primary,
                                 db_files_repository_path,
                                 option_suppress_fer_pcmpv_value_rft_report,
                                 option_printauthorisedonreports,
                                 option_printauthorisedonreports_suppressnodata,
                                 option_printverifiedonreports,
                                 option_printverifiedonreports_suppressnodata,
                                 option_fer_calc_preats_ers2021,
                                 option_fer_calc_ats_ers2021,
                                 fer_calc_changeover_date,
                                 accreditation_logo_showonreports,
                                 accreditation_logo_resourcename,
                                 accreditation_logo_width,
                                 accreditation_logo_height,
                                 accreditation_logo_left,
                                 accreditation_logo_top,
                                 site_country,
                                 site_parameter_units,
                                 autoreport_algorithm,
                                 cpet_pred_selection)
        VALUES ('1'::bpchar(5), -- site_id (assuming site_id 1 maps to '1')
                v_site_institution, -- site_name (using institution as name)
                v_site_institution,
                v_site_state::bpchar(3),
                v_site_logo_resourcename,
                v_site_logo_showonreports,
                v_site_logo_width,
                v_site_logo_height,
                v_site_logo_left,
                v_site_logo_top,
                v_site_logo_textflow,
                v_pas_mode_local,
                v_emr_mode_active,
                v_healthservice_code_primary,
                v_db_files_repository_path,
                COALESCE(v_option_suppress_fer_pcmpv_value_rft_report, false),
                v_option_printauthorisedonreports,
                v_option_printauthorisedonreports_suppressnodata,
                v_option_printverifiedonreports,
                v_option_printverifiedonreports_suppressnodata,
                COALESCE(v_option_fer_calc_preats_ers2021, true), -- Default from original schema
                COALESCE(v_option_fer_calc_ats_ers2021, false), -- Default from original schema
                v_fer_calc_changeover_date,
                v_accreditation_logo_showonreports,
                v_accreditation_logo_resourcename,
                v_accreditation_logo_width,
                v_accreditation_logo_height,
                v_accreditation_logo_left,
                v_accreditation_logo_top,
                v_site_country,
                COALESCE(v_site_parameter_units, 'TRADITIONAL'), -- Default from original schema
                v_autoreport_algorithm,
                v_cpet_pred_selection);

        -- Clean up the site_settings entries that were migrated
        DELETE
        FROM site_settings
        WHERE site_id = 1
          AND name IN (
                       'site_institution',
                       'site_state',
                       'site_logo',
                       'site_logo_showonreports',
                       'pas_mode_local',
                       'emr_mode_active',
                       'healthservice_code_primary',
                       'db_files_repository_path',
                       'accreditation_logo_showonreports',
                       'accreditation_logo_resourcename',
                       'site_country',
                       'site_parameter_units',
                       'autoreport_algorithm',
                       'cpet_pred_selection',
                       'site_logo_positioning',
                       'accreditation_logo_positioning',
                       'report_options'
            );

        RAISE NOTICE 'Down migration completed successfully. Data restored to site_config table.';

    EXCEPTION
        WHEN OTHERS THEN
            RAISE NOTICE 'Error during down migration: %', SQLERRM;
            RAISE;
    END
$$;