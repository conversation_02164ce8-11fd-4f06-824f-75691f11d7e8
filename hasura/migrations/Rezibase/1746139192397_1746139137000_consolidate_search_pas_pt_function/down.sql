
DROP FUNCTION IF EXISTS public.search_pas_pt(text, text[], text);

-- Step 2: Recreate the original function with gender filter
CREATE OR REPLACE FUNCTION public.search_pas_pt(
  search_text text,
  gender_filter text[] DEFAULT NULL::text[]
)
RETURNS SETOF pas_pt_search
LANGUAGE plpgsql
STABLE
AS $function$
BEGIN
  IF (search_text IS NULL OR search_text = '')
     AND (gender_filter IS NULL OR array_length(gender_filter, 1) IS NULL)
  THEN
    RETURN QUERY
    SELECT *
    FROM pas_pt_search;
  ELSE
    RETURN QUERY
    SELECT *
    FROM pas_pt_search
    WHERE (
      (search_text IS NULL OR search_text = '')
      OR (search_vector @@ plainto_tsquery('english', search_text))
      OR (search_vector @@ to_tsquery(
          'english',
          regexp_replace(
            regexp_replace(trim(search_text), '[\s]+', ' ', 'g'),
            '([^\s]+)',
            '\1:*',
            'g'
          )
        ))
      OR (
        search_text IS NOT NULL
        AND length(search_text) > 0
        AND trigram_search_text % search_text
        AND (
          CASE
            WHEN length(search_text) <= 3 THEN similarity(trigram_search_text, search_text) > 0.1
            WHEN length(search_text) <= 5 THEN similarity(trigram_search_text, search_text) > 0.2
            ELSE similarity(trigram_search_text, search_text) > 0.3
          END
        )
      )
    )
    AND (
      gender_filter IS NULL
      OR array_length(gender_filter, 1) IS NULL
      OR gender_code = ANY(gender_filter)
    )
    ORDER BY
      CASE
        WHEN search_text IS NOT NULL AND search_text <> '' THEN similarity(trigram_search_text, search_text)
        ELSE 1
      END DESC,
      CASE
        WHEN search_text IS NOT NULL AND search_text <> '' THEN ts_rank(
          search_vector,
          to_tsquery(
            'english',
            regexp_replace(
              regexp_replace(trim(search_text), '[\s]+', ' ', 'g'),
              '([^\s]+)',
              '\1:*',
              'g'
            )
          )
        )
        ELSE 0
      END DESC;
  END IF;
END;
$function$;

-- Step 3: Create the basic search function
CREATE OR REPLACE FUNCTION public.search_pas_pt(search_text text)
 RETURNS SETOF pas_pt_search
 LANGUAGE plpgsql
 STABLE
AS $function$
BEGIN
  IF search_text IS NULL OR search_text = '' THEN
    RETURN QUERY SELECT * FROM pas_pt_search;
  ELSE
    RETURN QUERY
    SELECT *
    FROM pas_pt_search
    WHERE search_vector @@ to_tsquery('english', search_text);
  END IF;
END;
$function$;

