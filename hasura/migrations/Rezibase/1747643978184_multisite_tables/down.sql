-- Not adding the removed tables back if you have to please refer postgres_heavy_scripts

-- <PERSON><PERSON>ve added site_ids
ALTER TABLE list_language DROP COLUMN "site_id";
ALTER TABLE list_nationality DROP COLUMN "site_id";
ALTER TABLE prefs_fields DROP COLUMN "site_id";
ALTER TABLE doctors DROP COLUMN "site_id";
ALTER TABLE sidebar_modules DROP COLUMN "site_id";
ALTER TABLE prefs_pred_new DROP COLUMN "site_id";
