http:
  routers:
    catchall:
      rule: "PathPrefix(`/`)"
      entryPoints:
        - web
      service: app-service
      middlewares: 
        - "my-compress-middleware"
      priority: 1 # Lower priority, acts as a fallback

  services:
    app-service:
      loadBalancer:
        servers:
          - url: "http://localhost:3010"

  middlewares: 
    my-compress-middleware:
      compress: {} # <--- Enable compression with default settings