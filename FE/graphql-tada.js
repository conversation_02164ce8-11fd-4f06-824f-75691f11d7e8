import {generateOutput} from '@gql.tada/cli-utils';
import dotenvFlow from 'dotenv-flow';
import {readFile, unlink, writeFile} from 'node:fs/promises';
import {tmpdir} from 'node:os';
import {join} from 'node:path';

const tsConfig = JSON.parse((await readFile('./tsconfig.json')).toString() ?? '{}');

dotenvFlow.config();

const tsconfigContent = JSON.stringify(
  {
    ...tsConfig,
    compilerOptions: {
      ...tsConfig.compilerOptions,
      plugins: [
        ...(tsConfig.compilerOptions.plugins || []),
        {
          name: 'gql.tada/ts-plugin',
          schema: {
            url: process.env.VITE_APP_HASURA_URL,
            headers: {
              'content-type': 'application/json',
              'x-hasura-admin-secret': process.env.HASURA_GRAPHQL_ADMIN_SECRET,
            },
          },
        },
      ],
    },
  },
  undefined,
  2
);

const tsconfigFilePath = join(tmpdir(), `tsconfig-${Math.floor(Math.random() * 1000)}.json`);

try {
  await writeFile(tsconfigFilePath, tsconfigContent);

  await generateOutput({
    output: './src/graphql-env.d.ts',
    disablePreprocessing: false,
    tsconfig: tsconfigFilePath,
  });
} catch (e) {
} finally {
  await unlink(tsconfigFilePath);
}
