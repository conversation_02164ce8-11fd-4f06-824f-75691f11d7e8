import { useLocalStorage } from '@mantine/hooks';
import { useLocation } from 'react-router';

// Module tour keys mapping
const moduleToursConfig = {
  '/patients': 'patients-tour-completed',
  '/quality-control': 'qc-tour-completed',
  '/contacts': 'contacts-tour-completed',
  '/normal-values': 'normal-values-tour-completed',
  '/reports': 'reports-tour-completed',
  '/pdf-import': 'pdf-import-tour-completed',
};

export const useOnboardingTour = () => {
  const location = useLocation();
  
  const [hasCompletedTour, setHasCompletedTour] = useLocalStorage({
    key: 'rezibase:onboarding-tour-completed',
    defaultValue: false,
  });

  const resetTour = () => {
    setHasCompletedTour(false);
    // Reload the page to restart the tour
    window.location.reload();
  };

  const completeTour = () => {
    setHasCompletedTour(true);
  };

  // Module-specific tour management
  const currentModuleKey = moduleToursConfig[location.pathname as keyof typeof moduleToursConfig];
  
  const [hasCompletedModuleTour, setHasCompletedModuleTour] = useLocalStorage({
    key: currentModuleKey || 'default-module-tour',
    defaultValue: false,
  });

  const resetModuleTour = () => {
    if (currentModuleKey) {
      setHasCompletedModuleTour(false);
      // Reload the page to restart the module tour
      window.location.reload();
    }
  };

  const resetAllTours = () => {
    // Reset generic tour
    setHasCompletedTour(false);
    
    // Reset all module tours
    Object.values(moduleToursConfig).forEach(key => {
      localStorage.removeItem(`mantine-${key}`);
    });
    
    // Reload the page
    window.location.reload();
  };

  return {
    hasCompletedTour,
    resetTour,
    completeTour,
    hasCompletedModuleTour,
    resetModuleTour,
    resetAllTours,
    currentModuleKey,
  };
};
