import clsx from 'clsx';
import * as React from 'react';
import {createElement} from 'react';
import {NavLink} from 'react-router';

import {useQuery} from '@apollo/client';

import CallingIconly from '@/assets/iconly/Calling.svg?react';
import ClipboardMenuIconly from '@/assets/iconly/ClipboardMenu.svg?react';
import SparksAi from '@/assets/iconly/SparksAi.svg?react';
import DatabaseIconly from '@/assets/iconly/DataBase.svg?react';
import DeliverySearchIconly from '@/assets/iconly/DeliverySearch.svg?react';
import DocumentCopyIconly from '@/assets/iconly/DocumentCopy.svg?react';
import GridInterfaceIconly from '@/assets/iconly/GridInterface.svg?react';
import UserSearchIconly from '@/assets/iconly/UserSearch.svg?react';
import {ChevronLeftIconly} from '@/components/icons/ChevronLeftIconly';
import {ChevronRightIconly} from '@/components/icons/ChevronRightIconly';
import {Sidebar, SidebarMenu, SidebarMenuButton, SidebarMenuItem, SidebarRail} from '@/components/ui/sidebar';
import {GET_SIDEBAR_MODULES} from '@/graphql/sidebar.ts';

import {useSidebar} from './ui/sidebar.tsx';

const iconsMap: Record<string, typeof GridInterfaceIconly> = {
  'layout-dashboard': GridInterfaceIconly,
  'notebook-pen': ClipboardMenuIconly,
  'clipboard-pen': DocumentCopyIconly,
  'notebook-tabs': CallingIconly,
  user: UserSearchIconly,
  database: DatabaseIconly,
  'package-search': DeliverySearchIconly,
  'normal-values': ClipboardMenuIconly,
  'pdf-import': SparksAi,
};

const urlToIdMap: Record<string, string> = {
  '/dashboard': 'dashboard',
  '/bookings': 'bookings',
  '/patients': 'patients',
  '/reports': 'reports',
  '/contacts': 'contacts',
  '/data': 'data',
  '/quality-control': 'quality-control',
  '/pdf-import': 'pdf-import',
  '/normal-values': 'normal-values',
};

export function AppSidebar({...props}: React.ComponentProps<typeof Sidebar>) {
  const {state, toggleSidebar} = useSidebar();
  const {data} = useQuery(GET_SIDEBAR_MODULES);

  return (
    <Sidebar
      collapsible="icon"
      {...props}
    >
      <div
        data-sidebar="header"
        className={clsx(
          'bg-brand-900 flex h-[88px] flex-col gap-2',
          state === 'collapsed' ? 'p-0 pt-2' : 'p-5 pb-4.5'
        )}
      >
        {state === 'collapsed' ? (
          <img
            className="h-12 w-12 p-2"
            alt="rezibase-logo"
            src="/logo-collapsed.png"
          />
        ) : (
          <img
            className="w-28"
            alt="rezibase-logo"
            src="/logo-white.png"
          />
        )}
      </div>

      <div
        data-sidebar="content"
        className="bg-brand-900 flex min-h-0 flex-1 flex-col gap-2 overflow-auto group-data-[collapsible=icon]:overflow-hidden"
      >
        <SidebarMenu>
          {data?.sidebar_modules?.map((item) => (
            <SidebarMenuItem id={urlToIdMap[item?.url]} key={item.title}>
              <NavLink
                to={item.url}
                className={({isActive}) =>
                  clsx(item.url && item.url !== '#' && isActive && `*:bg-brand-600 item-active`)
                }
              >
                <SidebarMenuButton
                  className="relative"
                  tooltip={item.title}
                >
                  <div className="[.item-active_&]:bg-brand2-400 absolute inset-y-0 left-0 w-1" />
                  {iconsMap[item.icon] && createElement(iconsMap[item.icon])}
                  <span className='uppercase text-xs font-semibold'>{item.title}</span>
                </SidebarMenuButton>
              </NavLink>
            </SidebarMenuItem>
          ))}
        </SidebarMenu>
      </div>
      <SidebarRail />
      <div className="relative">
        <div
          onClick={toggleSidebar}
          className={clsx(
            'bg-brand2-400 absolute -right-4 bottom-8 z-50 cursor-pointer rounded-md p-1.5 text-white shadow-lg *:size-5'
          )}
        >
          {state === 'expanded' ? (
            <ChevronLeftIconly className="h-6 w-6" />
          ) : (
            <ChevronRightIconly className="h-6 w-6" />
          )}
        </div>
      </div>
    </Sidebar>
  );
}
