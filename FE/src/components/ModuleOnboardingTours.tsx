import React, { useEffect, useState } from 'react';
import { TourProvider, useTour } from '@reactour/tour';
import { useLocalStorage } from '@mantine/hooks';
import { useLocation } from 'react-router';

// Tour step interface
interface TourStep {
  selector: string;
  content: React.ReactNode;
  position?: 'top' | 'bottom' | 'left' | 'right';
  action?: (elem: Element | null) => void;
  actionAfter?: (elem: Element | null) => void;
}

// Module tour configurations
const moduleToursConfig = {
  '/patients/all': {
    key: 'patients-tour-completed',
    steps: [
      {
        selector: '#onboarding-root',
        content: (
          <div className="p-4">
            <h2 className="text-xl font-bold text-gray-800 mb-3">
              Welcome to Patients Module! 👥
            </h2>
            <p className="text-gray-600 mb-4">
              This module allows you to manage patient records, demographics, and medical history.
              Let's explore the key features together.
            </p>
            <p className="text-sm text-gray-500">
              You can add new patients, search existing ones, and view detailed patient information.
            </p>
          </div>
        ),
        position: 'center' as const,
      },
      {
        selector: '#add-patient',
        content: (
          <div className="p-4">
            <h3 className="text-lg font-semibold text-gray-800 mb-2">
              Add New Patient
            </h3>
            <p className="text-gray-600 mb-3">
              Click this button to add a new patient to the system. This will open a form
              where you can enter patient demographics and contact information.
            </p>
            <p className="text-sm text-blue-600 bg-blue-50 p-2 rounded">
              <strong>Try it:</strong> Click the "Add Patient" button to see the form.
            </p>
          </div>
        ),
        position: 'left' as const,
        action: (elem) => {
          if (elem) {
            elem.classList.add('tour-highlight');
          }
        },
        actionAfter: (elem) => {
          if (elem) {
            elem.classList.remove('tour-highlight');
          }
        },
      },
      {
        selector: '.react-aria-Modal',
        content: (
          <div className="p-4">
            <h3 className="text-lg font-semibold text-gray-800 mb-2">
              Patient Information Form
            </h3>
            <p className="text-gray-600 mb-3">
              This form contains all the fields needed to add a new patient, including:
            </p>
            <ul className="text-sm text-gray-600 mb-3 list-disc list-inside">
              <li>Personal details (name, DOB, gender)</li>
              <li>Contact information (phone, email, address)</li>
              <li>Medical Record Numbers (MRN)</li>
              <li>Healthcare service information</li>
            </ul>
            <p className="text-sm text-amber-600 bg-amber-50 p-2 rounded">
              Fill out the required fields marked with an asterisk (*) to add a patient.
            </p>
          </div>
        ),
        position: 'right' as const,
      },
      {
        selector: '#onboarding-root',
        content: (
          <div className="p-4">
            <h3 className="text-lg font-semibold text-gray-800 mb-2">
              Patient List
            </h3>
            <p className="text-gray-600 mb-3">
              After closing the modal, you'll see the patient list. Click on any patient
              row to view their detailed information, including:
            </p>
            <ul className="text-sm text-gray-600 mb-3 list-disc list-inside">
              <li>Test results and medical history</li>
              <li>Appointment scheduling</li>
              <li>Contact information management</li>
              <li>Document uploads and reports</li>
            </ul>
            <p className="text-sm text-green-600 bg-green-50 p-2 rounded">
              <strong>Next:</strong> Try clicking on a patient to explore their details!
            </p>
          </div>
        ),
        position: 'center' as const,
      },
    ],
  },
  '/quality-control': {
    key: 'qc-tour-completed',
    steps: [
      {
        selector: '#onboarding-root',
        content: (
          <div className="p-4">
            <h2 className="text-xl font-bold text-gray-800 mb-3">
              Welcome to Quality Control! 🔬
            </h2>
            <p className="text-gray-600 mb-4">
              The Quality Control module helps you monitor and manage QC processes
              to ensure accurate test results. Let's explore the key features.
            </p>
            <p className="text-sm text-gray-500">
              You can add sessions, monitor parameter values, and manage control intervals.
            </p>
          </div>
        ),
        position: 'top' as const,
      },
      {
        selector: '[data-testid="add-session-button"]',
        content: (
          <div className="p-4">
            <h3 className="text-lg font-semibold text-gray-800 mb-2">
              Add QC Session
            </h3>
            <p className="text-gray-600 mb-3">
              Click this button to add a new quality control session. This allows you
              to record parameter values for your QC measurements.
            </p>
            <p className="text-sm text-blue-600 bg-blue-50 p-2 rounded">
              <strong>Try it:</strong> Click "Add Session" to open the session form.
            </p>
          </div>
        ),
        position: 'left' as const,
        action: (elem) => {
          if (elem) {
            elem.classList.add('tour-highlight');
          }
        },
        actionAfter: (elem) => {
          if (elem) {
            elem.classList.remove('tour-highlight');
          }
        },
      },
      {
        selector: '.react-aria-Modal',
        content: (
          <div className="p-4">
            <h3 className="text-lg font-semibold text-gray-800 mb-2">
              Session Parameters
            </h3>
            <p className="text-gray-600 mb-3">
              In this form, you can add parameter values for your QC session. Enter the
              measured values for each parameter to track quality control performance.
            </p>
            <p className="text-sm text-amber-600 bg-amber-50 p-2 rounded">
              Each parameter has specific ranges and control limits for monitoring.
            </p>
          </div>
        ),
        position: 'right' as const,
      },
      {
        selector: '#onboarding-root',
        content: (
          <div className="p-4">
            <h3 className="text-lg font-semibold text-gray-800 mb-2">
              Understanding Alert Colors
            </h3>
            <p className="text-gray-600 mb-3">
              After adding sessions, you'll see data with different color indicators:
            </p>
            <div className="space-y-2 mb-3">
              <div className="flex items-center gap-2">
                <div className="w-4 h-4 bg-red-700 rounded"></div>
                <span className="text-sm text-gray-600"><strong>Red:</strong> Open alerts (violations)</span>
              </div>
              <div className="flex items-center gap-2">
                <div className="w-4 h-4 bg-amber-500 rounded"></div>
                <span className="text-sm text-gray-600"><strong>Yellow:</strong> Control values</span>
              </div>
              <div className="flex items-center gap-2">
                <div className="w-4 h-4 bg-blue-500 rounded"></div>
                <span className="text-sm text-gray-600"><strong>Blue:</strong> Closed alerts</span>
              </div>
              <div className="flex items-center gap-2">
                <div className="w-4 h-4 bg-green-500 rounded"></div>
                <span className="text-sm text-gray-600"><strong>Green:</strong> Normal values</span>
              </div>
            </div>
          </div>
        ),
        position: 'top' as const,
      },
      {
        selector: '#onboarding-root',
        content: (
          <div className="p-4">
            <h3 className="text-lg font-semibold text-gray-800 mb-2">
              Control Intervals
            </h3>
            <p className="text-gray-600 mb-3">
              You can select multiple cells in the data table to form control intervals.
              This helps establish reference ranges for your QC parameters.
            </p>
            <p className="text-sm text-blue-600 bg-blue-50 p-2 rounded">
              <strong>Try it:</strong> Select multiple cells and click "Add Control Interval".
            </p>
          </div>
        ),
        position: 'top' as const,
      },
      {
        selector: '[data-testid="add-control-interval-button"]',
        content: (
          <div className="p-4">
            <h3 className="text-lg font-semibold text-gray-800 mb-2">
              Add Control Interval
            </h3>
            <p className="text-gray-600 mb-3">
              After selecting data points, click this button to create control intervals
              based on your selected measurements.
            </p>
          </div>
        ),
        position: 'left' as const,
        action: (elem) => {
          if (elem) {
            elem.classList.add('tour-highlight');
          }
        },
        actionAfter: (elem) => {
          if (elem) {
            elem.classList.remove('tour-highlight');
          }
        },
      },
      {
        selector: '.react-aria-Modal',
        content: (
          <div className="p-4">
            <h3 className="text-lg font-semibold text-gray-800 mb-2">
              Control Interval Settings
            </h3>
            <p className="text-gray-600 mb-3">
              This dialog allows you to configure control intervals based on your
              selected data points. Review the calculated statistics before confirming.
            </p>
          </div>
        ),
        position: 'right' as const,
      },
      {
        selector: '#onboarding-root',
        content: (
          <div className="p-4">
            <h2 className="text-xl font-bold text-gray-800 mb-3">
              Parameter View Available! 📊
            </h2>
            <p className="text-gray-600 mb-4">
              Don't forget to check out the "Parameter View" tab in the top right.
              This provides an alternative view of your QC data organized by parameters.
            </p>
            <p className="text-sm text-green-600 bg-green-50 p-2 rounded">
              You're now ready to manage quality control effectively!
            </p>
          </div>
        ),
        position: 'top' as const,
      },
    ],
  },
  '/contacts': {
    key: 'contacts-tour-completed',
    steps: [
      {
        selector: '#onboarding-root',
        content: (
          <div className="p-4">
            <h2 className="text-xl font-bold text-gray-800 mb-3">
              Welcome to Contacts! 📞
            </h2>
            <p className="text-gray-600 mb-4">
              The Contacts module helps you manage your directory of healthcare providers,
              referring doctors, and other professional contacts.
            </p>
            <p className="text-sm text-gray-500">
              Keep track of doctor information, contact details, and referral relationships.
            </p>
          </div>
        ),
        position: 'top' as const,
      },
      {
        selector: '.react-aria-Button',
        content: (
          <div className="p-4">
            <h3 className="text-lg font-semibold text-gray-800 mb-2">
              Add New Contact
            </h3>
            <p className="text-gray-600 mb-3">
              Click this button to add a new healthcare provider or doctor to your
              contact directory. This is essential for managing referrals and communications.
            </p>
            <p className="text-sm text-blue-600 bg-blue-50 p-2 rounded">
              <strong>Try it:</strong> Click "Add Contact" to open the contact form.
            </p>
          </div>
        ),
        position: 'left' as const,
        action: (elem) => {
          if (elem) {
            elem.classList.add('tour-highlight');
          }
        },
        actionAfter: (elem) => {
          if (elem) {
            elem.classList.remove('tour-highlight');
          }
        },
      },
      {
        selector: '.react-aria-Modal',
        content: (
          <div className="p-4">
            <h3 className="text-lg font-semibold text-gray-800 mb-2">
              Contact Information Form
            </h3>
            <p className="text-gray-600 mb-3">
              This form contains all the fields needed to add a healthcare provider contact:
            </p>
            <ul className="text-sm text-gray-600 mb-3 list-disc list-inside">
              <li>Personal details (title, name)</li>
              <li>Professional information (provider number, specialty)</li>
              <li>Contact details (phone, email, fax)</li>
              <li>Address and clinic information</li>
            </ul>
            <p className="text-sm text-amber-600 bg-amber-50 p-2 rounded">
              Complete the form to add the contact to your directory.
            </p>
          </div>
        ),
        position: 'right' as const,
      },
      {
        selector: '#onboarding-root',
        content: (
          <div className="p-4">
            <h2 className="text-xl font-bold text-gray-800 mb-3">
              Contact Management Complete! ✅
            </h2>
            <p className="text-gray-600 mb-4">
              You can now search, add, and manage healthcare provider contacts.
              Click on any contact in the list to view and edit their detailed information.
            </p>
            <p className="text-sm text-green-600 bg-green-50 p-2 rounded">
              Your contact directory is ready for managing professional relationships!
            </p>
          </div>
        ),
        position: 'top' as const,
      },
    ],
  },
  '/normal-values': {
    key: 'normal-values-tour-completed',
    steps: [
      {
        selector: '#onboarding-root',
        content: (
          <div className="p-4">
            <h2 className="text-xl font-bold text-gray-800 mb-3">
              Welcome to Normal Values! 📏
            </h2>
            <p className="text-gray-600 mb-4">
              This module helps you configure and manage normal reference ranges
              for laboratory tests. Set up equations and parameters for accurate test interpretation.
            </p>
            <p className="text-sm text-gray-500">
              Manage test libraries, equations, and reference value preferences.
            </p>
          </div>
        ),
        position: 'top' as const,
      },
      {
        selector: '[data-state="active"]',
        content: (
          <div className="p-4">
            <h3 className="text-lg font-semibold text-gray-800 mb-2">
              Library vs Preferences
            </h3>
            <p className="text-gray-600 mb-3">
              The Normal Values module has two main sections:
            </p>
            <ul className="text-sm text-gray-600 mb-3 list-disc list-inside">
              <li><strong>Library:</strong> Browse and manage test equations and sources</li>
              <li><strong>Preferences:</strong> Configure your site-specific normal value settings</li>
            </ul>
            <p className="text-sm text-blue-600 bg-blue-50 p-2 rounded">
              Switch between tabs to explore both sections.
            </p>
          </div>
        ),
        position: 'bottom' as const,
      },
      {
        selector: '#onboarding-root',
        content: (
          <div className="p-4">
            <h3 className="text-lg font-semibold text-gray-800 mb-2">
              Test Equations & Sources
            </h3>
            <p className="text-gray-600 mb-3">
              In the Library section, you can:
            </p>
            <ul className="text-sm text-gray-600 mb-3 list-disc list-inside">
              <li>View available test equations and reference sources</li>
              <li>Add new sources for specific tests</li>
              <li>Calculate predicted values using different equations</li>
              <li>Manage equation parameters and ranges</li>
            </ul>
          </div>
        ),
        position: 'top' as const,
      },
      {
        selector: '#onboarding-root',
        content: (
          <div className="p-4">
            <h2 className="text-xl font-bold text-gray-800 mb-3">
              Normal Values Setup Complete! 📊
            </h2>
            <p className="text-gray-600 mb-4">
              You can now manage reference ranges, equations, and normal value preferences.
              This ensures accurate interpretation of laboratory test results.
            </p>
            <p className="text-sm text-green-600 bg-green-50 p-2 rounded">
              Your normal values configuration is ready for precise test analysis!
            </p>
          </div>
        ),
        position: 'top' as const,
      },
    ],
  },
  '/reports': {
    key: 'reports-tour-completed',
    steps: [
      {
        selector: '#onboarding-root',
        content: (
          <div className="p-4">
            <h2 className="text-xl font-bold text-gray-800 mb-3">
              Welcome to Reports! 📋
            </h2>
            <p className="text-gray-600 mb-4">
              The Reports module allows you to generate, view, and manage laboratory
              reports and test results. Create professional reports for patients and referring doctors.
            </p>
            <p className="text-sm text-gray-500">
              Manage report statuses, generate batch reports, and customize report templates.
            </p>
          </div>
        ),
        position: 'top' as const,
      },
      {
        selector: '.react-aria-TabList',
        content: (
          <div className="p-4">
            <h3 className="text-lg font-semibold text-gray-800 mb-2">
              Report Categories
            </h3>
            <p className="text-gray-600 mb-3">
              Reports are organized by test type and laboratory:
            </p>
            <ul className="text-sm text-gray-600 mb-3 list-disc list-inside">
              <li><strong>RL:</strong> Respiratory Laboratory reports</li>
              <li><strong>SS:</strong> Sleep Study reports</li>
              <li><strong>CPAP:</strong> CPAP Clinic reports</li>
            </ul>
            <p className="text-sm text-blue-600 bg-blue-50 p-2 rounded">
              Switch between tabs to view different report types.
            </p>
          </div>
        ),
        position: 'bottom' as const,
      },
      {
        selector: '#onboarding-root',
        content: (
          <div className="p-4">
            <h3 className="text-lg font-semibold text-gray-800 mb-2">
              Report Management Features
            </h3>
            <p className="text-gray-600 mb-3">
              In the Reports module, you can:
            </p>
            <ul className="text-sm text-gray-600 mb-3 list-disc list-inside">
              <li>Filter reports by status (Unreported, Draft, Final, etc.)</li>
              <li>Search for specific patient reports</li>
              <li>Generate individual or batch reports</li>
              <li>Update report statuses and add comments</li>
              <li>Print or export reports in various formats</li>
            </ul>
          </div>
        ),
        position: 'top' as const,
      },
      {
        selector: '#onboarding-root',
        content: (
          <div className="p-4">
            <h2 className="text-xl font-bold text-gray-800 mb-3">
              Reports Module Ready! 🎯
            </h2>
            <p className="text-gray-600 mb-4">
              You can now efficiently manage laboratory reports, track report statuses,
              and generate professional documentation for patients and healthcare providers.
            </p>
            <p className="text-sm text-green-600 bg-green-50 p-2 rounded">
              Your reporting workflow is optimized for professional laboratory operations!
            </p>
          </div>
        ),
        position: 'top' as const,
      },
    ],
  },
  '/pdf-import': {
    key: 'pdf-import-tour-completed',
    steps: [
      {
        selector: '#onboarding-root',
        content: (
          <div className="p-4">
            <h2 className="text-xl font-bold text-gray-800 mb-3">
              Welcome to Magic Import! ✨
            </h2>
            <p className="text-gray-600 mb-4">
              Magic Import uses AI to automatically extract patient data and test results
              from PDF documents. This saves time and reduces manual data entry errors.
            </p>
            <p className="text-sm text-gray-500">
              Simply upload a PDF and let our AI do the work!
            </p>
          </div>
        ),
        position: 'top' as const,
      },
      {
        selector: '.border-dashed',
        content: (
          <div className="p-4">
            <h3 className="text-lg font-semibold text-gray-800 mb-2">
              PDF Upload Area
            </h3>
            <p className="text-gray-600 mb-3">
              Drag and drop your PDF files here, or click to browse and select files.
              The AI will automatically extract:
            </p>
            <ul className="text-sm text-gray-600 mb-3 list-disc list-inside">
              <li>Patient demographic information</li>
              <li>Test results and measurements</li>
              <li>Report images and charts</li>
              <li>Test dates and parameters</li>
            </ul>
            <p className="text-sm text-blue-600 bg-blue-50 p-2 rounded">
              <strong>Try it:</strong> Upload a PDF to see the magic happen!
            </p>
          </div>
        ),
        position: 'bottom' as const,
        action: (elem) => {
          if (elem) {
            elem.classList.add('tour-highlight');
          }
        },
        actionAfter: (elem) => {
          if (elem) {
            elem.classList.remove('tour-highlight');
          }
        },
      },
      {
        selector: 'button[type="submit"]',
        content: (
          <div className="p-4">
            <h3 className="text-lg font-semibold text-gray-800 mb-2">
              Extract PDF Data
            </h3>
            <p className="text-gray-600 mb-3">
              After selecting a PDF file, click this button to start the AI extraction process.
              The system will analyze the document and extract relevant information.
            </p>
            <p className="text-sm text-amber-600 bg-amber-50 p-2 rounded">
              Processing may take a few moments depending on document complexity.
            </p>
          </div>
        ),
        position: 'top' as const,
        action: (elem) => {
          if (elem) {
            elem.classList.add('tour-highlight');
          }
        },
        actionAfter: (elem) => {
          if (elem) {
            elem.classList.remove('tour-highlight');
          }
        },
      },
      {
        selector: '.grid.grid-cols-1',
        content: (
          <div className="p-4">
            <h3 className="text-lg font-semibold text-gray-800 mb-2">
              Sample Templates
            </h3>
            <p className="text-gray-600 mb-3">
              Download sample PDF templates to test the Magic Import functionality.
              These templates show the supported formats and layouts for different devices.
            </p>
            <p className="text-sm text-green-600 bg-green-50 p-2 rounded">
              Use these samples to understand what types of PDFs work best with Magic Import.
            </p>
          </div>
        ),
        position: 'top' as const,
      },
      {
        selector: '#onboarding-root',
        content: (
          <div className="p-4">
            <h2 className="text-xl font-bold text-gray-800 mb-3">
              Magic Import Ready! 🚀
            </h2>
            <p className="text-gray-600 mb-4">
              You can now use AI-powered document processing to automatically extract
              patient data from PDF reports. This streamlines your data entry workflow significantly.
            </p>
            <p className="text-sm text-green-600 bg-green-50 p-2 rounded">
              Your intelligent document processing system is ready to save you time!
            </p>
          </div>
        ),
        position: 'top' as const,
      },
    ],
  },
};

// Module tour component
const ModuleTourComponent: React.FC = () => {
  const location = useLocation();
  const { setIsOpen, setSteps } = useTour();
  const [hasCompletedGenericTour] = useLocalStorage({
    key: 'rezibase:onboarding-tour-completed',
    defaultValue: false,
  });

  // Get current module tour config
  const currentModuleConfig = moduleToursConfig[location.pathname as keyof typeof moduleToursConfig];

  const [hasCompletedModuleTour, setHasCompletedModuleTour] = useLocalStorage({
    key: currentModuleConfig?.key || 'default-tour',
    defaultValue: false,
  });

  useEffect(() => {
    // Only show module tour if:
    // 1. Generic tour is completed
    // 2. Current module has a tour configuration
    // 3. Module tour hasn't been completed yet
    if (hasCompletedGenericTour && currentModuleConfig && !hasCompletedModuleTour) {
      // Small delay to ensure DOM elements are rendered
      const timer = setTimeout(() => {
        startModuleTour();
      }, 1000);

      return () => clearTimeout(timer);
    }
  }, [location.pathname, hasCompletedGenericTour, hasCompletedModuleTour, currentModuleConfig]);

  const startModuleTour = () => {
    if (!currentModuleConfig) return;

    setSteps(currentModuleConfig.steps);
    setIsOpen(true);
  };

  return null;
};

// Main ModuleOnboardingTours component with TourProvider
const ModuleOnboardingTours: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const location = useLocation();
  const currentModuleConfig = moduleToursConfig[location.pathname as keyof typeof moduleToursConfig];

  const [, setHasCompletedModuleTour] = useLocalStorage({
    key: currentModuleConfig?.key || 'default-tour',
    defaultValue: false,
  });

  const handleTourClose = () => {
    if (currentModuleConfig) {
      setHasCompletedModuleTour(true);
    }
  };

  return (
    <TourProvider
      steps={[]}
      onClickClose={handleTourClose}
      onClickMask={handleTourClose}
      afterOpen={(target) => {
        // Scroll element into view when tour step opens
        if (target) {
          target.scrollIntoView({ behavior: 'smooth', block: 'center' });
        }
      }}
      styles={{
        popover: (base) => ({
          ...base,
          '--reactour-accent': '#4a827f',
          borderRadius: '12px',
          boxShadow: '0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)',
          maxWidth: '400px',
        }),
        badge: (base) => ({
          ...base,
          left: 'auto',
          right: '-0.8125em',
          background: '#4a827f',
        }),
        controls: (base) => ({
          ...base,
          marginTop: '16px',
        }),
        close: (base) => ({
          ...base,
          right: '16px',
          top: '16px',
          width: '24px',
          height: '24px',
        }),
      }}
      className="module-tour-popover"
      maskClassName="module-tour-mask"
      highlightedMaskClassName="module-tour-highlighted"
      disableDotsNavigation={false}
      disableKeyboardNavigation={false}
      disableInteraction={false}
      rounded={8}
      padding={4}
    >
      <ModuleTourComponent />
      {children}
    </TourProvider>
  );
};

export default ModuleOnboardingTours;
