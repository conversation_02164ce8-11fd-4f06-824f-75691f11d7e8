import React, {useEffect} from 'react';

import {useQuery} from '@apollo/client';
import {useLocalStorage} from '@mantine/hooks';
import {StepType, TourProvider, useTour} from '@reactour/tour';

import {GET_SIDEBAR_MODULES} from '@/graphql/sidebar.ts';

interface TourStep {
  selector: string;
  content: React.ReactNode;
  position?: 'top' | 'bottom' | 'left' | 'right' | 'center';
  action?: (elem: Element | null) => void;
  actionAfter?: (elem: Element | null) => void;
}

const urlToIdMap: Record<string, string> = {
  '/dashboard': 'dashboard',
  '/bookings': 'bookings',
  '/patients': 'patients',
  '/reports': 'reports',
  '/contacts': 'contacts',
  '/data': 'data',
  '/quality-control': 'quality-control',
  '/pdf-import': 'pdf-import',
  '/normal-values': 'normal-values',
};

const moduleDescriptions: Record<string, string> = {
  dashboard: 'View your main dashboard with key metrics and overview of your laboratory operations.',
  bookings: 'Manage patient appointments and booking schedules.',
  patients: 'Access and manage patient records, demographics, and medical history.',
  reports: 'Generate, view, and manage laboratory reports and test results.',
  contacts: 'Manage your contact directory including referring doctors and healthcare providers.',
  data: 'Access and manage your laboratory data, including test parameters and reference ranges.',
  'quality-control': 'Monitor and manage quality control processes to ensure accurate test results.',
  'pdf-import': 'Import and process PDF documents using our Magic Import feature.',
  'normal-values': 'Configure and manage normal reference ranges for laboratory tests.',
};

const TourComponent: React.FC = () => {
  const {data: sidebarData} = useQuery(GET_SIDEBAR_MODULES);
  const {setIsOpen, setSteps} = useTour();
  const [hasCompletedTour, setHasCompletedTour] = useLocalStorage({
    key: 'rezibase:onboarding-tour-completed',
    defaultValue: false,
  });

  useEffect(() => {
    if (!hasCompletedTour && sidebarData?.sidebar_modules) {
      const timer = setTimeout(() => {
        startTour();
      }, 1000);

      return () => clearTimeout(timer);
    }

    if (hasCompletedTour) {
      setIsOpen(false);
    }

  }, [hasCompletedTour, sidebarData]);

  const startTour = () => {
    const steps: TourStep[] = [
      {
        selector: '#onboarding-root',
        content: (
          <div className="p-4">
            <h2 className="mb-3 text-xl font-bold text-gray-800">Welcome to Rezibase! 🎉</h2>
            <p className="mb-4 text-gray-600">
              We're excited to have you on board! Let's take a quick tour of the application to help you get
              started with managing your laboratory operations.
            </p>
            <p className="text-sm text-gray-500">
              This tour will guide you through the main features and modules available in the sidebar.
            </p>

            <button
              className="text-sm text-gray-500 underline"
              onClick={() => {
                setHasCompletedTour(true);
                setIsOpen(false);
              }}
            >
              Skip Tour
            </button>
          </div>
        ),
        position: 'center',
      },
    ];

    if (sidebarData?.sidebar_modules) {
      sidebarData.sidebar_modules.forEach((module) => {
        const moduleId = urlToIdMap[module.url];
        if (moduleId) {
          steps.push({
            selector: `#${moduleId}`,
            content: (
              <div className="p-4">
                <h3 className="mb-2 text-lg font-semibold text-gray-800 capitalize">{module.title}</h3>
                <p className="mb-3 text-gray-600">
                  {moduleDescriptions[moduleId] ||
                    `Access the ${module.title} module to manage related features and data.`}
                </p>
                {module.url === '#' && (
                  <p className="rounded bg-amber-50 p-2 text-sm text-amber-600">
                    <strong>Note:</strong> This module is currently under development.
                  </p>
                )}
              </div>
            ),
            position: 'right',
          });
        }
      });
    }

    steps.push({
      selector: '#onboarding-root',
      content: (
        <div className="p-4">
          <h2 className="mb-3 text-xl font-bold text-gray-800">You're All Set! 🚀</h2>
          <p className="mb-4 text-gray-600">
            Congratulations! You've completed the onboarding tour. You're now ready to start using Rezibase to
            manage your laboratory operations efficiently.
          </p>
          <div className="bg-brand-100 rounded-lg p-3">
            <p className="text-brand-700 text-sm">
              <strong>Pro Tip:</strong> You can always access help and documentation from the user menu in the
              top-right corner.
            </p>
          </div>
        </div>
      ),
      position: 'center',
    });

    setSteps && setSteps(steps as StepType[]);
    setIsOpen(true);
  };

  return null;
};

const OnboardingTour: React.FC<{children: React.ReactNode}> = ({children}) => {
  const [, setHasCompletedTour] = useLocalStorage({
    key: 'rezibase:onboarding-tour-completed',
    defaultValue: false,
  });

  const handleTourClose = () => {
    setHasCompletedTour(true);
  };

  return (
    <TourProvider
      steps={[]}
      onClickClose={handleTourClose}
      onClickMask={handleTourClose}
      afterOpen={(target) => {
        if (target) {
          target.scrollIntoView({behavior: 'smooth', block: 'center'});
        }
      }}
      styles={{
        popover: (base) => ({
          ...base,
          '--reactour-accent': '#4a827f',
          borderRadius: '12px',
          boxShadow: '0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)',
          maxWidth: '400px',
        }),
        badge: (base) => ({
          ...base,
          left: 'auto',
          right: '-0.8125em',
          background: '#4a827f',
        }),
        controls: (base) => ({
          ...base,
          marginTop: '16px',
        }),
        close: (base) => ({
          ...base,
          right: '16px',
          top: '16px',
          width: '16px',
          height: '16px',
        }),
      }}
      className="tour-popover"
      maskClassName="tour-mask"
      highlightedMaskClassName="tour-highlighted"
      disableDotsNavigation={false}
      disableKeyboardNavigation={false}
      disableInteraction={false}
      padding={0}
      showBadge={false}
    >
      <TourComponent />
      {children}
    </TourProvider>
  );
};

export default OnboardingTour;
