import clsx from 'clsx';
import React, {useCallback, useMemo, useState} from 'react';
import {
  Button as RA<PERSON><PERSON>utton,
  CalendarCell,
  CalendarGrid,
  CalendarGridBody,
  CalendarGridHeader,
  CalendarHeaderCell,
  Heading,
  ListBox,
  ListBoxItem,
  Popover,
  Calendar as RACCalendar,
  Select,
} from 'react-aria-components';

import {CalendarDate, DateValue, getLocalTimeZone, isToday, today} from '@internationalized/date';
import {ChevronLeft, ChevronRight} from 'lucide-react';

import {Button} from '@/components/ui/button';

const currentGlobalYear = today(getLocalTimeZone()).year;
const yearOptions = Array.from({length: 125}, (_, i) => currentGlobalYear - 100 + i);

export function Calendar() {
  const [calendarFocusedDate, setCalendarFocusedDate] = useState<DateValue>();

  const handleCalendarFocusChange = useCallback((date: DateValue) => {
    setCalendarFocusedDate(date);
  }, []);

  const handleYearSelect = useCallback(
    (yearKey: React.Key | null) => {
      if (yearKey === null || yearKey === undefined) {
        return;
      }

      const year = Number(yearKey);
      if (!isNaN(year) && calendarFocusedDate) {
        const currentMonth = calendarFocusedDate.month;
        const currentDay = calendarFocusedDate.day;
        const newFocusedDateCandidate = new CalendarDate(year, currentMonth, currentDay);
        const daysInNewMonth = newFocusedDateCandidate.calendar.getDaysInMonth(newFocusedDateCandidate);
        const newDay = Math.min(currentDay, daysInNewMonth);
        const newFocusedDate = new CalendarDate(year, currentMonth, newDay);
        setCalendarFocusedDate(newFocusedDate);
      }
    },
    [calendarFocusedDate?.year]
  );

  const yearSelectDropdown = useMemo(() => {
    const currentYearString = calendarFocusedDate?.year?.toString();

    return (
        <Select
          aria-label="Select year"
          selectedKey={currentYearString || null}
          onSelectionChange={handleYearSelect}
          className="react-aria-Select"
          slot={null}
        >
          <RACButton className="group ml-1 flex cursor-pointer items-center gap-1 rounded p-1 text-lg text-gray-700 focus:outline-none focus-visible:ring-2 data-[hovered]:bg-gray-100">
            {currentYearString || 'Year'}
            <ChevronRight className="h-4 w-4 rotate-90 group-aria-[expanded=true]:-rotate-90" />
          </RACButton>
          <Popover className="react-aria-Popover h-100 w-35">
            <ListBox
              items={yearOptions.map((y) => ({id: y.toString(), name: y.toString()}))}
              className="react-aria-ListBox"
            >
              {(item) => (
                <ListBoxItem
                  key={item.id}
                  textValue={item.name}
                  className="react-aria-ListBoxItem"
                >
                  {item.name}
                </ListBoxItem>
              )}
            </ListBox>
          </Popover>
        </Select>
    );
  }, [calendarFocusedDate?.year, handleYearSelect]);


  return (
    <RACCalendar
      focusedValue={calendarFocusedDate}
      onFocusChange={handleCalendarFocusChange}
    >
      <header className="flex w-full items-center gap-1 px-1 pb-4">
        <Heading className="ml-2 flex-1 text-lg font-semibold flex items-center" level={3}>
          {calendarFocusedDate?.toDate(getLocalTimeZone()).toLocaleString('default', { month: 'long' })}
          {yearSelectDropdown}
        </Heading>
        <Button
          slot="previous"
          variant="plain"
          size="small"
          className="px-2 py-1"
        >
          <ChevronLeft className="h-5 w-5" />
        </Button>
        <Button
          slot="next"
          variant="plain"
          size="small"
          className="px-2 py-1"
        >
          <ChevronRight className="h-5 w-5" />
        </Button>
      </header>
      <CalendarGrid className="border-separate border-spacing-1">
        <CalendarGridHeader>
          {(day) => (
            <CalendarHeaderCell className="text-xs font-semibold text-gray-500">{day}</CalendarHeaderCell>
          )}
        </CalendarGridHeader>
        <CalendarGridBody>
          {(date) => (
            <CalendarCell
              date={date}
              className={clsx(
                'relative flex h-8 w-8 cursor-default items-center justify-center rounded-full text-xs outline-none',
                'after:absolute after:-bottom-1 after:opacity-0 after:content-["•"]',
                'data-[selected]:bg-brand-400 data-[selected]:text-white',
                'focus-visible:ring-brand2-400 hover:bg-gray-100 focus-visible:ring-2',
                'data-[outside-month]:pointer-events-none data-[outside-month]:text-gray-300 data-[outside-month]:hover:bg-transparent',
                isToday(date, getLocalTimeZone()) && 'after:text-brand-400 after:opacity-100'
              )}
            />
          )}
        </CalendarGridBody>
      </CalendarGrid>
    </RACCalendar>
  );
}