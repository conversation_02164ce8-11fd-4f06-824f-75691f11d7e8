import {ReactNode} from 'react';
import {
  CheckboxContext,
  CheckboxGroupContext,
  ComboBoxContext,
  DatePickerContext,
  NumberFieldContext,
  Provider,
  RadioGroupContext,
  SelectContext,
  SliderContext,
  SwitchContext,
  TextFieldContext,
  TimeFieldContext,
} from 'react-aria-components';
import {type RegisterOptions, type UseControllerProps, useController} from 'react-hook-form';

import {FieldErrorContext} from './FieldError';
import {getFocusableRef} from './utils';
import {messagifyValidationRules, type patternsMap} from './validations';

export type FieldProps = {
  children?: ReactNode;
  required?: RegisterOptions['required'];
  deps?: RegisterOptions['deps'];
  pattern?: RegisterOptions['pattern'] | keyof typeof patternsMap;
  min?: RegisterOptions['min'];
  max?: RegisterOptions['max'];
  minLength?: RegisterOptions['minLength'];
  maxLength?: RegisterOptions['maxLength'];
  validate?: RegisterOptions['validate'];
} & Omit<UseControllerProps, 'rules'>;

/**
 * This component help integrate react-hook-form with react-aria-fields.
 *
 * how exactly?
 * the solution is quite dumb but works well enough. We provide context values
 * for every possible react aria field. It covers binding field value, invalid state
 * and validation triggers with react aria fields. but we still have to take care of
 * refs (to focus the field on error) and the actual error message.
 *
 * For ref we manually traverse the dom tree after rendering, find the first focusable
 * ref and pass it to react-hook-form.
 *
 * For error messages we have to override FieldError component from react-aria,
 * provide to a custom context and utilize it in the FieldError component.
 */
export function FormField({
  children,
  min,
  max,
  minLength,
  maxLength,
  required,
  pattern,
  deps,
  name,
  validate,
  ...props
}: FieldProps) {
  const {
    field: {value, onBlur, onChange, ref},
    fieldState: {invalid, error},
  } = useController({
    name: name!,
    ...props,
    rules: {
      deps,
      validate,
      ...messagifyValidationRules({
        min,
        max,
        minLength,
        maxLength,
        required,
        pattern,
      }),
    },
  });

  return (
    <Provider
      values={[
        [FieldErrorContext, error ?? null],
        [
          CheckboxContext,
          {
            value,
            onChange,
            ref,
            isInvalid: invalid,
            validationBehavior: 'aria',
            name,
            isRequired: !!required,
          },
        ],
        [
          CheckboxGroupContext,
          {
            value,
            onChange,
            ref: (el) => el && ref(getFocusableRef(el)),
            isInvalid: invalid,
            validationBehavior: 'aria',
            name,
            isRequired: !!required,
          },
        ],
        [
          RadioGroupContext,
          {
            value,
            onBlur,
            onChange,
            ref: (el) => el && ref(getFocusableRef(el)),
            isInvalid: invalid,
            validationBehavior: 'aria',
            name,
            isRequired: !!required,
          },
        ],
        [
          SliderContext,
          {
            value,
            onChange,
            ref: (el) => el && ref(getFocusableRef(el)),
            // todo: handle name and isRequired
          },
        ],
        [
          SwitchContext,
          {
            value,
            onBlur,
            onChange,
            ref,
            name,
            // todo: handle isRequired
          },
        ],
        [
          TextFieldContext,
          {
            value,
            onBlur,
            onChange,
            isInvalid: invalid,
            ref: (el) => el && ref(getFocusableRef(el)),
            validationBehavior: 'aria',
            name,
            isRequired: !!required,
          },
        ],
        [
          NumberFieldContext,
          {
            value,
            onBlur,
            onChange,
            isInvalid: invalid,
            ref: (el) => el && ref(getFocusableRef(el)),
            validationBehavior: 'aria',
            name,
            isRequired: !!required,
          },
        ],
        [
          ComboBoxContext,
          {
            selectedKey: value,
            onBlur,
            onSelectionChange: onChange,
            isInvalid: invalid,
            ref: (el) => el && ref(getFocusableRef(el)),
            validationBehavior: 'aria',
            name,
            isRequired: !!required,
          },
        ],
        [
          SelectContext,
          {
            selectedKey: value,
            onBlur,
            onSelectionChange: onChange,
            isInvalid: invalid,
            ref: (el) => el && ref(getFocusableRef(el)),
            validationBehavior: 'aria',
            name,
            isRequired: !!required,
          },
        ],
        [
          DatePickerContext,
          {
            value,
            onBlur,
            onChange,
            isInvalid: invalid,
            ref: (el) => el && ref(getFocusableRef(el)),
            validationBehavior: 'aria',
            name,
            isRequired: !!required,
          },
        ],
      ]}
    >
      <Provider
        values={[
          [
            TimeFieldContext,
            {
              value,
              onBlur,
              onChange,
              isInvalid: invalid,
              ref: (el) => el && ref(getFocusableRef(el)),
              validationBehavior: 'aria',
              name,
              isRequired: !!required,
            },
          ],
        ]}
      >
        {children}
      </Provider>
    </Provider>
  );
}
