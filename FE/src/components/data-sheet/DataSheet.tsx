import clsx from 'clsx';
import {ComponentProps} from 'react';

import './styles.css';

export function DataSheet(props: ComponentProps<'div'>) {
  return (
    <div
      {...props}
      className={clsx(`GridSheet grid isolate bg-(--non-editable-bg) text-xs/[1.3]`, props.className)}
    />
  );
}

export function DataSheetRow({className, ...restProps}: ComponentProps<'div'>) {
  return (
    <div
      {...restProps}
      className={clsx(
        `GridSheetRow contents`,
        className
      )}
    />
  );
}

export function DataSheetHeading({className, ...props}: ComponentProps<'div'>) {
  return (
    <div
      {...props}
      className={clsx(
        `GridSheetHeader h-9 px-1 py-2.75 font-semibold text-neutral-600 uppercase text-[10px]/[1.3]`,
        'bg-white',
        className
      )}
    />
  );
}

export function DataSheetCell({className, children, ...props}: ComponentProps<'div'>) {
  return (
    <div
      {...props}
      className={clsx(`GridSheetCell h-full content-center text-neutral-800`, className)}
    >
      {children}
    </div>
  );
}
