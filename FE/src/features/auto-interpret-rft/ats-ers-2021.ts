import {RftStore} from '@/features/studies-rft/store/rtf.store.ts';

import {ATSERS2005_MRPs, ATSERS2005_FeNO, ATSERS2005_ABGs, ATSERS2005_SpO2} from './ats-ers-2005.ts';

enum SpirometryPattern {
  Normal = 'normal',
  Restrictive = 'restrictive',
  Obstructive = 'obstructive',
  ReducedFERnormalFEV1 = 'reducedFERnormalFEV1',
  Mixed = 'mixed',
}

let spirometryPattern: SpirometryPattern;

/**
 * Main auto-interpretation function following ATS/ERS 2021/2005 guidelines
 */
export function AutoInterpret_rft_ATSERS2021(
  rftStore: RftStore,
  notify?: (msg: string) => void
): string {
  const report: string[] = [];

  // Append interpretations from each component
  report.push(ATSERS2021_Spirometry(rftStore, notify));
  report.push(ATSERS2021_COTransfer(rftStore));
  report.push(ATSERS2021_LungVols(rftStore));
  report.push(ATSERS2005_MRPs(rftStore));
  report.push(ATSERS2005_FeNO(rftStore));
  report.push(ATSERS2005_ABGs(rftStore));
  report.push(ATSERS2005_SpO2(rftStore));

  // Join all non-empty interpretations and return
  return report.filter((text) => text.trim().length > 0).join('');
}

/**
 * Interprets spirometry according to ATS/ERS 2021 guidelines
 */
function ATSERS2021_Spirometry(
  rftStore: RftStore,
  notify?: (msg: string) => void
): string {
  const txt: string[] = [];

  // Get spirometry values
  const sp = rftStore.spirometryStore;
  const preFEV1 = sp.getSp1Value('FEV1')?.result || 0;
  const preFVC = sp.getSp1Value('FVC')?.result || 0;
  const preVC = sp.getSp1Value('VC')?.result || 0;
  const postFEV1 = sp.getSp2Value('FEV1')?.result || 0;
  const postFVC = sp.getSp2Value('FVC')?.result || 0;
  const postVC = sp.getSp2Value('VC')?.result || 0;

  // Check which tests were done
  const preDone = preFEV1 > 0 && preFVC > 0;
  const postDone = postFEV1 !== undefined && postFEV1 > 0 && postFVC > 0;
  const prepostDone = preDone && postDone;

  let pattern: SpirometryPattern;
  let vc: number, fvc: number, fev1: number, fer: number;
  let z_fev1: number, ferLLN: number, fev1LLN: number, biggest_vc_fvc_lln: number;
  let prepostLabel: string;

  if (preDone) {
    // Use pre-bronchodilator values
    vc = preVC;
    fvc = preFVC;
    fev1 = preFEV1;
    fer = sp.getSp1Value('FER')?.result || 0;
    z_fev1 = sp.getSp1Value('FEV1')?.zScore || NaN;
    ferLLN = sp.getSp1Value('FER')?.predResult?.lln || NaN;
    fev1LLN = sp.getSp1Value('FEV1')?.predResult?.lln || NaN;
    biggest_vc_fvc_lln = Math.max(
      sp.getSp1Value('VC')?.predResult?.lln || NaN,
      sp.getSp1Value('FVC')?.predResult?.lln || NaN
    );
    prepostLabel = sp.sp1Condition || 'Baseline';
  } else if (postDone) {
    // Use post-bronchodilator values
    vc = postVC;
    fvc = postFVC;
    fev1 = postFEV1;
    fer = sp.getSp2Value('FER')?.result || 0;
    z_fev1 = sp.getSp2Value('FEV1')?.zScore || NaN;
    ferLLN = sp.getSp2Value('FER')?.predResult?.lln || NaN;
    fev1LLN = sp.getSp2Value('FEV1')?.predResult?.lln || NaN;
    biggest_vc_fvc_lln = Math.max(
      sp.getSp2Value('VC')?.predResult?.lln || NaN,
      sp.getSp2Value('FVC')?.predResult?.lln || NaN
    );
    prepostLabel = sp.sp2Condition || 'Post BD (Salb MDI)';
  } else {
    notify?.('Partial set of spirometry results - auto-interpret unavailable.');
    return '';
  }

  const biggest_vc_fvc = Math.max(vc, fvc);
  txt.push(prepostLabel);

  const vcDone = preVC > 0 || postVC > 0;
  if (vcDone) {
    if (!preDone && !postDone) {
      notify?.(
        'ATS (2021) spirometry interpretation guideline recommends against using SVC - auto-interpret unavailable.'
      );
      return '';
    } else {
      notify?.(
        'ATS (2021) spirometry interpretation guideline recommends against using SVC - interpretation uses FVC.'
      );
    }
  }

  // Interpret pattern
  if (fer >= ferLLN && biggest_vc_fvc >= biggest_vc_fvc_lln) {
    pattern = SpirometryPattern.Normal;
    txt.push(' ventilatory function is within normal limits');
  } else if (fer >= ferLLN && biggest_vc_fvc < biggest_vc_fvc_lln) {
    pattern = SpirometryPattern.Restrictive;
    const severity = z_fev1 >= -1.65 ? 'mild' : GetSeverity_ATSERS2021(z_fev1);
    txt.push(` ventilatory function suggests a ${severity} restrictive defect`);
  } else if (fer < ferLLN && biggest_vc_fvc >= biggest_vc_fvc_lln && fev1 < fev1LLN) {
    pattern = SpirometryPattern.Obstructive;
    const severity = GetSeverity_ATSERS2021(z_fev1);
    txt.push(` ventilatory function demonstrates a ${severity} obstructive defect`);
  } else if (fer < ferLLN && biggest_vc_fvc >= biggest_vc_fvc_lln && fev1 >= fev1LLN) {
    pattern = SpirometryPattern.ReducedFERnormalFEV1;
    txt.push(' ventilatory function demonstrates a mild obstructive defect');
  } else if (fer < ferLLN && biggest_vc_fvc < biggest_vc_fvc_lln) {
    pattern = SpirometryPattern.Mixed;
    const severity = GetSeverity_ATSERS2021(z_fev1);
    txt.push(` ventilatory function demonstrates a ${severity} mixed obstructive/restrictive defect`);
  } else {
    notify?.('Unusual set of spirometry results - auto-interpret unavailable.');
    return '';
  }

  // Store pattern for use by other interpretation functions
  spirometryPattern = pattern;

  // Check bronchodilator response if both pre and post measurements exist
  if (prepostDone) {
    const fev1_delta_abs = postFEV1 - preFEV1;
    const fvc_delta_abs = postFVC - preFVC;
    const vc_delta_abs = postVC - preVC;

    const threshold_pc = 10; // 10% threshold for significant change

    const fev1_delta_pc = Math.round((fev1_delta_abs / preFEV1) * 100);
    const fvc_delta_pc = Math.round((fvc_delta_abs / preFVC) * 100);
    const vc_delta_pc = Math.round((vc_delta_abs / preVC) * 100);

    const significant_response =
      fev1_delta_pc >= threshold_pc || fvc_delta_pc >= threshold_pc || vc_delta_pc >= threshold_pc;

    if (significant_response) {
      txt.push(' with a significant bronchodilator response');
    } else {
      txt.push(' with no significant bronchodilator response on this occasion');
    }

    if (txt.join('').includes('normal limits') && significant_response) {
      txt.length = 0; // Clear array
      txt.push(
        'Although baseline spirometric values are within normal limits there is a significant bronchodilator response indicating some airflow obstruction'
      );
    }
  }

  // Ensure proper punctuation
  const result = txt.join('');
  if (!result.endsWith('.')) {
    return result + '. ';
  }
  return result + ' ';
}

/**
 * Interprets CO transfer according to ATS/ERS 2021 guidelines
 */
function ATSERS2021_COTransfer(rftStore: RftStore): string {
  const txt: string[] = [];

  // Get CO transfer values
  const coTransfer = rftStore.coTransferStore;
  const tlco = coTransfer.getParamValue('TLCO')?.result || 0;
  const kco = coTransfer.getParamValue('KCO')?.result || 0;
  const va = coTransfer.getParamValue('VA')?.result || 0;
  const hb = coTransfer.getParamValue('Hb')?.result || 0;
  const hbFactor = coTransfer.hbFactor ?? 0;

  // Get reference ranges
  const tlcoLLN = coTransfer.getParamValue('TLCO')?.predResult?.lln || NaN;
  const tlcoULN = coTransfer.getParamValue('TLCO')?.predResult?.uln || NaN;
  const vaLLN = coTransfer.getParamValue('VA')?.predResult?.lln || NaN;
  const kcoRangeHi = coTransfer.getParamValue('KCO')?.predResult?.uln || NaN;
  const z_tlco = coTransfer.getParamValue('TLCO')?.zScore || NaN;

  const testDone = tlco > 0;

  if (testDone) {
    let addedBit: string;
    let adjustedTlco = tlco;
    let adjustedKco = kco;

    if (hb === 0) {
      addedBit = ', uncorrected for haemoglobin,';
    } else {
      adjustedTlco = tlco * hbFactor;
      adjustedKco = kco * hbFactor;
      addedBit = ', corrected for haemoglobin,';
    }

    txt.push('Carbon monoxide transfer factor' + addedBit);

    if (adjustedTlco < tlcoLLN) {
      const severity = GetSeverity_ATSERS2021(z_tlco);
      txt.push(` shows a ${severity} reduction `);

      if (va > vaLLN) {
        txt.push('indicating a gas exchange abnormality at the alveolar-capillary level or due to pulmonary vascular dysfunction. ');
      } else if (adjustedKco > kcoRangeHi) {
        txt.push(
          'indicating a gas exchange abnormality at the alveolar-capillary level or due to pulmonary vascular dysfunction. '
        );
      } else {
        txt.push(
          'at least in part due to a loss of alveolar volume, but also indicating a gas exchange abnormality at the alveolar-capillary level or due pulmonary vascular dysfunction. '
        );
      }
    } else if (adjustedTlco > tlcoULN) {
      txt.push(' is increased, which may be a normal variant, or indicate increased pulmonary blood volume or pulmonary haemorrhage. ');
    } else {
      txt.push('is within normal limits. ');
    }

    return txt.join('');
  }

  return '';
}

/**
 * Interprets lung volumes according to ATS/ERS 2021 guidelines
 */
function ATSERS2021_LungVols(rftStore: RftStore): string {
  const txt: string[] = [];

  // Get lung volume values
  const lungVolumes = rftStore.lungVolumesStore;
  const tlc = lungVolumes.getParamValue('TLC')?.result || 0;
  const frc = lungVolumes.getParamValue('FRC')?.result || 0;
  const rv = lungVolumes.getParamValue('RV')?.result || 0;

  // Calculate RV/TLC ratio
  const rvtlc = tlc > 0 ? (100 * rv) / tlc : 0;

  // Get reference ranges
  const tlcRangeLo = lungVolumes.getParamValue('TLC')?.predResult?.lln || NaN;
  const tlcRangeHi = lungVolumes.getParamValue('TLC')?.predResult?.uln || NaN;
  const rvtlcULN = lungVolumes.getParamValue('RV/TLC')?.predResult?.uln || NaN;
  const z_tlc = lungVolumes.getParamValue('TLC')?.zScore || NaN;

  // Get spirometry values for interpretation context
  const sp = rftStore.spirometryStore;
  const ferFVC = sp.fer_fvc || NaN;
  const ferLLN = sp.getSp1Value('FEV1/FVC')?.predResult?.lln || NaN;

  const testDone = tlc > 0 && frc > 0 && rv > 0;

  if (testDone) {
    if (tlc < tlcRangeLo) {
      if (rvtlc > rvtlcULN) {
        if (ferFVC === 0) {
          txt.push(
            'Lung volume measurements show a reduced TLC together with gas trapping, suggesting a mixed restrictive/obstructive defect. '
          );
        } else if (ferFVC < ferLLN) {
          txt.push(
            'Lung volume measurements show a reduced TLC and gas trapping, and together with the reduced FEV1/FVC ratio, indicate a mixed restrictive/obstructive defect. '
          );
        } else {
          txt.push(
            'Lung volume measurements show a reduced TLC and gas trapping, and together with a FEV1/FVC ratio in the normal range, indicate a complex restrictive defect. '
          );
        }
      } else {
        const severity = GetSeverity_ATSERS2021(z_tlc);
        if (
          spirometryPattern === SpirometryPattern.Restrictive ||
          spirometryPattern === SpirometryPattern.Mixed
        ) {
          txt.push(
            `Lung volume measurements confirm the presence of a ${severity} simple restrictive defect. `
          );
        } else {
          txt.push(
            `Lung volume measurements show a low TLC indicating a ${severity} simple restrictive defect. `
          );
        }
      }
    } else if (tlc > tlcRangeHi) {
      if (rvtlc > rvtlcULN) {
        const severity = GetSeverity_ATSERS2021(z_tlc);
        txt.push(`Lung volume measurements indicate ${severity} hyperinflation with gas trapping. `);
      } else {
        txt.push(
          'Lung volume measurements show an increased TLC but without evidence of gas trapping, which may suggest large normal lungs. '
        );
      }
    } else {
      if (rvtlc > rvtlcULN) {
        txt.push(
          'Whilst TLC is within the normal range, the raised RV/TLC ratio indicates some gas trapping. '
        );
      } else {
        txt.push('Lung volumes are within normal limits. ');
      }
    }

    return txt.join('');
  }

  return '';
}

/**
 * Determines severity based on z-score following ATS/ERS 2021 guidelines
 *
 * Severity thresholds:
 * - Mild: −1.65 to −2.5
 * - Moderate: −2.51 to −4.0
 * - Severe: <−4.1
 */
function GetSeverity_ATSERS2021(zscore: number) {
  // Take absolute value to work for both below and above normal range
  const absolute_zscore = Math.abs(zscore);

  if (absolute_zscore > 4.1) {
    return 'severe';
  } else if (absolute_zscore > 2.5) {
    return 'moderate';
  } else if (absolute_zscore > 1.65) {
    return 'mild';
  } else {
    return '';
  }
}
