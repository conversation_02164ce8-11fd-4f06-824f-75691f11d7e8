import {RftStore} from '@/features/studies-rft/store/rtf.store.ts';


/**
 * ATS ERS 2005 MRPs interpretation
 */
export function ATSERS2005_MRPs(rftStore: RftStore): string {
  const txt: string[] = [];

  // Get MIP and MEP values from the store
  const mipValue = rftStore.mrpsStore.getParamValue('MIP');
  const mepValue = rftStore.mrpsStore.getParamValue('MEP');

  const mip = mipValue?.result ?? 0;
  const mep = mepValue?.result ?? 0;
  const miplln = mipValue?.predResult?.lln ?? NaN;
  const meplln = mepValue?.predResult?.lln ?? NaN;

  const testDone = mip > 0 && mep > 0;

  if (testDone) {
    if (mip > miplln && mep > meplln) {
      txt.push('Maximal respiratory pressures are within the normal range. ');
    } else if (mip <= miplln && mep <= meplln) {
      txt.push('Maximal respiratory pressures are reduced indicating respiratory muscle and diaphragmatic weakness. ');
    } else if (mip <= miplln) {
      txt.push('Maximal inspiratory pressure is reduced indicating diaphragmatic weakness. Maximal expiratory pressure is within the normal range. ');
    } else if (mep <= meplln) {
      txt.push('Maximal expiratory pressure is reduced indicating respiratory muscle weakness. Maximal inspiratory pressure is within the normal range. ');
    }

    return txt.join('');
  }

  return '';
}

/**
 * ATS ERS 2005 FeNO interpretation
 */
export function ATSERS2005_FeNO(rftStore: RftStore) {
  const txt: string[] = [];

  // Get FeNO value from the store
  const value = rftStore.exhaledNitricOxideStore.getParamValue('FeNO');
  const feno = value?.result ?? 0;
  const fenouln = value?.predResult?.uln ?? NaN;

  const testDone = feno > 0;

  if (testDone) {
    // Calculate grey_uln based on fenouln
    let grey_uln: number;
    if (fenouln === 20) {
      grey_uln = 35;
    } else if (fenouln === 25) {
      grey_uln = 50;
    } else {
      grey_uln = fenouln * 1.4; // Default fallback
    }

    if (feno < fenouln) {
      txt.push('FeNO result is low and does not suggest the presence of active eosinophilic inflammation. ');
    } else if (feno <= grey_uln) {
      txt.push('FeNO result is intermediate and may suggest the presence of active eosinophilic inflammation. ');
    } else {
      txt.push('FeNO result is high and suggests the presence of active eosinophilic inflammation. ');
    }

    return txt.join('');
  }

  return '';
}

/**
 * ATS ERS 2005 ABGs interpretation
 */
export function ATSERS2005_ABGs(rftStore: RftStore) {
  const txt: string[] = [];

  // Get values from the blood gases store
  const value1 = rftStore.bloodGasesStore.getResult1Value('PaO2');
  const value2 = rftStore.bloodGasesStore.getResult1Value('PaCO2');
  const value3 = rftStore.bloodGasesStore.getResult1Value('pH');
  const value4 = rftStore.bloodGasesStore.getResult1Value('A-aPO2');

  const pao2 = value1?.result ?? 0;
  const paco2 = value2?.result ?? 0;
  const ph = value3?.result ?? 0;
  const aapo2 = value4?.result ?? NaN;
  const fio2 = rftStore.bloodGasesStore.result1.fio2.toLowerCase();

  const fio2_is_air = fio2.includes('air');
  const testDone = paco2 > 0 && pao2 > 0 && ph > 0;

  if (testDone) {
    // PO2 interpretation
    if (pao2 < (value1?.predResult?.lln ?? NaN)) {
      if (fio2_is_air && !isNaN(aapo2)) {
        if (aapo2 > (value4?.predResult?.uln ?? NaN)) {
          txt.push('Arterial blood gases reveal hypoxaemia with a widened (A-a)PO2 gradient');
        } else {
          txt.push('Arterial blood gases reveal hypoxaemia but with a normal (A-a)PO2 gradient');
        }
      } else {
        txt.push('Arterial blood gases reveal hypoxaemia');
      }
    } else {
      if (fio2_is_air && !isNaN(aapo2)) {
        if (aapo2 > (value4?.predResult?.uln ?? NaN)) {
          txt.push('Arterial blood gases reveal normoxaemia but with a widened (A-a)PO2 gradient');
        } else {
          txt.push('Arterial blood gases reveal normoxaemia with a normal (A-a)PO2 gradient');
        }
      } else {
        txt.push('Arterial blood gases reveal normoxaemia');
      }
    }

    // PCO2 interpretation
    if (paco2 < (value2?.context?.lln ?? NaN)) {
      txt.push(', alveolar hyperventilation');
    } else if (paco2 > (value2?.context?.uln ?? NaN)) {
      txt.push(', alveolar hypoventilation');
    }

    // pH interpretation
    if (ph > (value3?.context?.uln ?? NaN)) {
      txt.push(' and alkalosis. ');
    } else if (ph < (value3?.context?.lln ?? NaN)) {
      txt.push(' and acidosis. ');
    } else {
      txt.push(' and a normal pH. ');
    }

    const result = txt.join('');
    if (!result.endsWith('. ')) {
      return result + '. ';
    }
    return result;
  }

  return '';
}

/**
 * ATS ERS 2005 SpO2 interpretation
 */
export function ATSERS2005_SpO2(rftStore: RftStore) {
  const value = rftStore.bloodGasesStore.getResult1Value('SpO2');
  const lln = value?.predResult?.lln;
  const spo2 = value?.result ?? 0;

  if (spo2 <= 0 || !lln) return '';

  if (spo2 < lln) {
    return `Oxygen saturation by pulse oximetry is reduced. `;
  } else {
    return `Oxygen saturation by pulse oximetry is within the normal range. `;
  }
}
