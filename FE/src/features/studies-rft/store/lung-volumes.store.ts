import {makeAutoObservable} from 'mobx';

import {QueryResultType} from '@/@types/graphql.tada.ts';
import {apolloClient} from '@/apollo-client.ts';
import {RFT_QUERY, UPDATE_RFT_ROUTINE} from '@/features/studies-rft/queries.ts';
import {safeParseFloat} from '@/features/studies/utils.ts';
import {applyMixins} from '@/lib/mixin.ts';
import {DisposableStoreMixin} from '@/store/disposable.store.ts';
import {Parameter} from '@/store/parameter.ts';

import {
  ComputedParameterValue,
  DisposableStoreWithUpsert,
  ParameterContext,
  ParameterValue,
  RefType,
} from './base.store.tsx';
import type {RftStore} from './rtf.store.ts';

class LungVolumes {
  constructor(public values: ParameterValue[]) {}
}

export class LungVolumesStore implements DisposableStoreWithUpsert {
  public baseline: LungVolumes;
  method: string | null = null;
  condition: string | null = null;

  private mutationPromise?: Promise<any>;

  constructor(public rftStore: RftStore) {
    makeAutoObservable(this);

    // Create parameters
    const tlc = Parameter.getOrCreate({description: 'TLC'});
    const lvc = Parameter.getOrCreate({description: 'LvVC'});
    const ic = Parameter.getOrCreate({description: 'IC'});
    const frc = Parameter.getOrCreate({description: 'FRC'});
    const erv = Parameter.getOrCreate({description: 'ERV'});
    const rv = Parameter.getOrCreate({description: 'RV'});
    const rvTlc = Parameter.getOrCreate({description: 'RV/TLC'});

    // Initialize with the values
    const baselineTlc = new ParameterValue(
      tlc,
      new ParameterContext(5.14, 7.8, 6.5, 0.95),
      RefType.RANGE,
      this,
      'r_bl_tlc'
    );
    const baselineLvc = new ParameterValue(
      lvc,
      new ParameterContext(3.42, undefined, 4.3, 0.6),
      RefType.LLN,
      this,
      'r_bl_lvvc'
    );
    const baselineIc = new ParameterValue(
      ic,
      new ParameterContext(2.18, undefined, 3.1, 0.55),
      RefType.LLN,
      this,
      'r_bl_ic'
    );
    const baselineFrc = new ParameterValue(
      frc,
      new ParameterContext(2.32, 4.63, 3.35, 0.7),
      RefType.RANGE,
      this,
      'r_bl_frc'
    );
    const baselineErv = new ParameterValue(
      erv,
      new ParameterContext(0.36, undefined, 1.08, 0.45),
      RefType.LLN,
      this,
      'r_bl_erv'
    );
    const baselineRv = new ParameterValue(
      rv,
      new ParameterContext(1.33, 3.23, 2.2, 0.5),
      RefType.RANGE,
      this,
      'r_bl_rv'
    );
    const baselineRvTlc = new ComputedParameterValue(
      () =>
        baselineRv.result && baselineTlc.result ? (baselineRv.result / baselineTlc.result) * 100 : undefined,
      rvTlc,
      new ParameterContext(),
      RefType.ULN,
      this,
      'r_bl_rvtlc'
    );

    this.baseline = new LungVolumes([
      baselineTlc,
      baselineLvc,
      baselineIc,
      baselineFrc,
      baselineErv,
      baselineRv,
      baselineRvTlc,
    ]);
  }

  get completedTestName() {
    if (!!this.getParamValue('TLC')?.result && !!this.getParamValue('FRC')?.result && !!this.getParamValue('RV')?.result) {
      return 'LV';
    }
  }

  getParamValue(paramName: string) {
    return this.baseline.values.find((v) => v.parameter?.description === paramName);
  }

  setProperty<K extends keyof LungVolumesStore>(key: K, value: LungVolumesStore[K]) {
    (this[key] as LungVolumesStore[K]) = value;
  }

  fromQueryResult(data: QueryResultType<typeof RFT_QUERY>['rft_routine'][number], flash = false) {
    if (data.r_condition_lv) {
      this.setProperty('condition', data.r_condition_lv);
    }

    if (safeParseFloat(data.r_bl_tlc)) {
      this.baseline.values[0].setResult(safeParseFloat(data.r_bl_tlc), flash);
    }

    if (safeParseFloat(data.r_bl_lvvc)) {
      this.baseline.values[1].setResult(safeParseFloat(data.r_bl_lvvc), flash);
    }

    if (safeParseFloat(data.r_bl_ic)) {
      this.baseline.values[2].setResult(safeParseFloat(data.r_bl_ic), flash);
    }

    if (safeParseFloat(data.r_bl_frc)) {
      this.baseline.values[3].setResult(safeParseFloat(data.r_bl_frc), flash);
    }

    if (safeParseFloat(data.r_bl_erv)) {
      this.baseline.values[4].setResult(safeParseFloat(data.r_bl_erv), flash);
    }

    if (safeParseFloat(data.r_bl_rv)) {
      this.baseline.values[5].setResult(safeParseFloat(data.r_bl_rv), flash);
    }

    if (safeParseFloat(data.r_bl_rvtlc)) {
      this.baseline.values[6].setResult(safeParseFloat(data.r_bl_rvtlc), flash);
    }

    if (data.lungvolumes_method) {
      this.method = data.lungvolumes_method;
    }
  }

  async upsertDB(field?: string) {
    if (this.mutationPromise) {
      await this.mutationPromise;
    }

    interface UpdateInput {
      r_condition_lv?: string | null;
      r_bl_tlc?: string | null;
      r_bl_lvvc?: string | null;
      r_bl_ic?: string | null;
      r_bl_frc?: string | null;
      r_bl_erv?: string | null;
      r_bl_rv?: string | null;
      r_bl_rvtlc?: string | null;
      lungvolumes_method?: string | null;
    }

    const updateData: UpdateInput = {};

    if (!field || field === 'r_condition_lv') {
      updateData.r_condition_lv = this.condition;
    }
    if (!field || field === 'r_bl_tlc') {
      updateData.r_bl_tlc = this.baseline.values[0].result?.toString() ?? null;
    }
    if (!field || field === 'r_bl_lvvc') {
      updateData.r_bl_lvvc = this.baseline.values[1].result?.toString() ?? null;
    }
    if (!field || field === 'r_bl_ic') {
      updateData.r_bl_ic = this.baseline.values[2].result?.toString() ?? null;
    }
    if (!field || field === 'r_bl_frc') {
      updateData.r_bl_frc = this.baseline.values[3].result?.toString() ?? null;
    }
    if (!field || field === 'r_bl_erv') {
      updateData.r_bl_erv = this.baseline.values[4].result?.toString() ?? null;
    }
    if (!field || field === 'r_bl_rv') {
      updateData.r_bl_rv = this.baseline.values[5].result?.toString() ?? null;
    }
    if (!field || field === 'r_bl_rvtlc') {
      updateData.r_bl_rvtlc = this.baseline.values[6].result?.toString() ?? null;
    }
    if (!field || field === 'lungvolumes_method') {
      updateData.lungvolumes_method = this.method;
    }

    this.mutationPromise = apolloClient.mutate({
      mutation: UPDATE_RFT_ROUTINE,
      variables: {
        id: this.rftStore.rftid,
        inp: updateData,
      },
    });

    try {
      await this.mutationPromise;
    } finally {
      this.mutationPromise = undefined;
    }
  }
}

export interface LungVolumesStore extends DisposableStoreMixin {}

applyMixins(LungVolumesStore, [DisposableStoreMixin]);
