import {<PERSON>Box, <PERSON>BoxItem, <PERSON>over, Button as RA<PERSON><PERSON><PERSON>on, Select, SelectValue} from 'react-aria-components';

import {useQuery} from '@apollo/client';
import {ChevronDown} from 'lucide-react';
import {observer} from 'mobx-react-lite';

import NIL from '@/components/NIL.tsx';
import {DataSheet, DataSheetCell, DataSheetRow} from '@/components/data-sheet';
import {Button} from '@/components/ui/button';
import {CommitableParameterField} from '@/features/studies-rft/components/CommitableParameterField.tsx';
import {ComputedParameterValue} from '@/features/studies-rft/store/base.store.tsx';
import {RftStore} from '@/features/studies-rft/store/rtf.store.ts';
import {getPreferenceField} from '@/graphql/preferences.ts';

export const BloodGasesSection = observer(({rftStore}: {rftStore: RftStore}) => {
  const {data: fio2Data} = useQuery(getPreferenceField, {variables: {fieldName: 'FiO2'}});

  const store = rftStore.bloodGasesStore;

  return (
    <div>
      <div className="flex items-start gap-x-2">
        <div className="w-32">
          <div className="mb-2 flex h-[calc(var(--row-height)+4px)] items-center justify-end text-[10px] font-semibold text-neutral-800 uppercase">
            Blood gases
          </div>
          <div className="flex h-(--row-height) items-center justify-end">
            <div className="truncate text-right text-(length:--font-size) font-semibold text-neutral-600">
              Sample Type
            </div>
          </div>
          <div className="flex h-(--row-height) items-center justify-end">
            <div className="truncate text-right text-(length:--font-size) font-semibold text-neutral-600">fio2</div>
          </div>
          {store.result1.values.map((value, index) => (
            <div
              key={index}
              className="flex h-(--row-height) items-center justify-end"
            >
              <div className="truncate text-right text-(length:--font-size) font-semibold text-neutral-600">
                {(() => {
                  if (value.parameter?.description === 'Shunt') {
                    return 'Shunt - anatomic'
                  }
                  return value.parameter?.description ?? '';
                })()}{' '}
                {value.parameter?.configAwareUnits && <>[{value.parameter?.configAwareUnits}]</>}
              </div>
            </div>
          ))}
        </div>

        <div>
          <div className="py-0.875 mb-2 grid h-[calc(var(--row-height)+4px)] grid-cols-[5.5rem_5.5rem_5.5rem] items-center rounded border border-neutral-300 text-[10px]/[1.3] font-semibold text-neutral-600 uppercase">
            <p className="px-1.5 text-right">Ref</p>
            <p className="px-1.5 text-right">Result1</p>
            <p className="px-1.5 text-right">Result2</p>
          </div>

          <DataSheet className="grid-cols-[6rem_5.5rem_5.5rem] !rounded-t-md">
            <DataSheetRow className="rounded-tl-lg">
              <DataSheetCell className="text-right  ">
                <NIL  className='rounded-none !rounded-tl-2xl'/>
              </DataSheetCell>
              <DataSheetCell className="text-right ">
                <Select
                  onSelectionChange={(newValue) => {
                    store.result1.setProperty('sampleType', newValue as string);
                    store.upsertDB('r_abg1_sampletype')
                  }}
                  isDisabled={!rftStore.isEditing}
                  selectedKey={store.result1.sampleType}
                  aria-label="Result 1 Sample Type"
                  className="react-aria-Select placeholder:text-xs"
                >
                  <RACButton className="react-aria-Button w-full">
                    <SelectValue className="react-aria-SelectValue text-[9px]/[1.3]" />
                    <ChevronDown
                      className="size-[9px] text-gray-400"
                      aria-hidden="true"
                    />
                  </RACButton>
                  <Popover>
                    <ListBox>
                      <ListBoxItem
                        id=""
                        textValue=""
                      >
                        <NIL className="ml-0" />
                      </ListBoxItem>
                      <ListBoxItem id="Arterial">Arterial</ListBoxItem>
                      <ListBoxItem id="Capillary">Capillary</ListBoxItem>
                    </ListBox>
                  </Popover>
                </Select>
              </DataSheetCell>
              <DataSheetCell className="text-right">
                <Select
                  onSelectionChange={(newValue) => {
                    store.result2.setProperty('sampleType', newValue as string);
                    store.upsertDB('r_abg2_sampletype')
                  }}
                  selectedKey={store.result2.sampleType}
                  isDisabled={!rftStore.isEditing}
                  aria-label="Result 2 Sample Type"
                  className='react-aria-Select '
                >
                  <RACButton className="react-aria-Button w-full !rounded-tr-md">
                    <SelectValue className="react-aria-SelectValue text-[9px]/[1.3]" />
                    <ChevronDown
                      className="size-[9px] text-gray-400"
                      aria-hidden="true"
                    />
                  </RACButton>
                  <Popover>
                    <ListBox>
                      <ListBoxItem
                        id=""
                        textValue=""
                      >
                        <NIL className="ml-0" />
                      </ListBoxItem>
                      <ListBoxItem id="Arterial">Arterial</ListBoxItem>
                      <ListBoxItem id="Capillary">Capillary</ListBoxItem>
                    </ListBox>
                  </Popover>
                </Select>
              </DataSheetCell>
            </DataSheetRow>

            <DataSheetRow>
              <DataSheetCell className="text-right">
                <NIL />
              </DataSheetCell>
              <DataSheetCell className="text-right">
                <Select
                  onSelectionChange={(newValue) => {
                    store.result1.setProperty('fio2', newValue as string);
                    store.upsertDB('r_abg1_fio2')
                  }}
                  selectedKey={store.result1.fio2}
                  aria-label="FiO2 Result 1"
                  isDisabled={!rftStore.isEditing}
                >
                  <RACButton className="react-aria-Button w-full">
                    <SelectValue className="react-aria-SelectValue text-[9px]/[1.3]" />
                    <ChevronDown
                      className="size-[9px] text-gray-400"
                      aria-hidden="true"
                    />
                  </RACButton>
                  <Popover className="react-aria-Popover w-auto">
                    <ListBox items={fio2Data?.prefs_fields?.[0]?.prefs_fielditems ?? []}>
                      {(item) => <ListBoxItem id={item.fielditem?.toString()}>{item.fielditem}</ListBoxItem>}
                    </ListBox>
                  </Popover>
                </Select>
              </DataSheetCell>
              <DataSheetCell className="text-right border-r border-neutral-500 ">
                <Select
                  onSelectionChange={(newValue) => {
                    store.result2.setProperty('fio2', newValue as string);
                    store.upsertDB('r_abg2_fio2')
                  }}
                  selectedKey={store.result2.fio2}
                  aria-label="FiO2 Result 2"
                  isDisabled={!rftStore.isEditing}
                >
                  <RACButton className="react-aria-Button w-full border-r-0 ">
                    <SelectValue className="react-aria-SelectValue text-[9px]/[1.3]" />
                    <ChevronDown
                      className="size-[9px] text-gray-400"
                      aria-hidden="true"
                    />
                  </RACButton>
                  <Popover className="react-aria-Popover w-auto">
                    <ListBox items={fio2Data?.prefs_fields?.[0]?.prefs_fielditems ?? []}>
                      {(item) => <ListBoxItem id={item.fielditem?.toString()}>{item.fielditem}</ListBoxItem>}
                    </ListBox>
                  </Popover>
                </Select>
              </DataSheetCell>
            </DataSheetRow>

            {store.result1.values.map((value, index) => {
              const value2 = store.result2.values[index];
              const isValue1Nil = value instanceof ComputedParameterValue && !value.result;
              const isValue2Nil = value2 instanceof ComputedParameterValue && !value2.result;

              return (
                <DataSheetRow key={index}>
                  <DataSheetCell className="text-right italic">{value.refValue ?? <NIL />}</DataSheetCell>
                  <DataSheetCell
                    data-computed={value instanceof ComputedParameterValue ? true : undefined}
                    className="text-right"
                  >
                    {isValue1Nil ? (
                      <NIL />
                    ) : (
                      <CommitableParameterField
                        paramValue={value}
                        rftStore={rftStore}
                      />
                    )}
                  </DataSheetCell>
                  <DataSheetCell
                    data-computed={value instanceof ComputedParameterValue ? true : undefined}
                    className="relative text-right"
                  >
                    {isValue2Nil ? (
                      <NIL />
                    ) : (
                      <CommitableParameterField
                        paramValue={value2}
                        rftStore={rftStore}
                      />
                    )}
                    {value instanceof ComputedParameterValue &&
                      !value.autoCalculated &&
                      rftStore.isEditing && (
                        <div className="absolute top-0 left-[100%] ml-2 flex items-center pl-px">
                          <Button
                            variant="plain"
                            size="small"
                            className="h-(--row-height) px-1.5 !text-(length:--font-size)"
                            onPress={() => {
                              value.calculate();
                              (value2 as ComputedParameterValue).calculate();
                              value2.upsertDB();
                              value.upsertDB();
                            }}
                          >
                            Calculate
                          </Button>
                          <Button
                            variant="plain"
                            size="small"
                            className="h-(--row-height) px-1.5 !text-(length:--font-size)"
                            onPress={() => {
                              value.reset();
                              (value2 as ComputedParameterValue).reset();
                              value2.upsertDB();
                              value.upsertDB();
                            }}
                          >
                            Clear
                          </Button>
                        </div>
                      )}
                  </DataSheetCell>
                </DataSheetRow>
              );
            })}
          </DataSheet>
        </div>
      </div>
    </div>
  );
});
