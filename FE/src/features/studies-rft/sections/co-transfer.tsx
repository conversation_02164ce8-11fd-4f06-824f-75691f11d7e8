import {useEffect} from 'react';
import {Button, ListBox, ListBoxItem, Popover, Select, SelectValue} from 'react-aria-components';

import {useQuery} from '@apollo/client';
import {ChevronDown} from 'lucide-react';
import {observer} from 'mobx-react-lite';

import NIL from '@/components/NIL.tsx';
import {DataSheet, DataSheetCell, DataSheetRow} from '@/components/data-sheet';
import {
  ParameterZScoreRange,
  ZScorePlotParamDot,
  ZScorePlotParamRow,
} from '@/features/studies-rft/ZScorePlot.tsx';
import {CommitableParameterField} from '@/features/studies-rft/components/CommitableParameterField.tsx';
import {ComputedParameterValue} from '@/features/studies-rft/store/base.store.tsx';
import {RftStore} from '@/features/studies-rft/store/rtf.store.ts';
import {getPreferenceField} from '@/graphql/preferences.ts';

export const COTransferSection = observer(({rftStore}: {rftStore: RftStore}) => {
  const {data} = useQuery(getPreferenceField, {variables: {fieldName: 'Test condition'}});

  useEffect(() => {
    if (!data?.prefs_fields?.[0] || rftStore.coTransferStore.condition) return;
    const defaultItem = data?.prefs_fields[0].prefs_fielditems.find(
      (e) => e.prefs_id === data?.prefs_fields[0].default_fielditem_id
    );

    rftStore.coTransferStore.setProperty('condition', defaultItem?.fielditem ?? undefined);
  }, [data]);

  return (
    <div>
      <div className="flex items-start justify-between gap-x-2">
        <div className="flex items-center gap-x-2">
          <div className="mt-0 w-32">
            <div className="flex h-[calc(var(--row-height)+4px)] items-center justify-end text-end text-[10px] font-semibold text-neutral-800 uppercase">
              Co transfer factor
            </div>
            {rftStore.coTransferStore.baseline.values.map((value, index) => (
              <div
                key={index}
                className="flex h-(--row-height) items-center justify-end"
              >
                <div className="truncate text-right text-(length:--font-size) font-semibold text-neutral-600">
                  {value.parameter?.description ?? ''}{' '}
                  {value.parameter?.configAwareUnits && <>[{value.parameter?.configAwareUnits}]</>}
                </div>
              </div>
            ))}
          </div>

          <div>
            <Select
              selectedKey={rftStore.coTransferStore.condition ?? data?.prefs_fields[0].default_fielditem_id}
              onSelectionChange={(val) => {
                rftStore.coTransferStore.setProperty('condition', val as string);
                rftStore.coTransferStore.upsertDB('r_condition_tl');
              }}
              aria-label="Test condition"
              isDisabled={!rftStore.isEditing}
            >
              <Button className="react-aria-Button -mb-0.25 h-[calc(var(--row-height)+4px)] w-full rounded-none rounded-t-sm px-3 py-1">
                <SelectValue className="react-aria-SelectValue text-[9px]/[1.3]" />
                <ChevronDown
                  className="size-3 text-gray-400"
                  aria-hidden="true"
                />
              </Button>
              <Popover>
                <ListBox items={data?.prefs_fields[0]?.prefs_fielditems ?? []}>
                  {(item) => <ListBoxItem id={item.fielditem?.toString()}>{item.fielditem}</ListBoxItem>}
                </ListBox>
              </Popover>
            </Select>

            <DataSheet className="grid-cols-[4.25rem_4.25rem_4.25rem_4.25rem]">
              {rftStore.coTransferStore.baseline.values.map((value, index) => {
                const isNil = value instanceof ComputedParameterValue && !value.result;

                return (
                  <DataSheetRow key={index}>
                    <DataSheetCell className="text-right italic">{value.refValue ?? <NIL />}</DataSheetCell>
                    <DataSheetCell
                      data-computed={value instanceof ComputedParameterValue}
                      className="text-right"
                    >
                      {isNil ? (
                        <NIL />
                      ) : (
                        <CommitableParameterField
                          paramValue={value}
                          rftStore={rftStore}
                        />
                      )}
                    </DataSheetCell>
                    <DataSheetCell className="text-right">
                      {value.zscoreFormatted ? value.zscoreFormatted : <NIL />}
                    </DataSheetCell>
                    <DataSheetCell className="text-right">
                      {value.predPercent ? (
                        `${Math.round(value.predPercent)}%`
                      ) : (
                        <NIL />
                      )}
                    </DataSheetCell>
                  </DataSheetRow>
                );
              })}
            </DataSheet>
          </div>
        </div>

        <div id="co-zscore-plot" className="mt-2 flex max-w-70 flex-1 shrink-0 items-center gap-x-2">
          <div className="w-full">
            <div className="mb-1 text-[10px]/[1.3] font-medium text-neutral-600">
              <div className="flex items-center justify-end gap-x-1">
                <div className="bg-chart-1 size-2 rounded" />
                <div>{rftStore.coTransferStore.condition ?? 'Baseline'}</div>
              </div>
            </div>

            {rftStore.coTransferStore.baseline.values.map((value) => {
              if (!value.refValue) return <div className="my-0.5 h-3.25" />;

              return (
                <ZScorePlotParamRow
                  key={value.parameter?.description}
                  className="my-0.5 overflow-hidden"
                >
                  <ParameterZScoreRange parameterValue={value} />
                  {value?.zScore && !isNaN(value?.zScore) && <ZScorePlotParamDot val={value?.zScore ?? 0} />}
                </ZScorePlotParamRow>
              );
            })}
          </div>
        </div>
      </div>
    </div>
  );
});
