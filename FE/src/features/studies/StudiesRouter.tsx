import {Suspense, createElement, lazy} from 'react';
import {Route, Routes} from 'react-router';

import {getStudies} from './studies-registry.ts';

export function StudiesRouter() {
  const studies = Array.from(getStudies());
  return (
    <Routes>
      {studies.map(({slug, element, lazyElement, ...routeProps}) => (
        <Route
          {...routeProps}
          key={slug}
          path={`${slug}/:id`}
          element={
            element ??
            (lazyElement ? <Suspense fallback={null}>{createElement(lazy(lazyElement))}</Suspense> : null)
          }
        />
      ))}
    </Routes>
  );
}
