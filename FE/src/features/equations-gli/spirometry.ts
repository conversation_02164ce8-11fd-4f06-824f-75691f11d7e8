import {apolloClient} from '@/apollo-client.ts';
import {
  type EquationCalcFn,
  type EquationMetadata,
  type PredResult,
} from '@/features/equations-registry/types.ts';

import {GET_SPIROMETRY_COEFFICIENTS, GET_SPIROMETRY_SPLINES} from './queries.ts';
import {LMSSplines} from './types.ts';
import {mapEthnicityToGLI, processSplineData} from './utils.ts';

/**
 * Get coefficients for spirometry calculation
 */
export async function getSpirometryCoefficients(paramName: string, gender: string) {
  // todo: fix this
  paramName = paramName === 'VC' ? 'FVC' : paramName;

  // Replace FEV1/FVC or FEV1/VC with FER
  paramName = paramName === 'FEV1/FVC' || paramName === 'FEV1/VC' ? 'FER' : paramName;

  const {data} = await apolloClient.query({
    query: GET_SPIROMETRY_COEFFICIENTS,
    variables: {parameter: paramName, gender},
    fetchPolicy: 'cache-first',
  });

  if (!(data.pred_lms_coeff && data.pred_lms_coeff.length > 0)) {
    return null;
  }

  const coeffData = data.pred_lms_coeff[0];
  return {
    a0: coeffData.intercept_m || 0,
    a1: coeffData.height_m || 0,
    a2: coeffData.age_m || 0,
    a3: coeffData.afram_m || 0,
    a4: coeffData.neastasia_m || 0,
    a5: coeffData.seastasia_m || 0,
    a6: coeffData.othermixed_m || 0,
    p0: coeffData.intercept_s || 0,
    p1: coeffData.age_s || 0,
    p2: coeffData.afram_s || 0,
    p3: coeffData.neastasia_s || 0,
    p4: coeffData.seastasia_s || 0,
    p5: coeffData.othermixed_s || 0,
    q0: coeffData.intercept_l || 0,
    q1: coeffData.age_l || 0,
  } as const;
}

/**
 * Get splines for spirometry calculation
 */
export async function getSpirometrySplines(
  paramName: string,
  predMetadata: EquationMetadata
): Promise<LMSSplines> {
  paramName = paramName === 'VC' ? 'FVC' : paramName;

  // Replace FEV1/FVC or FEV1/VC with FER
  paramName = paramName === 'FEV1/FVC' || paramName === 'FEV1/VC' ? 'FER' : paramName;

  const {data} = await apolloClient.query({
    query: GET_SPIROMETRY_SPLINES,
    variables: {
      age: predMetadata.age_clip,
    },
    fetchPolicy: 'cache-first',
  });
  const targetAge = predMetadata.age_clip;

  return processSplineData(
    data,
    predMetadata.age_clip_result,
    targetAge,
    paramName,
    predMetadata.gender_for_rfts
  );
}

/**
 * Calculate spirometry predictions
 */
export const calculatePredictionsSpiro = (async (
  paramName: string,
  predMetadata: EquationMetadata,
  testResult?: number
): Promise<PredResult> => {
  // Adjust test result if FER
  let adjustedTestResult = testResult;
  if (paramName === 'FER' && testResult !== undefined) {
    adjustedTestResult = testResult / 100; // GLI uses FER as a fraction
  }

  // Map ethnicity to GLI ethnicity flags
  const {gBlack, gNEAsia, gSEAsia, gOther} = mapEthnicityToGLI(predMetadata.ethnicity);

  // Get splines and coefficients
  const splines = await getSpirometrySplines(paramName, predMetadata);
  const coefficients = await getSpirometryCoefficients(paramName, predMetadata.gender_for_rfts);

  if (!coefficients) {
    throw new Error(
      `Coefficients not found for parameter ${paramName} and gender ${predMetadata.gender_for_rfts}`
    );
  }

  // Calculate LMS values
  const L = coefficients.q0 + coefficients.q1 * Math.log(predMetadata.age_clip) + splines.LSpline;
  const M = Math.exp(
    coefficients.a0 +
      coefficients.a1 * Math.log(predMetadata.Htcm_clip) +
      coefficients.a2 * Math.log(predMetadata.age_clip) +
      coefficients.a3 * gBlack +
      coefficients.a4 * gNEAsia +
      coefficients.a5 * gSEAsia +
      coefficients.a6 * gOther +
      splines.MSpline
  );
  const S = Math.exp(
    coefficients.p0 +
      coefficients.p1 * Math.log(predMetadata.age_clip) +
      coefficients.p2 * gBlack +
      coefficients.p3 * gNEAsia +
      coefficients.p4 * gSEAsia +
      coefficients.p5 * gOther +
      splines.SSpline
  );

  // Calculate Lower Limit of Normal
  const LLN = Math.exp(Math.log(M) + Math.log(1 - 1.645 * L * S) / L);

  // Format results
  const results: PredResult = {
    mpv: paramName === 'FER' ? M * 100 : M,
    lln: paramName === 'FER' ? LLN * 100 : LLN,
  };

  // Calculate Z-score if test result provided
  if (adjustedTestResult !== undefined && adjustedTestResult !== 0) {
    results.zscore = ((adjustedTestResult / M) ** L - 1) / (L * S);
  }

  if (results.zscore === Infinity) results.zscore = undefined;
  if (results.mpv === Infinity) results.zscore = undefined;
  if (results.lln === Infinity) results.zscore = undefined;
  if (results.uln === Infinity) results.zscore = undefined;

  return results;
}) satisfies EquationCalcFn;
