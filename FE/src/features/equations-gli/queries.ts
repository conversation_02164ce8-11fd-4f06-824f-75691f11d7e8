import {graphql} from '@/graphql.ts';


export const GET_SPIROMETRY_COEFFICIENTS = graphql(`
  query GetSpirometryCoefficients($parameter: String!, $gender: String!) {
    pred_lms_coeff(where: {parameter: {_eq: $parameter}, gender: {_eq: $gender}}) {
      intercept_m
      height_m
      age_m
      afram_m
      neastasia_m
      seastasia_m
      othermixed_m
      intercept_s
      age_s
      afram_s
      neastasia_s
      seastasia_s
      othermixed_s
      intercept_l
      age_l
    }
  }
`);

export const GET_SPIROMETRY_SPLINES = graphql(`
  query GetSpirometrySplines($age: float8!) {
    # For exact match
    exact_match: pred_gli_lookup(where: {age: {_eq: $age}}) {
      age
      fev1_lspline_male
      fev1_mspline_male
      fev1_sspline_male
      fvc_lspline_male
      fvc_mspline_male
      fvc_sspline_male
      fer_lspline_male
      fer_mspline_male
      fer_sspline_male
      fef2575_lspline_male
      fef2575_mspline_male
      fef2575_sspline_male
      fef75_lspline_male
      fef75_mspline_male
      fef75_sspline_male
      fev1_lspline_female
      fev1_mspline_female
      fev1_sspline_female
      fvc_lspline_female
      fvc_mspline_female
      fvc_sspline_female
      fer_lspline_female
      fer_mspline_female
      fer_sspline_female
      fef2575_lspline_female
      fef2575_mspline_female
      fef2575_sspline_female
      fef75_lspline_female
      fef75_mspline_female
      fef75_sspline_female
    }
    # For upper bound
    upper_bound: pred_gli_lookup(where: {age: {_gte: $age}}, order_by: {age: asc}, limit: 1) {
      age
      fev1_lspline_male
      fev1_mspline_male
      fev1_sspline_male
      fvc_lspline_male
      fvc_mspline_male
      fvc_sspline_male
      fer_lspline_male
      fer_mspline_male
      fer_sspline_male
      fef2575_lspline_male
      fef2575_mspline_male
      fef2575_sspline_male
      fef75_lspline_male
      fef75_mspline_male
      fef75_sspline_male
      fev1_lspline_female
      fev1_mspline_female
      fev1_sspline_female
      fvc_lspline_female
      fvc_mspline_female
      fvc_sspline_female
      fer_lspline_female
      fer_mspline_female
      fer_sspline_female
      fef2575_lspline_female
      fef2575_mspline_female
      fef2575_sspline_female
      fef75_lspline_female
      fef75_mspline_female
      fef75_sspline_female
    }
    # For lower bound
    lower_bound: pred_gli_lookup(where: {age: {_lte: $age}}, order_by: {age: desc}, limit: 1) {
      age
      fev1_lspline_male
      fev1_mspline_male
      fev1_sspline_male
      fvc_lspline_male
      fvc_mspline_male
      fvc_sspline_male
      fer_lspline_male
      fer_mspline_male
      fer_sspline_male
      fef2575_lspline_male
      fef2575_mspline_male
      fef2575_sspline_male
      fef75_lspline_male
      fef75_mspline_male
      fef75_sspline_male
      fev1_lspline_female
      fev1_mspline_female
      fev1_sspline_female
      fvc_lspline_female
      fvc_mspline_female
      fvc_sspline_female
      fer_lspline_female
      fer_mspline_female
      fer_sspline_female
      fef2575_lspline_female
      fef2575_mspline_female
      fef2575_sspline_female
      fef75_lspline_female
      fef75_mspline_female
      fef75_sspline_female
    }
  }
`);

export const GET_TLCO_SPLINES = graphql(`
  query GetTLCOSplines($parameter: String!, $age: String!, $gender: String!) {
    # For exact match
    exact_match: pred_gli_tlco_lookup(
      where: {age: {_eq: $age}, parameter: {_eq: $parameter}, sex: {_eq: $gender}}
    ) {
      age
      mspline
      lspline
      sspline
    }
    # For upper bound
    upper_bound: pred_gli_tlco_lookup(
      where: {age: {_gte: $age}, parameter: {_eq: $parameter}, sex: {_eq: $gender}}
      order_by: {age: asc}
      limit: 1
    ) {
      age
      mspline
      lspline
      sspline
    }
    # For lower bound
    lower_bound: pred_gli_tlco_lookup(
      where: {age: {_lte: $age}, parameter: {_eq: $parameter}, sex: {_eq: $gender}}
      order_by: {age: desc}
      limit: 1
    ) {
      age
      mspline
      lspline
      sspline
    }
  }
`);

export const GET_LV_SPLINES = graphql(`
  query GetLVSplines($parameter: String!, $age: String!, $gender: String!) {
    # For exact match
    exact_match: pred_gli_lv_lookup(
      where: {age: {_eq: $age}, parameter: {_eq: $parameter}, sex: {_eq: $gender}}
    ) {
      age
      mspline
      lspline
      sspline
    }
    # For upper bound
    upper_bound: pred_gli_lv_lookup(
      where: {age: {_gte: $age}, parameter: {_eq: $parameter}, sex: {_eq: $gender}}
      order_by: {age: asc}
      limit: 1
    ) {
      age
      mspline
      lspline
      sspline
    }
    # For lower bound
    lower_bound: pred_gli_lv_lookup(
      where: {age: {_lte: $age}, parameter: {_eq: $parameter}, sex: {_eq: $gender}}
      order_by: {age: desc}
      limit: 1
    ) {
      age
      mspline
      lspline
      sspline
    }
  }
`);
