import {useApolloClient} from '@apollo/client';
import {El<PERSON>sisV<PERSON><PERSON>, ScanSearch, Trash2} from 'lucide-react';

import {Paths} from '@/api-types/routePaths.ts';
import {urlWithArgs} from '@/api-types/url.ts';
import {axiosInstance} from '@/axios.ts';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {apiQueryOptions} from '@/hooks/use-api-query.ts';
import {useConfirmation} from '@/hooks/use-confirmation.tsx';
import {queryClient} from '@/query-client.ts';
import {allAlarmsQuery, allOpenAlarmsCountQuery} from "@/graphql/qc.ts";
import authStore from "@/store/auth.store.ts";

function ReviewOrDeleteSessionMenu({
  expandRowToggleable,
  sessionId,
  controlMethodEquipmentId,
}: {
  expandRowToggleable: () => void;
  sessionId: number;
  controlMethodEquipmentId: number;
  hasViolations?: boolean;
}) {
  const confirm = useConfirmation();
  const client = useApolloClient();
  function handleConfirmation() {
    confirm({
      title: 'Delete Session',
      description: 'Do you really want to delete this session? This is irreversible.',
      actionLabel: 'Delete Session',
      callback: async () => {
        try {
          await axiosInstance.delete(urlWithArgs(Paths.QC_SESSIONS_DETAIL, {sessionId}), {
            params: {control_method_equipment_id: controlMethodEquipmentId},
          });
          const p1 = queryClient.invalidateQueries(
            apiQueryOptions(Paths.QC_SESSIONS, {}, {control_method_equipment_id: controlMethodEquipmentId})
          );
          const p2 = queryClient.invalidateQueries(
            apiQueryOptions(
              Paths.QC_ALARMS,
              {},
              {
                control_method_equipment_id: controlMethodEquipmentId,
                site_id: authStore.tokenSiteId,
              }
            )
          );
          client.refetchQueries({include: [allOpenAlarmsCountQuery, allAlarmsQuery]});
          return await Promise.allSettled([p1, p2]);
        } catch (e) {
          console.log('error deleting session: ', e);
        }
      },
    });
  }

  return (
    <div className="flex items-center justify-end gap-2">
      <DropdownMenu>
        <DropdownMenuTrigger className="cursor-pointer">
          <EllipsisVertical className="h-5 w-5" />
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end">
          <DropdownMenuLabel>Actions</DropdownMenuLabel>
          <DropdownMenuSeparator />
          <DropdownMenuItem
            className="hover:bg-brand-50! hover:text-brand-600! cursor-pointer"
            onClick={expandRowToggleable}
          >
            <ScanSearch className="h-4 w-4" />
            Edit / Review
          </DropdownMenuItem>
          <DropdownMenuItem
            className="flex cursor-pointer items-center gap-x-2 text-red-700 hover:bg-red-50! hover:text-red-500!"
            onClick={handleConfirmation}
          >
            <Trash2 className="h-4 w-4" />
            Delete
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>
    </div>
  );
}

export default ReviewOrDeleteSessionMenu;
