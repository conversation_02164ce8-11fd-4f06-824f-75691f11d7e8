import clsx from 'clsx';
import {Suspense, lazy} from 'react';
import {Focusable, Tooltip, TooltipTrigger} from 'react-aria-components';
import {useParams} from 'react-router';

import {useAtomValue} from 'jotai/react';
import {Plus} from 'lucide-react';

import {Paths} from '@/api-types/routePaths.ts';
import {Alert, ControlRuleInterval, Parameter, Session} from '@/api-types/schema.ts';
import {useDialogState} from '@/components/modal-state-provider.tsx';
import {Button} from '@/components/ui/button.tsx';
import {useApiQuery} from '@/hooks/use-api-query.ts';
import {
  SessionsTable,
  tableSelection$,
} from '@/views/quality-control/components/device-summary/SessionsTable.tsx';
import {AddControlIntervalDialog} from '@/views/quality-control/components/device-summary/add-control-interval-dialog.tsx';

import AddSessionDialog from '../components/device-summary/add-session-dialog.tsx';
import Legends from '../components/device-summary/legends.tsx';
import ParameterGraphs from '../components/parameter-graphs';
import authStore from "@/store/auth.store.ts";

const ReportsDialog = lazy(() => import('../components/device-summary/reports-dialog'));

function DeviceSummary() {
  const {controlEquipmentId} = useParams();

  const [, setOpenReportsDialog] = useDialogState('reports-dialogue');

  const {data: parameters, isLoading: isParamsLoading} = useApiQuery(
    Paths.QC_PARAMETERS,
    {},
    {control_methods_equipments_id: controlEquipmentId}
  );

  const {data: sessions, isLoading: isSessionsLoading} = useApiQuery(
    Paths.QC_SESSIONS,
    {},
    {control_method_equipment_id: controlEquipmentId}
  );

  const {data: alarms, isLoading: isAlarmsLoading} = useApiQuery(
    Paths.QC_ALARMS,
    {},
    {
      control_method_equipment_id: controlEquipmentId,
      site_id: authStore.tokenSiteId,
    }
  );

  const {data: controlRuleIntervals} = useApiQuery(
    Paths.QC_CONTROL_RULE_INTERVALS,
    {},
    {
      control_methods_equipments_id: controlEquipmentId,
    }
  );

  return (
    <>
      <div className="mb-4 flex items-center justify-between">
        <div className="flex gap-2">
          <ButtonsToolbar />

          <Button
            className="flex shrink-0 cursor-pointer items-center gap-x-2 bg-white capitalize"
            variant="outlined"
            type="button"
            size="small"
            onPress={() => setOpenReportsDialog(true)}
          >
            Export Report
          </Button>
        </div>
        <Legends />
      </div>

      <div className="flex justify-between gap-x-4">
        <SessionsTable />

        <ParameterGraphs
          controlRuleIntervals={controlRuleIntervals as ControlRuleInterval[]}
          sessions={sessions as Session[]}
          parameters={parameters as Parameter[]}
          alarms={alarms as Alert[]}
          type="absolute"
          isLoading={isParamsLoading || isSessionsLoading || isAlarmsLoading}
        />
      </div>

      <AddSessionDialog />

      <AddControlIntervalDialog />

      <Suspense fallback={<div />}>
        <ReportsDialog parameters={parameters as Parameter[]} />
      </Suspense>
    </>
  );
}

function ButtonsToolbar() {
  const tableSelection = useAtomValue(tableSelection$);
  const [, setOpenSessionDialog] = useDialogState('add-session-dialogue');
  const [, openControlIntervalDialog] = useDialogState('add-control-interval-dialog');

  return (
    <>
      <Button
        data-testid="add-session-button"
        className="flex shrink-0 cursor-pointer items-center gap-x-2 capitalize"
        type="button"
        variant={!tableSelection ? 'solid' : 'outlined'}
        size="small"
        onPress={() => setOpenSessionDialog(true)}
      >
        <Plus className="h-4 w-4" />
        add session
      </Button>

      <TooltipTrigger
        delay={0}
        closeDelay={100}
      >
        <Focusable>
          <span
            role="button"
            className="shrink-0"
          >
            <Button
              data-testid="add-control-interval-button"
              className="flex shrink-0 items-center gap-x-2 capitalize"
              type="button"
              variant={tableSelection ? 'solid' : 'outlined'}
              size="small"
              isDisabled={!tableSelection}
              onPress={() => openControlIntervalDialog(true)}
            >
              Add Control Interval
            </Button>
          </span>
        </Focusable>
        <Tooltip className={clsx('react-aria-Tooltip mb-2 bg-black/65', !!tableSelection && 'hidden')}>
          Select a range of cells to form a control interval
        </Tooltip>
      </TooltipTrigger>
    </>
  );
}

export default DeviceSummary;
