import { useState } from 'react';
import { useLazyQuery } from '@apollo/client';
import { SearchIcon } from 'lucide-react';
import { searchPatientsQuery } from '@/graphql/patients.ts';

interface Props {
  siteId: string;
  onSelect: (patient: any) => void;
}

export default function PatientSearchBox({ siteId, onSelect }: Props) {
  const [query, setQuery] = useState('');
  const [search, { data, loading }] = useLazyQuery(searchPatientsQuery, {
    fetchPolicy: 'network-only',
  });

  const handleChange = (value: string) => {
    setQuery(value);
    if (value.trim().length >= 2) {
      search({
        variables: {
          searchText: value,
          genderCodes: null,
          siteId,
          limit: 10,
          offset: 0,
        },
      });
    }
  };

  const results = data?.search_pas_pt || [];

  return (
    <div className="relative w-full">
      <div className="relative">
        <SearchIcon className="absolute left-2 top-2.5 h-4 w-4 text-neutral-400" />
        <input
          type="text"
          value={query}
          onChange={(e) => handleChange(e.target.value)}
          placeholder="Search patients..."
          className="w-full h-10 pl-8 pr-3 rounded-sm border border-neutral-300 text-sm placeholder:text-neutral-500 focus:outline-none focus:ring-2 focus:ring-blue-300"
        />
      </div>

      {query.length >= 2 && (
        <div className="absolute z-10 mt-1 w-full bg-white border border-gray-200 rounded shadow max-h-64 overflow-y-auto">
          {loading && (
            <div className="px-4 py-2 text-sm text-gray-500">Loading...</div>
          )}

          {!loading && results.length === 0 && (
            <div className="px-4 py-2 text-sm text-gray-500">No results</div>
          )}

          {results.map((item: any) => (
            <div
              key={item.patientid}
              onClick={() => {
                onSelect({
                  ...item,
                  first_name: item.firstname,
                  last_name: item.surname,
                  gender: item.gender_code,
                  date_of_birth: item.dob,
                });
                setQuery('');
              }}
              className="flex items-center gap-x-3 px-4 py-2 text-sm text-gray-800 hover:bg-blue-50 cursor-pointer"
            >
            <div className="flex h-8 w-8 items-center justify-center rounded-full bg-blue-600 text-white text-sm font-semibold">
                {item.firstname?.[0]}
              </div>
              <div className="flex flex-col">
                <span className="font-medium">{item.firstname} {item.surname}</span>
                <span className="text-xs text-gray-600">
                  {item.ur} • {item.gender_code} • DOB: {item.dob}
                </span>
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
}
