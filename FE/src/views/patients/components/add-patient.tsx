import clsx from 'clsx';
import {useEffect, useState} from 'react';
import {
  DateInput,
  DatePicker,
  DateSegment,
  Dialog,
  Group,
  ListBox,
  ListBoxItem,
  Modal,
  Popover,
  Button as RACButton,
  Select,
  SelectValue,
} from 'react-aria-components';
import {useForm} from 'react-hook-form';
import {useNavigate} from 'react-router';

import {useLazyQuery, useMutation, useQuery} from '@apollo/client';
import {ChevronDown, Info, Plus} from 'lucide-react';

import {CalendarIconly} from '@/components/icons/CalenderIconly.tsx';
import {CloseRemoveIconly} from '@/components/icons/CloseRemoveIconly.tsx';
import {useDialogState} from '@/components/modal-state-provider.tsx';
import {Input, Label} from '@/components/ui/Field';
import {Button} from '@/components/ui/button';
import {Calendar} from '@/components/ui/calendar';
import {
  FieldError,
  Form,
  FormField,
  FormRootErrors,
  NumberFormField,
  SelectFormField,
  TextFormField,
} from '@/components/ui/form';
import {FieldArray} from '@/components/ui/form/FieldArray/FieldArray';
import {FieldArrayItems} from '@/components/ui/form/FieldArray/FieldArrayItems';
import {WatchState} from '@/components/ui/form/WatchState.tsx';
import {Tooltip, TooltipContent, TooltipTrigger} from '@/components/ui/tooltip.tsx';
import {getAboriginalList, getLanguageList, getNationalities} from '@/graphql/lists.ts';
import {createPatient, createPatientUrNumbers} from '@/graphql/patient-mutations';
import {
  demographicOptions,
  getSiteConfigHealthServicesList,
  searchPatientsQuery,
} from '@/graphql/patients.ts';
import {getPreferenceField} from '@/graphql/preferences.ts';
import {checkUrExists} from '@/graphql/ur.ts';
import {useGlobalStoreLoader} from '@/store/global.store.ts';
import {Patient} from '@/store/patient';
import authStore from "@/store/auth.store.ts";

export default function AddPatient() {
  useGlobalStoreLoader();
  const primaryHealthServiceCode = authStore.site?.getConfig('healthservice_code_primary');
  const form = useForm();
  const [isOpen, setIsOpen] = useDialogState('add-patient');
  const [checkUr] = useLazyQuery(checkUrExists);
  const [addPatient, {loading: addingPatient}] = useMutation(createPatient);
  const [addPatientUrNumbers] = useMutation(createPatientUrNumbers);
  const navigate = useNavigate();
  const [statusValues, setStatusValues] = useState<{
    [key: number]: {status: string; merge: string; error: string | null; healthcare?: string};
  }>({
    0: {
      status: 'primary',
      merge: '',
      error: null,
      healthcare: primaryHealthServiceCode,
    },
  });

  const resetFormAndState = () => {
    setStatusValues({});
    form.reset();
    form.clearErrors();
    if (primaryHealthServiceCode) {
      setStatusValues({
        0: {
          status: 'primary',
          merge: '',
          error: null,
          healthcare: primaryHealthServiceCode,
        },
      });
    }
  };

  useEffect(() => {
    return () => {
      resetFormAndState();
    };
  }, [form]);

  const {data: options} = useQuery(demographicOptions);
  const {data: titles} = useQuery(getPreferenceField, {variables: {fieldName: 'Titles'}});
  const {data: nationalities} = useQuery(getNationalities);
  const {data: aboriginalStatus} = useQuery(getAboriginalList);
  const {data: languages} = useQuery(getLanguageList);
  const {data: SiteConfigHealthServices} = useQuery(getSiteConfigHealthServicesList);

  const healthServicesList = options?.list_healthservices?.filter((hs) => hs?.code !== null);
  const siteConfigHealthServices = SiteConfigHealthServices?.site_config_healthservices
    ?.filter((schs) => schs.enabled)
    .map((schs) => schs.hsid);

  const healthServices = healthServicesList?.filter((hs) => siteConfigHealthServices?.includes(hs?.code!));

  const calculateStatusAndMerge = async (ur: string, healthservice: string, index: number) => {
    if (!ur || !healthservice) {
      // If UR or healthcare is empty, set default values
      setStatusValues((prev) => ({
        ...prev,
        [index]: {
          status: index === 0 ? 'primary' : 'other_healthservice',
          merge: index === 0 ? '' : 'primary',
          error: null,
          healthcare: healthservice,
        },
      }));
      // Clear any existing errors on the primary MRN field
      if (index === 0) {
        form.clearErrors('ur');
        form.clearErrors(`mrns.${index}.ur`);
      }
      return;
    }

    try {
      // Check if UR already exists
      const result = await checkUr({variables: {ur: ur.toString(), ur_hsid: healthservice.toString()}});
      const existingUr = result.data?.pas_pt_ur_numbers?.[0];

      // First UR is always primary with the system's primary health service
      if (index === 0) {
        if (existingUr) {
          // If the UR already exists, show an error
          setStatusValues((prev) => ({
            ...prev,
            [index]: {
              status: 'error',
              merge: '',
              error: 'MRN already exists for another patient.',
              healthcare: healthservice,
            },
          }));
          // Set error on the primary MRN field
          form.setError(`mrns.${index}.ur`, {
            type: 'manual',
            message: 'MRN already exists for another patient.',
          });
          if (index === 0) {
            form.setError('ur', {
              type: 'manual',
              message: 'MRN already exists for another patient.',
            });
          }
        } else {
          // First UR is always primary
          setStatusValues((prev) => ({
            ...prev,
            [index]: {status: 'primary', merge: '', error: null, healthcare: healthservice},
          }));
          // Clear any existing errors
          form.clearErrors(`mrns.${index}.ur`);
          form.clearErrors(`mrns.${index}.healthcare`);
          if (index === 0) {
            form.clearErrors('ur');
          }
        }
        return;
      }

      // For additional URs
      if (existingUr) {
        // Check if the UR exists with the same health service
        if (existingUr.ur_hsid === healthservice.toString()) {
          // Error: Cannot add a UR with the same health service
          setStatusValues((prev) => ({
            ...prev,
            [index]: {
              status: 'error',
              merge: 'primary',
              error: 'MRN for a patient already exists with this health service.',
              healthcare: healthservice,
            },
          }));
          // Set error on the relevant healthcare field
          form.setError(`mrns.${index}.healthcare`, {
            type: 'manual',
            message: 'MRN for a patient already exists with this health service.',
          });
        } else if (result.data?.pas_pt?.[0]?.ur_mergeto) {
          // Error: UR is already merged
          setStatusValues((prev) => ({
            ...prev,
            [index]: {
              status: 'error',
              merge: 'primary',
              error: 'Entered primary UR is merged and not primary.',
              healthcare: healthservice,
            },
          }));

          // Set error on the relevant healthcare field
          form.setError(`mrns.${index}.healthcare`, {
            type: 'manual',
            message: 'Entered primary UR is merged and not primary.',
          });
        }
      } else {
        // UR doesn't exist, check if it's from a different health service
        // Get the primary healthcare service from the form state
        const formState = statusValues[0];
        const primaryHealthService = formState ? formState.healthcare : primaryHealthServiceCode;

        if (healthservice === primaryHealthService) {
          // If it's from the same health service as the primary MRN, show an error
          setStatusValues((prev) => ({
            ...prev,
            [index]: {
              status: 'error',
              merge: 'primary',
              error: 'Cannot add another MRN with the same health service.',
              healthcare: healthservice,
            },
          }));

          // Set error on the relevant healthcare field
          form.setError(`mrns.${index}.healthcare`, {
            // type: 'manual',
            message: 'Cannot add another MRN with the same health service.',
          });
        } else {
          // If it's from a different health service, set as other_healthservice
          setStatusValues((prev) => ({
            ...prev,
            [index]: {
              status: 'other_healthservice',
              merge: 'primary',
              error: null,
              healthcare: healthservice,
            },
          }));
          // Clear any existing errors
          form.clearErrors(`mrns.${index}.ur`);
          form.clearErrors(`mrns.${index}.healthcare`);
          form.clearErrors('ur');
        }
      }
    } catch (error) {
      console.error('Error checking UR:', error);
      // Default fallback
      setStatusValues((prev) => ({
        ...prev,
        [index]: {
          status: index === 0 ? 'primary' : 'other_healthservice',
          merge: index === 0 ? '' : 'primary',
          error: null,
          healthcare: healthservice,
        },
      }));
    }
  };

  return (
    <Modal
      isOpen={isOpen}
      onOpenChange={(open) => {
        if (!open) {
          resetFormAndState();
        }
        setIsOpen(open);
      }}
      className="react-aria-Modal h-200 w-150 overflow-y-auto p-0"
      data-testid="add-patient-modal"
    >
      <Dialog>
        <div className="sticky top-0 z-10 flex items-center justify-between border-b border-neutral-200 bg-white px-6 py-4">
          <div className="text-sm font-semibold text-neutral-800">Add Patient</div>
          <RACButton slot="close">
            <CloseRemoveIconly />
          </RACButton>
        </div>

        <Form
          control={form}
          onSubmit={async (data) => {
            const hasErrors = Object.values(statusValues).some((value) => value.status === 'error');
            if (hasErrors) {
              return false;
            }

            try {
              // Execute the mutation to create the patient
              const result = await addPatient({
                variables: data,
                refetchQueries: [searchPatientsQuery],
              });

              const newPatientId = result.data?.insert_pas_pt_one?.patientid;

              if (newPatientId && data.mrns && data.mrns.length > 1) {
                try {
                  const additionalMrns = data.mrns
                    .slice(1) // Skip the first MRN (primary)
                    .filter(
                      (mrn: {ur: number; healthcare: string; status: string}) => mrn.ur && mrn.healthcare
                    )
                    .map((mrn: {ur: number; healthcare: string; status: string}) => ({
                      patientid: newPatientId,
                      ur: mrn.ur,
                      ur_hsid: mrn?.healthcare?.toString(),
                      ur_status: mrn.status || 'other_healthservice',
                    }));

                  if (additionalMrns.length > 0) {
                    await addPatientUrNumbers({
                      variables: {
                        objects: additionalMrns,
                      },
                    });
                  }
                } catch (error) {
                  console.error('Error adding additional MRNs:', error);
                }
              }
              resetFormAndState();
              setIsOpen(false);
              if (newPatientId) {
                navigate(`/patients/${newPatientId}`);
              }

              return true;
            } catch (error) {
              console.error('Error creating patient:', error);
              form.setError('root', {
                type: 'manual',
                message:
                  error instanceof Error
                    ? error.message
                    : 'An error occurred while creating the patient. Please try again.',
              });

              return false;
            }
          }}
          transformData={(data) => {
            const primaryMrn = data.mrns?.[0] || {};

            return {
              ...data,
              // Primary MRN data
              ur: primaryMrn.ur || data.ur,
              ur_hsid: primaryMrn.healthcare || data.healthcare || primaryHealthServiceCode,
              ur_status: 'primary',

              // Patient personal information
              title: data.title,
              firstname: data.firstname,
              surname: data.surname,
              middlename: data.middlename,
              dob: data.dob ? data.dob.toDate().toISOString().split('T')[0] : null,
              gender_code: options?.pred_ref_genders.find((g) => g.id === data.gender)?.gender_code,
              gender_forrfts_code: data?.gender_for_rft?.toString(),

              // Contact information
              email: data.email,
              phone_home: data.phone_home?.toString(),
              phone_mobile: data.phone_mobile?.toString(),
              phone_work: data.phone_work?.toString(),

              // Address information
              address_1: data.address_1,
              address_2: data.address_2,
              suburb: data.suburb,
              postcode: data.postcode?.toString(),

              // Additional information
              countryofbirth_code: data.countryofbirth_code,
              preferredlanguage_code: data.preferred_language?.toString(),
              aboriginalstatus_code: data.aboriginalstatus_code,
              medicare_no: data.medicare_no?.toString(),
              medicare_expirydate: data.medicare_expiry
                ? data.medicare_expiry.toDate().toISOString().split('T')[0]
                : null,
              death_indicator: data.death_indicator,
              death_date: data.death_date ? data.death_date.toDate().toISOString().split('T')[0] : null,
              race_forrfts_code: data.ethnicity?.toString() || data.race_forrfts_code,
            };
          }}
          onReset={() => {
            resetFormAndState();
          }}
        >
          <FormRootErrors />
          {Object.values(statusValues).some((value) => value.status === 'error') && (
            <div className="mx-6 mt-4 rounded-sm border border-red-200 bg-red-50 p-3 text-sm text-red-600">
              Please fix the errors in the MRN fields before submitting.
            </div>
          )}
          <div className="isolate flex flex-col gap-5 divide-y-1 divide-neutral-200 bg-white p-6">
            {/*GENERAL INFO HEADING*/}

            <div className="space-y-4 pb-6">
              <div className="font-bold text-neutral-800 uppercase">General Info</div>

              {/* MRN + HEALTHCARE */}
              <div className="flex items-start gap-x-4">
                <TextFormField
                  className="w-full"
                  name="ur"
                  required
                  defaultValue={null}
                >
                  <Label className="text-xs font-semibold text-neutral-600">Primary MRN</Label>
                  <Input
                    placeholder="Enter Primary MRN"
                    className="react-aria-Input min-w-60 rounded-sm text-neutral-800"
                    onChange={(e) => {
                      const value = e.target.value;
                      form.setValue('mrns.0.ur', value);
                    }}
                    onBlur={(e) => {
                      const value = e.target.value;
                      if (primaryHealthServiceCode && value) {
                        calculateStatusAndMerge(value.toString(), primaryHealthServiceCode.toString(), 0);
                      } else {
                        // Clear errors if the field is empty
                        form.clearErrors('ur');
                        form.clearErrors('mrns.0.ur');
                        form.clearErrors('mrns.0.healthcare');
                      }
                    }}
                  />
                  <FieldError />
                </TextFormField>

                <TextFormField
                  className="w-full"
                  defaultValue={
                    healthServices?.find((hs) => hs?.code?.toString() === primaryHealthServiceCode)
                      ?.description
                  }
                  name="healthservice"
                  isReadOnly={true}
                >
                  <Label className="text-xs font-semibold text-neutral-600">Healthcare Facility</Label>
                  <Input
                    placeholder="Healthcare Facility"
                    className="react-aria-Input min-w-60 cursor-not-allowed rounded-sm bg-neutral-50 text-neutral-800 disabled:text-neutral-700"
                    disabled={true}
                    value={
                      healthServicesList?.find((hs) => hs?.code?.toString() === primaryHealthServiceCode)
                        ?.description ?? undefined
                    }
                  />
                </TextFormField>
              </div>

              {/*TITLE + SURNAME + FIRSTNAME*/}
              <div className="grid grid-cols-[8rem_1fr_1fr] items-start gap-x-2">
                <SelectFormField
                  className="react-aria-Select w-full"
                  placeholder="Select Title"
                  name="title"
                >
                  <Label className="text-xs text-neutral-600">Title</Label>
                  <RACButton className="react-aria-Button w-full rounded-sm">
                    <SelectValue className="react-aria-SelectValue text-sm" />
                    <ChevronDown />
                  </RACButton>
                  <Popover>
                    <ListBox items={titles?.prefs_fields?.[0]?.prefs_fielditems ?? []}>
                      {(item) => (
                        <ListBoxItem
                          id={item.fielditem?.toString()}
                          textValue={item.fielditem?.toString()}
                        >
                          <div className="max-w-full overflow-hidden text-ellipsis whitespace-nowrap">
                            {item.fielditem}
                          </div>
                        </ListBoxItem>
                      )}
                    </ListBox>
                  </Popover>
                </SelectFormField>
                <TextFormField
                  className="react-aria-TextField w-full flex-1"
                  name="surname"
                  required
                >
                  <Label className="text-xs font-semibold text-neutral-600">Surname</Label>
                  <Input
                    placeholder="Enter Surname"
                    className="react-aria-Input rounded-sm text-neutral-800"
                  />
                  <FieldError />
                </TextFormField>

                <TextFormField
                  className="react-aria-TextField w-full flex-1"
                  name="firstname"
                  required
                >
                  <Label className="text-xs font-semibold text-neutral-600">First Name</Label>
                  <Input
                    placeholder="Enter First Name"
                    className="react-aria-Input rounded-sm text-neutral-800"
                  />
                  <FieldError />
                </TextFormField>
              </div>

              <div className="flex items-start gap-x-4">
                <SelectFormField
                  name="gender"
                  placeholder="Select Gender"
                  className="react-aria-Select w-full"
                  required
                >
                  <Label className="text-xs text-neutral-600">Gender</Label>
                  <RACButton className="react-aria-Button w-full rounded-sm">
                    <SelectValue className="react-aria-SelectValue text-sm" />
                    <ChevronDown />
                  </RACButton>
                  <Popover>
                    <ListBox>
                      {options?.pred_ref_genders
                        ?.filter((g) => g.list_option_for_demographics)
                        .map((g) => (
                          <ListBoxItem
                            key={g.id}
                            id={g.id}
                            textValue={g?.description ?? undefined}
                          >
                            {g.description}
                          </ListBoxItem>
                        ))}
                    </ListBox>
                  </Popover>
                  <FieldError />
                </SelectFormField>

                {/*DOB*/}
                <FormField
                  name="dob"
                  required
                >
                  <DatePicker
                    granularity="day"
                    hourCycle={24}
                    shouldForceLeadingZeros
                    className="react-aria-DatePicker flex flex-col"
                    data-required
                  >
                    <Label className="text-xs font-semibold text-neutral-600">Date of birth</Label>
                    <Group className="react-aria-Group min-w-68 rounded-sm">
                      <DateInput className="react-aria-DateInput text-neutral-800">
                        {(segment) => <DateSegment segment={segment} />}
                      </DateInput>
                      <RACButton className="react-aria-Button group">
                        <CalendarIconly
                          className="group-data-[focus-visible]:text-brand2-500 h-4.5 w-4.5 text-neutral-600"
                          strokeWidth={1.5}
                        />
                      </RACButton>
                    </Group>
                    <Popover className="react-aria-Popover w-max">
                      <Dialog className="p-4">
                        <Calendar />
                      </Dialog>
                    </Popover>
                    <FieldError />
                  </DatePicker>
                </FormField>
              </div>
            </div>

            {/*DEMOGRAPHICS HEADING*/}

            <div className="space-y-4 pb-6">
              <div className="font-bold text-neutral-800 uppercase">Demographics</div>

              {/*EMAIL + PHONE_HOME*/}
              <div className="flex items-start gap-x-4">
                <TextFormField
                  className="w-full"
                  name="email"
                >
                  <FieldError />
                  <Label className="text-xs font-semibold text-neutral-600">Email</Label>
                  <Input
                    placeholder="Enter Email"
                    className="react-aria-Input min-w-60 rounded-sm text-neutral-800"
                  />
                </TextFormField>

                <NumberFormField
                  className="w-full"
                  formatOptions={{
                    useGrouping: false,
                  }}
                  name="phone_home"
                  defaultValue={null}
                >
                  <FieldError />
                  <Label className="text-xs font-semibold text-neutral-600">Phone (Home)</Label>
                  <Input
                    placeholder="Enter Phone (Home)"
                    className="react-aria-Input min-w-60 rounded-sm text-neutral-800"
                  />
                </NumberFormField>
              </div>

              {/*PHONE_WORK + PHONE_MOBILE*/}
              <div className="flex items-start gap-x-4">
                <NumberFormField
                  className="w-full"
                  formatOptions={{
                    useGrouping: false,
                  }}
                  name="phone_work"
                  defaultValue={null}
                >
                  <FieldError />
                  <Label className="text-xs font-semibold text-neutral-600">Phone (Work)</Label>
                  <Input
                    placeholder="Enter Phone (Work)"
                    className="react-aria-Input min-w-60 rounded-sm text-neutral-800"
                  />
                </NumberFormField>
                <NumberFormField
                  className="w-full"
                  formatOptions={{
                    useGrouping: false,
                  }}
                  name="phone_mobile"
                  defaultValue={null}
                >
                  <FieldError />
                  <Label className="text-xs font-semibold text-neutral-600">Phone (Mobile)</Label>
                  <Input
                    placeholder="Enter Phone (Mobile)"
                    className="react-aria-Input min-w-60 rounded-sm text-neutral-800"
                  />
                </NumberFormField>
              </div>

              {/*ADDRESSES*/}
              <TextFormField
                className="w-full"
                name="address_1"
              >
                <FieldError />
                <Label className="text-xs font-semibold text-neutral-600">Address 1</Label>
                <Input
                  placeholder="Enter Address 1"
                  className="react-aria-Input min-w-60 rounded-sm text-neutral-800"
                />
              </TextFormField>
              <TextFormField
                className="w-full"
                name="address_2"
              >
                <FieldError />
                <Label className="text-xs font-semibold text-neutral-600">Address 2</Label>
                <Input
                  placeholder="Enter Address 2"
                  className="react-aria-Input min-w-60 rounded-sm text-neutral-800"
                />
              </TextFormField>

              {/*SUBURB + POSTCODE*/}
              <div className="flex items-start gap-x-4">
                <TextFormField
                  className="w-full"
                  name="suburb"
                >
                  <FieldError />
                  <Label className="text-xs font-semibold text-neutral-600">Subrub</Label>
                  <Input
                    placeholder="Enter Subrub"
                    className="react-aria-Input min-w-60 rounded-sm text-neutral-800"
                  />
                </TextFormField>
                <NumberFormField
                  className="w-full"
                  name="postcode"
                  defaultValue={null}
                  formatOptions={{
                    useGrouping: false,
                  }}
                >
                  <FieldError />
                  <Label className="text-xs font-semibold text-neutral-600">Postcode</Label>
                  <Input
                    placeholder="Enter Postcode"
                    className="react-aria-Input min-w-60 rounded-sm text-neutral-800"
                  />
                </NumberFormField>
              </div>

              {/* COUNTRY OF BIRTH + PREFFERED LANGUAGAE */}
              <div className="flex items-start gap-x-4">
                <FormField name="countryofbirth_code">
                  <Select
                    className="react-aria-Select w-full"
                    placeholder="Select Country of Birth"
                  >
                    <Label className="text-xs text-neutral-600">Country of Birth</Label>
                    <RACButton className="react-aria-Button w-full min-w-60 rounded-sm">
                      <SelectValue className="react-aria-SelectValue text-sm" />
                      <ChevronDown />
                    </RACButton>
                    <Popover>
                      <ListBox items={nationalities?.list_nationality ?? []}>
                        {(item) => (
                          <ListBoxItem
                            id={item?.code ?? undefined}
                            textValue={item.description?.toString()}
                          >
                            <div className="max-w-full overflow-hidden text-ellipsis whitespace-nowrap">
                              {item.description}
                            </div>
                          </ListBoxItem>
                        )}
                      </ListBox>
                    </Popover>
                  </Select>
                </FormField>

                <FormField name="preferred_language">
                  <Select
                    className="react-aria-Select w-full"
                    placeholder="Select Preferred Language"
                  >
                    <Label className="text-xs text-neutral-600">Preferred Language</Label>
                    <RACButton className="react-aria-Button w-full min-w-60 rounded-sm">
                      <SelectValue className="react-aria-SelectValue text-sm" />
                      <ChevronDown />
                    </RACButton>
                    <Popover>
                      <ListBox items={languages?.list_language?.filter((l) => l.description) ?? []}>
                        {(item) => (
                          <ListBoxItem
                            id={item.code ?? Math.random()}
                            textValue={item.description ?? undefined}
                          >
                            <div className="max-w-full overflow-hidden text-ellipsis whitespace-nowrap">
                              {item.description}
                            </div>
                          </ListBoxItem>
                        )}
                      </ListBox>
                    </Popover>
                  </Select>
                </FormField>
              </div>

              {/*ABORIGINAL STATUS*/}
              <FormField name="aboriginalstatus_code">
                <Select
                  className="react-aria-Select"
                  placeholder="Select Aboriginal Status"
                >
                  <Label className="text-xs text-neutral-600">Aboriginal Status</Label>
                  <RACButton className="react-aria-Button min-w-68 rounded-sm">
                    <SelectValue className="react-aria-SelectValue text-sm" />
                    <ChevronDown />
                  </RACButton>
                  <Popover>
                    <ListBox
                      items={aboriginalStatus?.list_aboriginalstatus?.filter((a) => a.description) ?? []}
                    >
                      {(item) => (
                        <ListBoxItem
                          id={item?.code ?? Math.random()}
                          textValue={item?.description ?? undefined}
                        >
                          <div className="max-w-full overflow-hidden text-ellipsis whitespace-nowrap">
                            {item.description}
                          </div>
                        </ListBoxItem>
                      )}
                    </ListBox>
                  </Popover>
                </Select>
              </FormField>

              {/*MEDICARE NO. + EXPIRY*/}
              <div className="flex items-start gap-x-4">
                <NumberFormField
                  className="w-full"
                  name="medicare_no"
                  defaultValue={null}
                  validate={(val) => val ? Patient.validateMedicareNumber(val) : undefined}
                >
                  <Label className="text-xs font-semibold text-neutral-600">Medicare No.</Label>
                  <Input
                    placeholder="Enter Medicare No."
                    className="react-aria-Input min-w-60 rounded-sm text-neutral-800"
                  />
                  <FieldError />
                </NumberFormField>
                <FormField name="medicare_expiry">
                  <DatePicker
                    granularity="day"
                    hourCycle={24}
                    shouldForceLeadingZeros
                    className="react-aria-DatePicker flex w-full flex-col"
                  >
                    <Label className="text-xs font-semibold text-neutral-600">Medicare Expiry</Label>
                    <Group className="react-aria-Group w-full rounded-sm">
                      <DateInput className="react-aria-DateInput text-neutral-800">
                        {(segment) => <DateSegment segment={segment} />}
                      </DateInput>
                      <RACButton className="react-aria-Button group">
                        <CalendarIconly
                          className="group-data-[focus-visible]:text-brand2-500 h-4.5 w-4.5 text-neutral-600"
                          strokeWidth={1.5}
                        />
                      </RACButton>
                    </Group>
                    <Popover className="react-aria-Popover w-max">
                      <Dialog className="p-4">
                        <Calendar />
                      </Dialog>
                    </Popover>
                    <FieldError />
                  </DatePicker>
                </FormField>
              </div>

              {/*DEATH STATUS + DATE*/}
              <div className="flex items-end gap-x-4">
                <TextFormField
                  className="react-aria-TextField w-full"
                  name="death_status"
                >
                  <Label className="text-xs text-neutral-600">Death Status</Label>
                  <Input
                    placeholder="Enter Death Status"
                    className="react-aria-Input min-w-60 rounded-sm text-neutral-800"
                  />
                  <FieldError />
                </TextFormField>
                <FormField name="death_date">
                  <DatePicker
                    granularity="day"
                    hourCycle={24}
                    shouldForceLeadingZeros
                    className="react-aria-DatePicker flex w-full flex-col"
                  >
                    <Label className="text-xs font-semibold text-neutral-600">Date</Label>
                    <Group className="react-aria-Group w-full rounded-sm">
                      <DateInput className="react-aria-DateInput text-neutral-800">
                        {(segment) => <DateSegment segment={segment} />}
                      </DateInput>
                      <RACButton className="react-aria-Button group">
                        <CalendarIconly
                          className="group-data-[focus-visible]:text-brand2-500 h-4.5 w-4.5 text-neutral-600"
                          strokeWidth={1.5}
                        />
                      </RACButton>
                    </Group>
                    <Popover className="react-aria-Popover w-max">
                      <Dialog className="p-4">
                        <Calendar />
                      </Dialog>
                    </Popover>
                    <FieldError />
                  </DatePicker>
                </FormField>
              </div>
            </div>

            {/* RFT SECTION */}
            <div className="space-y-4 pb-6">
              <div className="font-bold text-neutral-800 uppercase">RFT</div>

              <div className="flex items-start gap-x-4">
                <FormField name="gender_for_rft">
                  <FieldError />

                  <Select
                    className="react-aria-Select w-full"
                    placeholder="Sex"
                  >
                    <Label className="text-xs text-neutral-600">Sex</Label>
                    <RACButton className="react-aria-Button w-full rounded-sm">
                      <SelectValue className="react-aria-SelectValue text-sm" />
                      <ChevronDown />
                    </RACButton>
                    <Popover>
                      <ListBox>
                        {options?.pred_ref_genders
                          ?.filter((g) => g.list_option_for_rfts)
                          .map((g) => (
                            <ListBoxItem
                              key={g.id}
                              id={g.id}
                              textValue={g?.description ?? undefined}
                            >
                              {g.description}
                            </ListBoxItem>
                          ))}
                      </ListBox>
                    </Popover>
                  </Select>
                </FormField>
                <FormField name="ethnicity">
                  <FieldError />
                  <Select
                    className="react-aria-Select w-full"
                    placeholder="Select Ethnicity"
                  >
                    <Label className="text-xs text-neutral-600">Ethnicity</Label>
                    <RACButton className="react-aria-Button w-full rounded-sm">
                      <SelectValue className="react-aria-SelectValue text-sm" />
                      <ChevronDown />
                    </RACButton>
                    <Popover>
                      <ListBox>
                        {options?.pred_ref_ethnicities
                          ?.filter((g) => g.list_option_for_demographics)
                          .map((item) => (
                            <ListBoxItem
                              key={item.id}
                              id={item.id}
                              textValue={item?.description ?? undefined}
                            >
                              <div className="max-w-full overflow-hidden text-ellipsis whitespace-nowrap">
                                {item.description}
                              </div>
                            </ListBoxItem>
                          ))}
                      </ListBox>
                    </Popover>
                  </Select>
                </FormField>
              </div>
            </div>

            {/*ALL MRNs HEADING*/}
            <div className="space-y-4 pb-6">
              <div className="font-bold text-neutral-800 uppercase">MRNs</div>

              <FieldArray
                name="ur_numbers"
                initialItemsCount={1}
                className="grid grid-cols-[1fr_1fr_1fr_1fr_40px] gap-x-4 gap-y-4"
              >
                {({append}) => (
                  <>
                    <FieldArrayItems className="contents">
                      {({index}) => (
                        <div className="col-span-5 grid grid-cols-subgrid">
                          <TextFormField
                            name={`mrns.${index}.ur`}
                            isDisabled={index === 0}
                            defaultValue={null}
                          >
                            <div className="flex items-start gap-x-1.5">
                              <Label
                                className={clsx(
                                  'text-xs font-semibold whitespace-nowrap text-neutral-600',
                                  index === 0 ? "after:ml-0.5 after:text-red-500 after:content-['*']" : ''
                                )}
                              >
                                {index === 0 ? `Primary MRN` : 'MRN'}
                                {statusValues[index]?.status === 'error' && index !== 0 && (
                                  <span className="sr-only">(Error: {statusValues[index]?.error})</span>
                                )}
                              </Label>
                              {index === 0 && (
                                <Tooltip>
                                  <TooltipTrigger className="grow truncate text-left text-xs text-neutral-900">
                                    <Info className="h-3.5 w-3.5 text-neutral-700" />
                                  </TooltipTrigger>
                                  <TooltipContent className={index === 0 ? '' : 'hidden'}>
                                    Enter Primary MRN from Primary MRN Field Above
                                  </TooltipContent>
                                </Tooltip>
                              )}
                            </div>
                            <Input
                              placeholder="MRN"
                              className="react-aria-Input w-full rounded-sm text-neutral-800 disabled:bg-neutral-50 disabled:text-neutral-500"
                              onChange={(e) => {
                                const value = e.target.value;
                                form.setValue(`mrns.${index}.ur`, value);
                                if (index === 0) {
                                  form.setValue('ur', value);
                                }

                                // If there's a healthcare value, validate on change to provide immediate feedback
                                const healthcare = form.getValues(`mrns.${index}.healthcare`);
                                const healthService = index === 0 ? primaryHealthServiceCode : healthcare;

                                if (healthService && value) {
                                  calculateStatusAndMerge(value.toString(), healthService.toString(), index);
                                }
                              }}
                              onBlur={(e) => {
                                const ur = e.target.value;
                                const healthcare = form.getValues(`mrns.${index}.healthcare`);
                                const healthService = index === 0 ? primaryHealthServiceCode : healthcare;
                                if (healthService) {
                                  calculateStatusAndMerge(ur, healthService, index);
                                }

                                // Clear errors if the field is empty (user is starting over)
                                if (!ur) {
                                  form.clearErrors(`mrns.${index}.ur`);
                                  form.clearErrors(`mrns.${index}.healthcare`);
                                  if (index === 0) {
                                    form.clearErrors('ur');
                                  }
                                }
                              }}
                            />
                            <FieldError />
                          </TextFormField>
                          <WatchState name={`mrns.${index}.ur`}>
                            {(ur) => (
                              <SelectFormField
                                name={`mrns.${index}.healthcare`}
                                className="react-aria-Select disabled:bg-neutral-100 disabled:text-neutral-500"
                                placeholder="Select Healthcare"
                                defaultValue={index === 0 ? primaryHealthServiceCode?.toString() : undefined}
                                key={index === 0 ? primaryHealthServiceCode?.toString() : undefined}
                                isDisabled={index === 0}
                                onSelectionChange={(selected) => {
                                  const healthcare = selected as string;

                                  // Clear errors when healthcare selection changes
                                  form.clearErrors(`mrns.${index}.healthcare`);

                                  if (ur) {
                                    calculateStatusAndMerge(ur, healthcare, index);
                                  } else {
                                    // If no UR value, just clear errors
                                    form.clearErrors(`mrns.${index}.ur`);
                                    if (index === 0) {
                                      form.clearErrors('ur');
                                    }
                                  }
                                }}
                                onBlur={() => {
                                  const healthcare = form.getValues(`mrns.${index}.healthcare`);

                                  const ur = form.getValues(`mrns.${index}.ur`);

                                  // If both values are present, validate
                                  if (ur && healthcare) {
                                    calculateStatusAndMerge(ur.toString(), healthcare.toString(), index);
                                  } else {
                                    // If either value is missing, clear errors
                                    form.clearErrors(`mrns.${index}.healthcare`);
                                    form.clearErrors(`mrns.${index}.ur`);
                                    if (index === 0) {
                                      form.clearErrors('ur');
                                    }
                                  }
                                }}
                              >
                                <Label className="text-xs text-neutral-600">Healthcare</Label>
                                <RACButton
                                  className={clsx(
                                    'react-aria-Button w-40 rounded-sm disabled:text-neutral-500',
                                    index === 0 && 'cursor-not-allowed'
                                  )}
                                >
                                  <SelectValue className="react-aria-SelectValue text-sm" />
                                  <ChevronDown />
                                </RACButton>
                                <Popover className="react-aria-Popover w-auto">
                                  <ListBox items={(index === 0 ? healthServicesList : healthServices) ?? []}>
                                    {(item) => (
                                      <ListBoxItem
                                        id={item?.code?.toString() ?? undefined}
                                        textValue={item?.description ?? undefined}
                                      >
                                        <div className="max-w-full overflow-hidden text-ellipsis whitespace-nowrap">
                                          {item.description}
                                        </div>
                                      </ListBoxItem>
                                    )}
                                  </ListBox>
                                </Popover>
                                <FieldError />
                              </SelectFormField>
                            )}
                          </WatchState>

                          <TextFormField
                            name={`mrns.${index}.ur_status`}
                            defaultValue={
                              statusValues[index]?.status || (index === 0 ? 'primary' : 'other_healthservice')
                            }
                            isReadOnly={true}
                          >
                            <Label className="text-xs text-neutral-600">Status</Label>
                            <Input
                              className="react-aria-Input w-full cursor-not-allowed truncate rounded-sm bg-neutral-50 text-neutral-800"
                              value={
                                index === 0 ? 'primary' : statusValues[index]?.status || 'other_healthservice'
                              }
                            />
                          </TextFormField>

                          <TextFormField
                            name={`mrns.${index}.merge`}
                            defaultValue={statusValues[index]?.merge || '-'}
                            isReadOnly={true}
                          >
                            <Label className="text-xs text-neutral-600">Merge</Label>
                            <Input
                              className="react-aria-Input w-full cursor-not-allowed truncate rounded-sm bg-neutral-50 text-neutral-800"
                              value={statusValues[index]?.merge || '-'}
                            />
                          </TextFormField>

                          <Button
                            className={clsx('ml-auto p-0', index === 0 && 'cursor-not-allowed')}
                            slot="remove"
                            variant="plain"
                            isDisabled={index === 0}
                          >
                            <CloseRemoveIconly className="h-4 w-4 text-neutral-800" />
                          </Button>
                        </div>
                      )}
                    </FieldArrayItems>

                    <div className="col-span-5 grid grid-cols-subgrid">
                      <div className="col-start-5">
                        <Button
                          variant="plain"
                          onPress={() => {
                            const newIndex = Object.keys(statusValues).length;
                            append({ur: '', healthcare: ''}, {shouldFocus: true});
                            setStatusValues((prev) => ({
                              ...prev,
                              [newIndex]: {status: 'other_healthservice', merge: 'primary', error: null},
                            }));
                          }}
                          className="-ml-6.5 flex"
                        >
                          <Plus className="text-brand-500 h-4 w-4" />
                          Add
                        </Button>
                      </div>
                    </div>
                  </>
                )}
              </FieldArray>
            </div>
          </div>

          <div className="sticky bottom-0 z-20 flex w-full justify-end gap-4 border-t border-neutral-200 bg-white px-6 py-4">
            <Button
              type="reset"
              variant="outlined"
              className="text-brand-500 data-hovered:text-brand-500 data-focused:text-brand-500 rounded-sm border-neutral-300 font-bold"
              onPress={() => {
                resetFormAndState();
                setIsOpen(false);
              }}
            >
              Cancel
            </Button>
            <Button
              type="submit"
              className="rounded-sm font-semibold"
              isDisabled={addingPatient}
            >
              {addingPatient ? 'Adding Patient...' : 'Add Patient'}
            </Button>
          </div>
        </Form>
      </Dialog>
    </Modal>
  );
}
