import clsx from 'clsx';
import React, {Fragment, useEffect} from 'react';
import {RouterProvider} from 'react-aria-components';
import {Outlet, useHref, useMatches, useNavigate} from 'react-router';

import {useLocalStorage} from '@mantine/hooks';

import {Paths} from '@/api-types/routePaths.ts';
import {GlobalSearch} from '@/components/GlobalSearch.tsx';
import {AppSidebar} from '@/components/app-sidebar';
import ModalStateProvider from '@/components/modal-state-provider.tsx';
import {NavUser} from '@/components/nav-user';
import OnboardingTour from '@/components/OnboardingTour.tsx';
import ModuleOnboardingTours from '@/components/ModuleOnboardingTours.tsx';
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from '@/components/ui/breadcrumb';
import {SidebarInset, SidebarProvider} from '@/components/ui/sidebar';
import {apiQueryOptions} from '@/hooks/use-api-query.ts';
import {queryClient} from '@/query-client.ts';

export default function RootLayout() {
  const navigate = useNavigate();
  const [siteId] = useLocalStorage({
    key: 'rezibase:site_id',
    getInitialValueInEffect: false,
  });

  const matches = useMatches();

  let crumbs = matches
    .filter((match) => Boolean((match.handle as any)?.crumb) || (match.handle as any)?.crumbName)
    .map((match) => ({...(match.handle as any), actualPath: match.pathname}));

  const getBreadcrumb = (crumb: {
    crumbName: string | React.ReactNode;
    dynamicBreadcrumb?: React.ReactNode;
  }) => {
    return crumb.dynamicBreadcrumb || crumb.crumbName;
  };

  const activePageIndex = crumbs.length - 1;
  const isFromPatientDetail = crumbs?.some((crumb) => crumb.crumbName === 'Patient Details');
  const isFromReportDetail = crumbs?.some((crumb) => crumb.crumbName === 'Reporting Details');

  useEffect(() => {
    queryClient.ensureQueryData(apiQueryOptions(Paths.GET_FAVOURITE, {siteID: siteId}));
  }, []);

  return (
    <RouterProvider
      navigate={navigate}
      useHref={useHref}
    >
      <OnboardingTour>
        <SidebarProvider>
          <ModalStateProvider>
            <AppSidebar />
            <SidebarInset>
            <header
              className={clsx(
                'flex h-16 items-center justify-between gap-2 border-b bg-white px-6',
                !isFromPatientDetail && !isFromReportDetail && 'sticky top-0 z-10'
              )}
            >
              <div className="flex items-center gap-2">
                <Breadcrumb>
                  <BreadcrumbList>
                    {crumbs.map(
                      (
                        crumb: {
                          crumbName: string | React.ReactNode;
                          dynamicBreadcrumb?: React.ReactNode;
                          crumbPath: string;
                          actualPath: string;
                        },
                        index
                      ) => (
                        <Fragment key={index}>
                          <BreadcrumbItem className="flex items-center gap-x-2">
                            <BreadcrumbPage className="text-foreground">
                              {activePageIndex === index ? (
                                getBreadcrumb(crumb)
                              ) : (
                                <BreadcrumbLink
                                  className="hover:text-foreground text-muted-foreground cursor-pointer"
                                  to={crumb.crumbPath ?? crumb.actualPath}
                                >
                                  {getBreadcrumb(crumb)}
                                </BreadcrumbLink>
                              )}
                            </BreadcrumbPage>
                          </BreadcrumbItem>
                          {index !== activePageIndex && (
                            <BreadcrumbSeparator className="size-3.5 text-neutral-500" />
                          )}
                        </Fragment>
                      )
                    )}
                  </BreadcrumbList>
                </Breadcrumb>
              </div>

              <GlobalSearch className="absolute inset-x-0 left-1/2 w-60 -translate-x-1/2" />
              <div>
                <NavUser />
              </div>
            </header>

              <div id="onboarding-root" className="hidden" />
              <div className="flex flex-1 flex-col gap-4 bg-neutral-50 px-6 pt-6 pb-4">
                <Outlet />
              </div>
            </SidebarInset>
          </ModalStateProvider>
        </SidebarProvider>
      </OnboardingTour>
    </RouterProvider>
  );
}
